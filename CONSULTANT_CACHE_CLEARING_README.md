# Project Cache Clearing Documentation

## Overview

This documentation provides comprehensive information about ALL caching implementations and the complete cache clearing solution for the Habib application. This solution clears ALL cached data for the entire project.

## Current Caching Implementation

### Cache Keys and Patterns

The application uses Redis to cache various types of data with the following patterns:

#### 1. Consultant List Caches
- **Pattern**: `consultants:{user_id}:{language_code}:{only_interacted}:{search}`
- **Example**: `consultants:123:en,ar:False:None`
- **Expiry**: 180 seconds (3 minutes) - optimized for WebSocket real-time updates
- **Content**: Array of consultant objects with basic info and dynamic data

#### 2. Individual Consultant Caches
- **Pattern**: `consultant:{username}:{user_id}:{language_code}`
- **Example**: `consultant:john_doe:123:en`
- **Expiry**: 180 seconds (3 minutes) - optimized for WebSocket real-time updates
- **Content**: Detailed consultant object with full information

#### 3. Statistics and Analytics Caches
- **Pattern**: `stats_info_consultant_{consultant}_{period}_{date}`
- **Pattern**: `stats_info_all_{period}_{date}`
- **Pattern**: `period_stats_{start_date}_{end_date}_{labels}_{consultant_id}_{date}`
- **Example**: `stats_info_consultant_1_month_2024-01-15`
- **Example**: `period_stats_2024-01-01_2024-01-31_January_1_2024-01-15`
- **Expiry**: 86400 seconds (24 hours)
- **Content**: Statistical data about consultant performance and analytics

#### 4. Other Project Caches
- **Pattern**: `*` (catch-all for any other cached data)
- **Content**: Any other cached data used by the application

### Caching Strategy

The current implementation uses a **cache-aside** pattern:

1. **Read Path**:
   - Check Redis cache first
   - If found, update only dynamic data (status, unread messages)
   - If not found, fetch from database and cache the result

2. **Write Path**:
   - No automatic cache invalidation on data changes
   - Caches expire naturally after TTL
   - Only single-key deletion when consultant not found

## Cache Clearing Solution

### Files Created

1. **`clear_all_caches.py`** - Main cache clearing script (clears ALL project caches)
2. **`clear_consultant_caches.py`** - Legacy script (now updated for all caches)
3. **`test_cache_clearing.py`** - Test script to validate functionality
4. **Enhanced `utils/redis_client.py`** - Added pattern deletion methods

### New Redis Client Methods

Added to `utils/redis_client.py`:

```python
async def keys(self, pattern: str) -> list:
    """Get all keys matching a pattern."""

async def delete_pattern(self, pattern: str) -> int:
    """Delete all keys matching a pattern."""
```

### Cache Clearing Script Features

- **Dry Run Mode**: Preview what would be deleted without actual deletion
- **Verbose Mode**: Detailed logging of operations
- **Pattern-Based Clearing**: Efficiently clears all related cache keys
- **Verification**: Confirms successful cache clearing
- **Error Handling**: Robust error handling and logging

## Usage Instructions

### 1. Dry Run (Recommended First)

```bash
# Preview what would be cleared
python clear_all_caches.py --dry-run --verbose
```

### 2. Production Cache Clearing

```bash
# Clear all project caches (with confirmation prompt)
python clear_all_caches.py --verbose

# Clear all project caches (skip confirmation - use with caution)
python clear_all_caches.py --verbose --confirm
```

### 3. Testing the Script

```bash
# Run the test suite (requires Redis connection)
python test_cache_clearing.py
```

## Cache Patterns Cleared

The script clears the following patterns:

1. `consultants:*` - All consultant list caches
2. `consultant:*` - All individual consultant caches
3. `stats_info_consultant_*` - Consultant-specific stats caches
4. `stats_info_all_*` - General stats caches
5. `period_stats_*` - Period-based statistics caches
6. `*` - All remaining cache keys (catch-all pattern)

⚠️ **WARNING**: The catch-all pattern `*` will delete ALL keys in the Redis database. Use with caution in shared Redis environments.

## Production Deployment Steps

### Step 1: Backup (Optional)
If you want to backup current cache data before clearing:

```bash
# Use Redis CLI to backup specific patterns
redis-cli --scan --pattern "consultants:*" > consultant_cache_backup.txt
```

### Step 2: Test in Staging
1. Deploy the enhanced `redis_client.py` to staging
2. Run the cache clearing script with `--dry-run` first
3. Run actual clearing and verify application behavior

### Step 3: Production Deployment
1. Deploy the enhanced `redis_client.py` to production
2. Run the cache clearing script during low-traffic period
3. Monitor application performance after clearing

## Expected Impact

### Immediate Effects
- All cached consultant data will be cleared
- Next requests will fetch fresh data from database
- Temporary increase in database load and response times
- All users will see up-to-date consultant information

### Performance Considerations
- First requests after clearing will be slower (cache miss)
- Database load will increase temporarily
- Cache will rebuild naturally as users make requests
- Normal performance will resume within 1-2 hours

## Monitoring and Verification

### Before Running
```bash
# Check current cache usage
redis-cli info memory
redis-cli --scan --pattern "consultants:*" | wc -l
```

### After Running
```bash
# Verify caches are cleared
redis-cli --scan --pattern "consultants:*" | wc -l
redis-cli --scan --pattern "consultant:*" | wc -l
```

### Application Monitoring
- Monitor API response times for consultant endpoints
- Check database query performance
- Verify consultant data freshness in application

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check Redis server status
   - Verify connection parameters in settings
   - Ensure network connectivity

2. **Partial Cache Clearing**
   - Check Redis memory limits
   - Verify pattern matching is working
   - Run verification step

3. **Performance Impact**
   - Monitor database load
   - Consider running during off-peak hours
   - Scale database resources if needed

### Recovery Steps

If issues occur after cache clearing:

1. **Immediate**: Restart application servers to clear any in-memory state
2. **Short-term**: Monitor database performance and scale if needed
3. **Long-term**: Consider implementing proper cache invalidation on data changes

## Future Improvements

### Recommended Enhancements

1. **Automatic Cache Invalidation**
   - Invalidate caches when consultant data changes
   - Use database triggers or application-level hooks

2. **Selective Cache Clearing**
   - Clear only specific consultant caches
   - Clear caches for specific users or languages

3. **Cache Warming**
   - Pre-populate caches after clearing
   - Background jobs to rebuild important caches

4. **Monitoring Dashboard**
   - Track cache hit/miss ratios
   - Monitor cache memory usage
   - Alert on cache-related issues

## Security Considerations

- The script requires Redis access permissions
- Run with appropriate user privileges
- Consider audit logging for cache operations
- Restrict access to production Redis instances

## Support

For issues or questions:
1. Check the logs generated by the script
2. Verify Redis connectivity and permissions
3. Test in staging environment first
4. Monitor application behavior after clearing
