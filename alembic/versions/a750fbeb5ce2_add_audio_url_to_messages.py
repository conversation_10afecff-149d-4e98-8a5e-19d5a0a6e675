"""Add audio_url to messages

Revision ID: a750fbeb5ce2
Revises: 59bd891b633b
Create Date: 2024-05-30 18:18:34.745135

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a750fbeb5ce2'
down_revision: Union[str, None] = '59bd891b633b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('audio_url', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messages', 'audio_url')
    # ### end Alembic commands ###
