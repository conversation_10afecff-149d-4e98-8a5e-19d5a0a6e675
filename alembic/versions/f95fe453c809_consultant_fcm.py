"""consultant fcm

Revision ID: f95fe453c809
Revises: a13ebd74b3c4
Create Date: 2024-01-29 12:34:07.820972

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f95fe453c809'
down_revision: Union[str, None] = 'a13ebd74b3c4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('consultants', sa.Column('fcm', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('consultants', 'fcm')
    # ### end Alembic commands ###
