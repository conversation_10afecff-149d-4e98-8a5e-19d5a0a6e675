"""add session_duration, video_call_cost, and voice_call_cost to Consultant

Revision ID: c037cf323c92
Revises: a750fbeb5ce2
Create Date: 2024-06-07 14:10:58.336044

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c037cf323c92'
down_revision: Union[str, None] = 'a750fbeb5ce2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('consultants', sa.Column('session_duration', sa.Integer(), nullable=True))
    op.add_column('consultants', sa.Column('video_call_cost', sa.Integer(), nullable=True))
    op.add_column('consultants', sa.Column('voice_call_cost', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('consultants', 'voice_call_cost')
    op.drop_column('consultants', 'video_call_cost')
    op.drop_column('consultants', 'session_duration')
    # ### end Alembic commands ###
