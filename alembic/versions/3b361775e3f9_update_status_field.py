"""update status field

Revision ID: 3b361775e3f9
Revises: c200581eb961
Create Date: 2024-06-24 13:45:07.786363

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3b361775e3f9'
down_revision: Union[str, None] = 'c200581eb961'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('consultants', 'status')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('consultants', sa.Column('status', sa.BOOLEAN(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
