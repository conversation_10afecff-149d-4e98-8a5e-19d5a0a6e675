"""Add CALL

Revision ID: 1d2402b1fe0a
Revises: 1c21fe919ecc
Create Date: 2024-06-07 17:27:11.749752

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1d2402b1fe0a'
down_revision: Union[str, None] = '1c21fe919ecc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('calls',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('consultant_id', sa.Integer(), nullable=True),
    sa.Column('client_id', sa.Integer(), nullable=True),
    sa.Column('start_time', sa.DateTime(), nullable=False),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.Column('call_type', sa.String(), nullable=False),
    sa.Column('cost', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['client_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['consultant_id'], ['consultants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_calls_id'), 'calls', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_calls_id'), table_name='calls')
    op.drop_table('calls')
    # ### end Alembic commands ###
