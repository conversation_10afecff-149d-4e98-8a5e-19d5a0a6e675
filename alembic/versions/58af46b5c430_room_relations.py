"""room relations

Revision ID: 58af46b5c430
Revises: 16b002c7883d
Create Date: 2023-09-23 18:29:02.600336

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '58af46b5c430'
down_revision: Union[str, None] = '16b002c7883d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('room_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'messages', 'rooms', ['room_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.drop_column('messages', 'room_id')
    # ### end Alembic commands ###
