"""room type

Revision ID: f782e84dd1f6
Revises: 4b0e276f7011
Create Date: 2024-01-22 16:11:03.950943

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f782e84dd1f6'
down_revision: Union[str, None] = '4b0e276f7011'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity_logs', 'duration',
               existing_type=sa.BOOLEAN(),
               type_=sa.String(),
               existing_nullable=True)
    op.add_column('rooms', sa.Column('room_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rooms', 'room_type')
    op.alter_column('activity_logs', 'duration',
               existing_type=sa.String(),
               type_=sa.BOOLEAN(),
               existing_nullable=True)
    # ### end Alembic commands ###
