"""add translation field

Revision ID: 8d4f0119f2a2
Revises: 0264fb330877
Create Date: 2023-10-02 11:06:30.330126

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8d4f0119f2a2'
down_revision: Union[str, None] = '0264fb330877'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('start_at', sa.DateTime(), nullable=True),
    sa.Column('end_at', sa.DateTime(), nullable=True),
    sa.Column('duration', sa.<PERSON>(), nullable=True),
    sa.Column('messages_count', sa.<PERSON>(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_activity_logs_id'), 'activity_logs', ['id'], unique=False)
    op.add_column('consultants', sa.Column('name_translation', sa.JSON(), nullable=True))
    op.add_column('consultants', sa.Column('topics_translation', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('consultants', 'topics_translation')
    op.drop_column('consultants', 'name_translation')
    op.drop_index(op.f('ix_activity_logs_id'), table_name='activity_logs')
    op.drop_table('activity_logs')
    # ### end Alembic commands ###
