"""files relations

Revision ID: 0264fb330877
Revises: 58af46b5c430
Create Date: 2023-09-23 18:30:25.971044

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0264fb330877'
down_revision: Union[str, None] = '58af46b5c430'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('user_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'files', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'files', type_='foreignkey')
    op.drop_column('files', 'user_id')
    # ### end Alembic commands ###
