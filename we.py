from models import *
import json

tr = {
    '<EMAIL>': {
        "fa": "محمد امین قربانی",
        "ar": "محمد امین قربانی"
    },
    "<EMAIL>": {
        "fa": "آستان قدس رضوی",
        "ar": "آستان قدس رضوی"
    }
}

{"fa": "آستان قدس رضوی","ar": "آستان قدس رضوی"}


def find_row(_id, table):
    for i in json.load(open(f"db/{table}.json")):
        if i['_id']['$oid'] == _id:
            return i


def import_db():
    admin_data = json.load(open("db/admin.json"))
    for admin_row in admin_data:
        admin_obj = Admin(
            avatar_url=admin_row['avatar_url'],
            created_at=admin_row['created_at']['$date'],
            password=admin_row['password'],
            token=admin_row['token'],
            username=admin_row['username'],
        )
        db.add(admin_obj)
        db.commit()

    consultant_data = json.load(open("db/consultant.json"))
    for consultant_row in consultant_data:
        consultant_row.pop('_id')
        consultant_row.pop('updated_at', None)
        consultant_row['contact_type'] = ",".join(consultant_row['contact_type'])
        consultant_row['languages'] = ",".join(consultant_row['languages'])
        consultant_row['topics'] = ",".join(consultant_row['topics'])

        consultant_row['created_at'] = consultant_row['created_at']['$date']
        consultant_obj = Consultant(**consultant_row)
        db.add(consultant_obj)
        db.commit()

    user_data = json.load(open("db/user.json"))
    for user_row in user_data:
        user_row['username'] = user_row.pop('_id')
        user_row['banned_at'] = user_row['banned_at']['$date'] if user_row['banned_at'] else None
        user_row['last_update'] = user_row['last_update']['$date'] if user_row.get('last_update') else None
        user_obj = User(**user_row)
        db.add(user_obj)
        db.commit()

    topic_data = json.load(open("db/topic.json"))
    for topic_row in topic_data:
        topic_obj = Topic(title=topic_row['t'])
        db.add(topic_obj)
        db.commit()

    db.commit()
    room_data = json.load(open("db/room.json"))
    for room_row in room_data:
        messages = room_row['messages']
        client_id = db.query(User).filter(User.username == room_row['cl']).first().id
        consultant_row = find_row(room_row['c']['$oid'], "consultant")
        consultant_id = db.query(Consultant).filter(Consultant.username == consultant_row['username']).first().id
        room_obj = Room(
            is_public=room_row['ip'],
            created_at=room_row['ca']['$date'],
            client_id=client_id,
            consultant_id=consultant_id,
            status=room_row['st'],
        )

        db.add(room_obj)
        db.commit()
        db.refresh(room_obj)

        for message in messages:
            by_user_id = client_id if message['byt'] == "User" else consultant_id
            if not message.get('ct'):
                print(message)
                continue

            msg_obj = Message(
                room_id=room_obj.id,
                mime_type=message['mt'],
                content=message['ct'],
                at_time=message['at']['$date'],
                by_user_type=message['byt'],
                has_read=message['hr'],
                by_user_id=by_user_id,
            )
            db.add(msg_obj)
            db.commit()


import_db()
