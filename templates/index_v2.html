<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/html">

<head>
    <script
            src="https://code.jquery.com/jquery-3.4.1.min.js"
            integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo="
            crossorigin="anonymous"></script>

    <title>Chat V2</title>
    <style>
        html, body {
            height: 100%;
            background: #1a1717;
            outline: none;
            margin: 0;
            padding: 0;
            border: 0;
        }

        * {
            box-sizing: border-box;
        }

        .content {
            width: 100%;
            margin: auto;
            height: 100%;
            border: 1px dashed #414241;
            color: wheat;
        }

        #messageText {
            height: 200px;
            width: 450px;
            border: 1px dashed gray;
            background: #1a1717;
            color: wheat;
        }


        .box {
            padding: 5px;
            margin-bottom: 12px;
            border: 1px solid gray;
        }

        #messages {
            border-top: 1px solid gray;
        }

        aside {
            margin-top: 30px;
            width: 30%;
            float: left;
        }

        div#messages {
            width: 67%;
            float: right;
        }

    </style>
</head>
<body>
<div class="content">
    <h1 style="text-align: center">WebSocket Chat V2</h1>
    <aside>
        <div class="box">
            <select name="token" id="">
                <option value="consultant:06b0516cee0256b4c39372ed0d693004ccae397e">consultant</option>
                <option value="119ff3737d731c7a0ed5d5ca45a9516f920add81">client</option>
            </select>
            <input type="text" name="token" placeholder="Token">
            <button id="connectSocket">
                Connect To Socket
            </button>
        </div>
        <div class="box">
            <input type="text" name="language_code" placeholder="Language Code. en" value="en">
            <input type="text" name="timezone" placeholder="Timezone. Asia/Tehran" value="Asia/Tehran">
            <button id="getConsultants">
                getConsultants
            </button>
        </div>

        <div class="box">
            <input type="text" name="room-2" placeholder="room id">
            <button id="get-history">
                Get History
            </button>
        </div>

        <div class="box">
            <br>
            <input name="send-message" type="text" placeholder="message"><br>
            <input name="room-1" type="text" placeholder="room id">
            <button id="send-message">
                Send Message
            </button>
        </div>

        <div class="box vm-request">
            <br>
            <input name="room_id" type="text" placeholder="room id">
            <select name="chat_type" id="">
                <option value="voice">voice</option>
                <option value="video">video</option>
            </select><br>
            <button>
                VM Request (video/voice)
            </button>
        </div>

        <div class="box">
            <br>
            <input name="get-rooms" type="text" placeholder="Consultants username">
            <input name="room-page" type="text" placeholder="page">
            <button id="get-rooms"> Get Rooms</button>
        </div>

        <div class="box">
            <br>
            <input name="c-username" type="text" placeholder="Consultants username"><br>
            <label for="">
                <select name="c-room_type" id="">
                    <option value="chat">RoomType: chat</option>
                    <option value="voice">RoomType: voice</option>
                    <option value="video">RoomType: video</option>
                </select>
            </label>
            <button id="start-room"> Start Room</button>
        </div>

        <div class="box">
            <br>
            <input name="close-room" type="text" placeholder="Room ID"><br>
            <input name="reason" type="text" placeholder="Reason"><br>
            <button id="close-room"> Close Room</button>
            <button id="open-room"> Open Room</button>
        </div>
        <div class="box">
            <br>
            <input name="read-msg-room" type="text" placeholder="Room ID"><br>
            <button id="read-msg"> Read All Room Message</button>
        </div>
        <div class="box">
            <br>
            <button id="getStats">GetStats</button>
        </div>

        <div class="box">
            <br>
            <button id="getCategories">Get Categories</button>
        </div>
        <div class="box">
            <br>
            <select name="status" id="">
                <option value="true">On</option>
                <option value="false">Off</option>
            </select>
            <button id="changeStatus">Change Status</button>
        </div>

        <div class="box update-message">
            <br>
            <input name="message_id" type="text" placeholder="message id"><br>
            <input name="text" type="text" placeholder="new text"><br>
            <button id="UpdateMessage">Update Message</button>
        </div>

        <div class="box delete-message">
            <br>
            <input name="message_id" type="text" placeholder="message id"><br>
            <button class="submit">Delete Message</button>
        </div>
        {#<div class="box">
            <br>
            <video id="localVideo" autoplay></video>
            <video id="remoteVideo" autoplay></video>
            <button onclick="startVideoCall()">Start Video Call</button>
        </div>#}

    </aside>


    <div id='messages'></div>
</div>

<script>
    let base_url = "{{ socket_protocol }}://{{ host }}/ws/?version=2&t="

    function isOpen(wsc) {
        return wsc.readyState === wsc.OPEN
    }

    function sendMsg(wsc, data) {
        if (!isOpen(wsc)) {
            console.log('websocket is not ready yet')
            return
        }
        wsc.send(JSON.stringify(data))
    }

    $('#connectSocket').click(function () {
        let token = $('input[name="token"]').val() || $('select[name="token"]').val()

        try {
            window.ws.close()

        } catch (e) {
            console.log(e)
        }


        window.ws = new WebSocket(base_url + token);
        ws.onmessage = function (event) {
            try {

                let data = JSON.parse(event.data)
                console.log(data)
                $('#messages').html(`<pre>${JSON.stringify(data, undefined, 2)}</pre>`)

            } catch (erro) {
                $('#messages').html(`<pre>${event.data}</pre>`)
            }
        };


    })

    $("#get-history").click(function () {
        let data = {
            "room": $("input[name=room-2]").val(),
            "action": "getHistory",
        }
        if (!isOpen(ws)) {
            console.log('websocket is not ready yet')
            return
        }
        sendMsg(ws, data)
    })

    $("#getConsultants").click(function () {
        let data = {
            "action": "getConsultants",
            "language_code": $('input[name="language_code"]').val(),
            "timezone": $('input[name="timezone"]').val(),
        }
        sendMsg(ws, data)
    })

    $("#get-rooms").click(function () {
        let data = {
            "action": "getRooms",
            "consultant": $("input[name=get-rooms]").val(),
            'page': $('input[name=room-page]').val() || 1,
        }
        sendMsg(ws, data)
    })

    $("#start-room").click(function () {
        let data = {
            "action": "startRoom",
            "consultant": $("input[name=c-username]").val(),
            "room_type": $("select[name=c-room_type]").val(),
        }
        sendMsg(ws, data)
    })

    $("#close-room").click(function () {
        let data = {
            "action": "closeRoom",
            "room_id": $('input[name=close-room]').val(),
            "reason": $('input[name=reason]').val(),
        }
        sendMsg(ws, data)
    })
    $("#open-room").click(function () {
        let data = {
            "action": "openRoom",
            "room_id": $('input[name=close-room]').val()
        }
        sendMsg(ws, data)
    })

    $("#read-msg").click(function () {
        let data = {
            "action": "readMsg",
            "room_id": $('input[name=read-msg-room]').val(),
        }
        sendMsg(ws, data)
    })

    $("#send-message").click(function () {
        let data = {
            "room": $("input[name=room-1]").val(),
            "action": "sendMessage",
            "text": $("input[name=send-message]").val()
        }
        sendMsg(ws, data)
    })

    $("#send-message-s").click(function () {
        let data = {
            "room": $("input[name=room-3]").val(),
            "action": "ctest",
        }
        sendMsg(ws, data)
    })

    $("#getStats").click(function () {
        let data = {
            "action": "getStats",
        }
        sendMsg(ws, data)
    })

    $("#getCategories").click(function () {
        let data = {
            "action": "getCategories",
        }
        sendMsg(ws, data)
    })

    $("#changeStatus").click(function () {
        let data = {
            "action": "changeStatus",
            "status": $('select[name=status]').val(),
        }
        sendMsg(ws, data)
    })

    $(".update-message #UpdateMessage").click(function () {
        let data = {
            "action": "updateMessage",
            "message_id": $('.update-message input[name=message_id]').val(),
            "text": $('.update-message input[name=text]').val(),
        }
        sendMsg(ws, data)
    })

    $(".delete-message .submit").click(function () {
        let data = {
            "action": "deleteMessage",
            "message_id": $('.delete-message input[name=message_id]').val(),
        }
        sendMsg(ws, data)
    })


    let localStream;
    let peerConnection;

    async function startVideoCall() {
        try {
            localStream = await navigator.mediaDevices.getUserMedia({video: true, audio: true});
            document.getElementById('localVideo').srcObject = localStream;
            const configuration = {
                iceServers: [
                    {urls: "stun:stun.l.google.com:19302"},
                    {urls: "turn:your-turn-server.com", username: "your-username", credential: "your-password"}
                ]
            };

            peerConnection = new RTCPeerConnection();
            localStream.getTracks().forEach(track => {
                peerConnection.addTrack(track, localStream)
                console.log(track, 'on track');
            });

            peerConnection.onicecandidate = event => {
                if (event.candidate) {
                    sendMsg(window.ws, {'ice': event.candidate});
                }
            };

            peerConnection.ontrack = event => {
                document.getElementById('remoteVideo').srcObject = event.streams[0];
                console.log(event.streams[0], 'on track');
            };

            const offer = await peerConnection.createOffer();
            await peerConnection.setLocalDescription(offer);
            sendMsg(window.ws, {'offer': offer});

        } catch (error) {
            console.error('Error starting video call:', error);
        }
    }

    // $("#send-video").click(function () {
    //     uploadFile("send-video", function (f) {
    //         let data = {
    //             "chat_type": "team",
    //             "chat_id": room,
    //             "action": "sendVideo",
    //             "video": f['result'],
    //         }
    //         ws.send(JSON.stringify(data))
    //     })
    // })

    // $("#send-photo").click(function () {
    //     uploadFile("send-photo", function (f) {
    //         let data = {
    //             "chat_type": "team",
    //             "chat_id": room,
    //             "action": "sendPhoto",
    //             "photo": f['result'],
    //             'preview': 'pEHV6nWB2yk8$NxujFpyoJadR*=ss:I[.7kCMdnjx]S2NHS#M|%1%2ENRis9Sis.slNHW:WBxZogaekBW;ofo0Rk',
    //         }
    //         ws.send(JSON.stringify(data))
    //     })
    // })
    //
    // $("#send-document").click(function () {
    //     uploadFile("send-document", function (f) {
    //         let data = {
    //             "chat_type": "team",
    //             "chat_id": room,
    //             "action": "sendDocument",
    //             "document": f['result'],
    //         }
    //         ws.send(JSON.stringify(data))
    //     })
    // })
    //
    // function uploadFile(fileINPUT = "file", onsuccess) {
    //     let file = $(`input[name=${fileINPUT}]`)[0].files[0]
    //     let formData = new FormData();
    //     formData.append('file', file);
    //     formData.append('token', user);
    //     $.ajax({
    //         url: '/upload/',
    //         type: 'POST',
    //         data: formData,
    //         processData: false,  // tell jQuery not to process the data
    //         contentType: false,  // tell jQuery not to set contentType
    //         success: function (data) {
    //             onsuccess(data)
    //         },
    //         error: function (data) {
    //             alert(data)
    //         }
    //     });
    // }


</script>

</body>
</html>
