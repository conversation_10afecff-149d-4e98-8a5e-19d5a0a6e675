<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/html">

<head>
    <script
            src="https://code.jquery.com/jquery-3.4.1.min.js"
            integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo="
            crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <title>Chat V3</title>
    <style>
        html, body {
            height: 100%;
            background: #1a1717;
            outline: none;
            margin: 0;
            padding: 0;
            border: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        * {
            box-sizing: border-box;
        }

        .content {
            width: 100%;
            margin: auto;
            height: 100%;
            border: 1px dashed #414241;
            color: wheat;
            display: flex;
            flex-direction: column;
        }

        #messageText {
            height: 200px;
            width: 450px;
            border: 1px dashed gray;
            background: #1a1717;
            color: wheat;
        }

        .box {
            padding: 15px;
            margin-bottom: 12px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #222;
            transition: all 0.3s ease;
        }

        .box:hover {
            border-color: #555;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
        }

        #messages {
            border-top: 1px solid gray;
            padding: 15px;
            overflow: auto;
            background-color: #1d1d1d;
            border-radius: 5px;
        }

        aside {
            margin-top: 20px;
            width: 30%;
            float: left;
            padding: 0 15px;
            overflow-y: auto;
            max-height: calc(100vh - 100px);
            position: relative;
        }

        div#messages {
            width: 67%;
            float: right;
            margin-top: 20px;
            margin-right: 15px;
            height: calc(100vh - 100px);
        }

        .main-container {
            display: flex;
            width: 100%;
        }

        input, select, button {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            border: 1px solid #444;
            background-color: #333;
            color: #ddd;
            font-size: 14px;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #666;
        }

        button {
            background-color: #2c3e50;
            color: white;
            cursor: pointer;
            transition: background-color 0.3s;
            border: none;
        }

        button:hover {
            background-color: #34495e;
        }

        .connection-box {
            background-color: #2c3e50;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            text-align: center;
        }

        .menu-container {
            margin-bottom: 20px;
        }

        .menu-tabs {
            display: flex;
            flex-wrap: wrap;
            border-bottom: 1px solid #444;
            margin-bottom: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
            background-color: #1a1717;
            padding-top: 10px;
        }

        .menu-tab {
            padding: 10px 15px;
            cursor: pointer;
            background-color: #222;
            border: 1px solid #444;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
            transition: all 0.3s;
        }

        .menu-tab:hover {
            background-color: #333;
        }

        .menu-tab.active {
            background-color: #2c3e50;
            border-color: #2c3e50;
            color: white;
        }

        .menu-content {
            display: none;
            overflow-y: auto;
            padding-top: 5px;
        }

        .menu-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #bbb;
        }

        h2 {
            color: #3498db;
            border-bottom: 1px solid #3498db;
            padding-bottom: 10px;
            margin-top: 0;
        }

        .icon {
            margin-right: 8px;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #222;
        }

        ::-webkit-scrollbar-thumb {
            background: #444;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* Modal Styles */
        .modal-content {
            background-color: #222;
            color: #ddd;
            border: 1px solid #444;
        }
        
        .modal-header {
            border-bottom: 1px solid #444;
        }
        
        .modal-footer {
            border-top: 1px solid #444;
        }
        
        .modal-title {
            color: #3498db;
        }
        
        .close {
            color: #ddd;
        }
        
        .close:hover {
            color: white;
        }
        
        .json-editor {
            width: 100%;
            height: 300px;
            background-color: #1a1717;
            color: #ddd;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            resize: vertical;
        }
        
        .docs-btn {
            margin-left: 10px;
            background-color: #3498db;
        }
        
        .docs-btn:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
<div class="content">
    <h1 style="text-align: center">WebSocket Chat Version 3</h1>
    
    <!-- Connection Box - Outside of menus -->
    <div class="connection-box">
        <select name="token" id="token-select">
            <option value="consultant:06b0516cee0256b4c39372ed0d693004ccae397e">consultant</option>
            <option value="119ff3737d731c7a0ed5d5ca45a9516f920add81">client</option>
            <option>admin</option>
        </select>
        <input type="text" name="token" placeholder="Token">
        <button id="connectSocket">
            <i class="fas fa-plug icon"></i>Connect To Socket
        </button>
        <button id="connectDocs" class="docs-btn">
            <i class="fas fa-file-alt icon"></i>
        </button>
    </div>
    
    <!-- Modal for Documentation -->
    <div class="modal fade" id="docsModal" tabindex="-1" aria-labelledby="docsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="docsModalLabel">Connect to Socket Documentation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>Input Parameters:</h6>
                    <textarea id="connectJsonEditor" class="json-editor" readonly></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <aside>
            <!-- Menu Tabs -->
            <div class="menu-container">
                <div class="menu-tabs">
                    <div class="menu-tab active" data-tab="consultants">
                        <i class="fas fa-user-tie icon"></i>Consultants
                    </div>
                    <div class="menu-tab" data-tab="rooms">
                        <i class="fas fa-comments icon"></i>Rooms
                    </div>
                    <div class="menu-tab" data-tab="messages">
                        <i class="fas fa-envelope icon"></i>Messages
                    </div>
                    <div class="menu-tab" data-tab="calls">
                        <i class="fas fa-phone icon"></i>Calls
                    </div>
                    <div class="menu-tab" data-tab="reservations">
                        <i class="fas fa-calendar-alt icon"></i>Reservations
                    </div>
                    <div class="menu-tab" data-tab="subscriptions">
                        <i class="fas fa-credit-card icon"></i>Subscriptions
                    </div>
                    <div class="menu-tab" data-tab="streams">
                        <i class="fas fa-video icon"></i>Streams
                    </div>
                    <div class="menu-tab" data-tab="settings">
                        <i class="fas fa-cog icon"></i>Settings
                    </div>
                </div>
                
                <!-- Menu Contents -->
                
                <!-- Consultants Menu -->
                <div class="menu-content active" id="consultants-menu">
                    <h2><i class="fas fa-user-tie icon"></i>Consultants Management</h2>
                    
                    <div class="box">
                        <h3>Get Consultants List</h3>
                        <div class="form-group">
                            <label>Language:</label>
                            <input type="text" name="language_code" placeholder="Language Code. en" value="en">
                        </div>
                        <div class="form-group">
                            <label>Timezone:</label>
                            <input type="text" name="timezone" placeholder="Timezone. Asia/Tehran" value="Asia/Tehran">
                        </div>
                        <div class="form-group">
                            <label>Only Interacted:</label>
                            <input type="text" name="only_interacted" placeholder="args<bool>: True, False" value="False">
                        </div>
                        <div class="form-group">
                            <label>Search:</label>
                            <input type="text" name="search_c" placeholder="args<str>: fullname or username">
                        </div>
                        <button id="getConsultants">
                            <i class="fas fa-search icon"></i>Get Consultants List
                        </button>
                        <button id="getConsultantsDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Get Consultant Info</h3>
                        <div class="form-group">
                            <label>Consultant Username:</label>
                            <input type="text" name="consultant_username" placeholder="Consultant Username" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>Language:</label>
                            <input type="text" name="consultant_language_code" placeholder="Language Code. en" value="en">
                        </div>
                        <button id="getConsultant">
                            <i class="fas fa-user icon"></i>Get Consultant Info
                        </button>
                        <button id="getConsultantInfoDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Change Status</h3>
                        <div class="form-group">
                            <label>Status:</label>
                            <select name="status" id="status">
                                <option value="online">Online</option>
                                <option value="offline">Offline</option>
                                <option value="busy">Busy</option>
                            </select>
                        </div>
                        <button id="changeStatus">
                            <i class="fas fa-toggle-on icon"></i>Change Status
                        </button>
                        <button id="changeStatusDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Get Dynamic Consultants List</h3>
                        <div class="form-group">
                            <label>Language:</label>
                            <input type="text" name="dynamic_language_code" placeholder="Language Code. en" value="en">
                        </div>
                        <div class="form-group">
                            <label>Timezone:</label>
                            <input type="text" name="dynamic_timezone" placeholder="Timezone. Asia/Tehran" value="Asia/Tehran">
                        </div>
                        <div class="form-group">
                            <label>Only Interacted:</label>
                            <input type="text" name="dynamic_only_interacted" placeholder="args<bool>: True, False" value="False">
                        </div>
                        <div class="form-group">
                            <label>Search:</label>
                            <input type="text" name="dynamic_search" placeholder="args<str>: fullname or username">
                        </div>
                        <button id="getDynamicConsultants">
                            <i class="fas fa-bolt icon"></i>Get Dynamic Consultants
                        </button>
                        <button id="getDynamicConsultantsDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Rooms Menu -->
                <div class="menu-content" id="rooms-menu">
                    <h2><i class="fas fa-comments icon"></i>Rooms Management</h2>
                    
                    <div class="box">
                        <h3>Get Rooms</h3>
                        <div class="form-group">
                            <label>Consultant Username:</label>
                            <input name="get-rooms" type="text" placeholder="Consultants username">
                        </div>
                        <div class="form-group">
                            <label>Page:</label>
                            <input name="room-page" type="text" placeholder="page">
                        </div>
                        <div class="form-group">
                            <label>Per Page:</label>
                            <input name="room-per-page" type="text" placeholder="per_page">
                        </div>
                        <div class="form-group">
                            <label>Date Filter:</label>
                            <input name="room-filter-data" type="text" placeholder="Filter Date">
                        </div>
                        <div class="form-group">
                            <label>Name Search:</label>
                            <input type="text" name="room_search" placeholder="args<str>: Search Names">
                        </div>
                        <div class="form-group">
                            <label>Unread Messages:</label>
                            <select name="unread" id="unread" class="form-control">
                                <option value="false">No</option>
                                <option value="true">Yes</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Not Replied:</label>
                            <select name="not_replied" id="not_replied" class="form-control">
                                <option value="false">No</option>
                                <option value="true">Yes</option>
                            </select>
                        </div>
                        <button id="get-rooms">
                            <i class="fas fa-search icon"></i>Get Rooms
                        </button>
                        <button id="getRoomsDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Start New Room</h3>
                        <div class="form-group">
                            <label>Consultant Username:</label>
                            <input name="c-username" type="text" placeholder="Consultants username">
                        </div>
                        <button id="start-room">
                            <i class="fas fa-plus-circle icon"></i>Start Room
                        </button>
                        <button id="startRoomDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Room Management</h3>
                        <div class="form-group">
                            <label>Room ID:</label>
                            <input name="close-room" type="text" placeholder="Room ID">
                        </div>
                        <div class="form-group">
                            <label>Close Reason:</label>
                            <input name="reason" type="text" placeholder="Reason">
                        </div>
                        <div class="form-group">
                            <button id="close-room">
                                <i class="fas fa-lock icon"></i>Close Room
                            </button>
                            <button id="open-room">
                                <i class="fas fa-lock-open icon"></i>Open Room
                            </button>
                            <button id="roomManagementDocs" class="docs-btn">
                                <i class="fas fa-file-alt icon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="box">
                        <h3>Read All Room Messages</h3>
                        <div class="form-group">
                            <label>Room ID:</label>
                            <input name="read-msg-room" type="text" placeholder="Room ID">
                        </div>
                        <button id="read-msg">
                            <i class="fas fa-check-double icon"></i>Read All Messages
                        </button>
                        <button id="readMsgDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Get History</h3>
                        <div class="form-group">
                            <label>Room ID:</label>
                            <input type="text" name="room-2" placeholder="room id">
                        </div>
                        <button id="get-history">
                            <i class="fas fa-history icon"></i>Get History
                        </button>
                        <button id="getHistoryDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Messages Menu -->
                <div class="menu-content" id="messages-menu">
                    <h2><i class="fas fa-envelope icon"></i>Messages Management</h2>
                    
                    <div class="box">
                        <h3>Send Message</h3>
                        <div class="form-group">
                            <label>Message Text:</label>
                            <input name="send-message" type="text" placeholder="message">
                        </div>
                        <div class="form-group">
                            <label>Room ID:</label>
                            <input name="room-1" type="text" placeholder="room id">
                        </div>
                        <div class="form-group">
                            <label>Additional Payload:</label>
                            <input name="payloads" type="text" placeholder="payloads">
                        </div>
                        <button id="send-message">
                            <i class="fas fa-paper-plane icon"></i>Send Message
                        </button>
                        <button id="sendMessageDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Edit Message</h3>
                        <div class="form-group">
                            <label>Message ID:</label>
                            <input name="message_id" type="text" placeholder="message id" class="update-message-id">
                        </div>
                        <div class="form-group">
                            <label>New Text:</label>
                            <input name="text" type="text" placeholder="new text" class="update-message-text">
                        </div>
                        <button id="UpdateMessage">
                            <i class="fas fa-edit icon"></i>Update Message
                        </button>
                        <button id="updateMessageDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Delete Message</h3>
                        <div class="form-group">
                            <label>Message ID:</label>
                            <input name="message_id" type="text" placeholder="message id" class="delete-message-id">
                        </div>
                        <button class="delete-message-btn">
                            <i class="fas fa-trash icon"></i>Delete Message
                        </button>
                        <button id="deleteMessageDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Send Audio Message</h3>
                        <div class="form-group">
                            <label>Room ID:</label>
                            <input name="audio-room" type="text" placeholder="room id">
                        </div>
                        <div class="form-group">
                            <label>Audio File:</label>
                            <input name="audio-file" type="file" accept="audio/*">
                        </div>
                        <button id="send-audio-message">
                            <i class="fas fa-microphone icon"></i>Send Audio Message
                        </button>
                        <button id="sendAudioMessageDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Calls Menu -->
                <div class="menu-content" id="calls-menu">
                    <h2><i class="fas fa-phone icon"></i>Calls Management</h2>
                    
                    <div class="box">
                        <h3>Voice/Video Call Request</h3>
                        <div class="form-group">
                            <label>Consultant Username:</label>
                            <input name="consultant" type="text" placeholder="Consultant Username">
                        </div>
                        <div class="form-group">
                            <label>Call Type:</label>
                            <select name="chat_type" id="chat-type">
                                <option value="voice">Voice</option>
                                <option value="video">Video</option>
                            </select>
                        </div>
                        <button id="vm-request-btn">
                            <i class="fas fa-phone-alt icon"></i>Request Call
                        </button>
                        <button id="vmRequestDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Call Response</h3>
                        <div class="form-group">
                            <label>Call ID:</label>
                            <input name="call-id-1" type="text" placeholder="call id">
                        </div>
                        <div class="form-group">
                            <label>Response Status:</label>
                            <select name="response-call-status" id="response-call-status" class="form-control">
                                <option value="confirmed">Confirmed</option>
                                <option value="rejected">Rejected</option>
                                <option value="unconfirmed">Unconfirmed</option>
                            </select>
                        </div>
                        <button id="vm-response-btn">
                            <i class="fas fa-reply icon"></i>Respond to Call
                        </button>
                        <button id="vmResponseDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Cancel Call</h3>
                        <div class="form-group">
                            <label>Call ID:</label>
                            <input name="call-id-2" type="text" placeholder="call id">
                        </div>
                        <div class="form-group">
                            <label>Canceled By:</label>
                            <select name="canceled" id="canceled" class="form-control">
                                <option value="consultant1">Consultant</option>
                                <option value="client1">Client</option>
                            </select>
                        </div>
                        <button id="vm-cancel-btn">
                            <i class="fas fa-times-circle icon"></i>Cancel Call
                        </button>
                        <button id="vmCancelDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Get Recent Calls</h3>
                        <div class="form-group">
                            <label>Consultant Username:</label>
                            <input name="get-recent-call" type="text" placeholder="Consultants username">
                        </div>
                        <div class="form-group">
                            <label>Page:</label>
                            <input name="recent-page" type="text" placeholder="page">
                        </div>
                        <div class="form-group">
                            <label>Per Page:</label>
                            <input name="recent-per-page" type="text" placeholder="per_page">
                        </div>
                        <button id="getRecentCall">
                            <i class="fas fa-history icon"></i>Get Recent Calls
                        </button>
                        <button id="getRecentCallDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Reservations Menu -->
                <div class="menu-content" id="reservations-menu">
                    <h2><i class="fas fa-calendar-alt icon"></i>Reservations Management</h2>
                    
                    <div class="box">
                        <h3>Get Reservation Slots</h3>
                        <div class="form-group">
                            <label>Consultant Username:</label>
                            <input name="reservation-consultant" type="text" placeholder="Consultant Username" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>Timezone:</label>
                            <input name="reservation-timezone" type="text" placeholder="Timezone (e.g. Asia/Tehran)" value="Asia/Tehran">
                        </div>
                        <button id="getReservationSlots">
                            <i class="fas fa-calendar-check icon"></i>Get Reservation Slots
                        </button>
                        <button id="getReservationSlotsDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Reserve Call Slot</h3>
                        <div class="form-group">
                            <label>Consultant Username:</label>
                            <input name="reserve-consultant" type="text" placeholder="Consultant Username" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>Date:</label>
                            <input name="reserve-date" type="text" placeholder="Date (YYYY-MM-DD)">
                        </div>
                        <div class="form-group">
                            <label>Start Time:</label>
                            <input name="reserve-start-time" type="text" placeholder="Start Time (HH:MM)">
                        </div>
                        <div class="form-group">
                            <label>End Time:</label>
                            <input name="reserve-end-time" type="text" placeholder="End Time (HH:MM)">
                        </div>
                        <div class="form-group">
                            <label>Timezone:</label>
                            <input name="reserve-timezone" type="text" placeholder="Timezone (e.g. Asia/Tehran)" value="Asia/Tehran">
                        </div>
                        <div class="form-group">
                            <label>Call Type:</label>
                            <select name="reserve-call-type">
                                <option value="voice">Voice</option>
                                <option value="video">Video</option>
                            </select>
                        </div>
                        <button id="reserveCallSlot">
                            <i class="fas fa-calendar-plus icon"></i>Reserve Call Slot
                        </button>
                        <button id="reserveCallSlotDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Get User Reservations</h3>
                        <button id="getUserReservations">
                            <i class="fas fa-list-alt icon"></i>Get User Reservations
                        </button>
                        <button id="getUserReservationsDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Get Consultant Reservations</h3>
                        <p>View all reservations made by clients for your consultation services.</p>
                        <div class="form-group">
                            <label>Date Filter:</label>
                            <select name="consultant-reservations-filter">
                                <option value="">All Time</option>
                                <option value="week">Last Week</option>
                                <option value="month">Last Month</option>
                                <option value="three_months">Last 3 Months</option>
                                <option value="six_months">Last 6 Months</option>
                                <option value="year">Last Year</option>
                            </select>
                        </div>
                        <button id="getConsultantReservations">
                            <i class="fas fa-calendar-check icon"></i>Get Consultant Reservations
                        </button>
                        <button id="getConsultantReservationsDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Subscriptions Menu -->
                <div class="menu-content" id="subscriptions-menu">
                    <h2><i class="fas fa-credit-card icon"></i>Subscriptions Management</h2>
                    
                    <div class="box">
                        <h3>Get Categories</h3>
                        <button id="getCategories">
                            <i class="fas fa-list icon"></i>Get Categories
                        </button>
                        <button id="getCategoriesDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Get Subscription Info</h3>
                        <button id="getSubscriptionInfo">
                            <i class="fas fa-info-circle icon"></i>Get Subscription Info
                        </button>
                        <button id="getSubscriptionInfoDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Purchase Subscription</h3>
                        <div class="form-group">
                            <label>Subscription ID:</label>
                            <input name="subscription_id" type="text" placeholder="Subscription ID">
                        </div>
                        <button id="purchaseSubscription">
                            <i class="fas fa-shopping-cart icon"></i>Purchase Subscription
                        </button>
                        <button id="purchaseSubscriptionDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Streams Menu -->
                <div class="menu-content" id="streams-menu">
                    <h2><i class="fas fa-video icon"></i>Streams Management</h2>
                    
                    <div class="box">
                        <h3>Start Stream</h3>
                        <div class="form-group">
                            <label>Call ID:</label>
                            <input name="stream-room-id" type="text" placeholder="Call ID">
                        </div>
                        <button id="stream-started">
                            <i class="fas fa-play icon"></i>Start Stream
                        </button>
                        <button id="streamStartedDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Stop Stream</h3>
                        <div class="form-group">
                            <label>Call ID:</label>
                            <input name="stream-room-id-stop" type="text" placeholder="Call ID">
                        </div>
                        <button id="stream-stopped">
                            <i class="fas fa-stop icon"></i>Stop Stream
                        </button>
                        <button id="streamStoppedDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Pay for Next Period</h3>
                        <div class="form-group">
                            <label>Call ID:</label>
                            <input name="stream-room-id-next-period" type="text" placeholder="Call ID">
                        </div>
                        <button id="pay-for-next-period">
                            <i class="fas fa-money-bill icon"></i>Pay for Next Period
                        </button>
                        <button id="payForNextPeriodDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Settings Menu -->
                <div class="menu-content" id="settings-menu">
                    <h2><i class="fas fa-cog icon"></i>Settings</h2>
                    
                    <div class="box">
                        <h3>Get Statistics</h3>
                        <button id="getStats">
                            <i class="fas fa-chart-bar icon"></i>Get Statistics
                        </button>
                        <button id="getStatsDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Set FCM Token</h3>
                        <div class="form-group">
                            <label>FCM Token:</label>
                            <input name="token" type="text" placeholder="fcm token" class="fcm-token">
                        </div>
                        <button id="set-fcm-btn">
                            <i class="fas fa-bell icon"></i>Set FCM Token
                        </button>
                        <button id="setFcmDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                    
                    <div class="box">
                        <h3>Set Preferred Language</h3>
                        <div class="form-group">
                            <label>Language Code:</label>
                            <input name="preferred_language" type="text" placeholder="e.g., en, fa, ar" value="en">
                        </div>
                        <button id="addPreferredLanguage">
                            <i class="fas fa-language icon"></i>Set Preferred Language
                        </button>
                        <button id="addPreferredLanguageDocs" class="docs-btn">
                            <i class="fas fa-file-alt icon"></i>
                        </button>
                    </div>
                </div>
            </div>
        </aside>

        <div id='messages'></div>
    </div>
</div>

<script>
    let base_url = "{{ socket_protocol }}://{{ host }}/ws/?version=3&t="

    function isOpen(wsc) {
        return wsc.readyState === wsc.OPEN
    }

    function sendMsg(wsc, data) {
        if (!isOpen(wsc)) {
            console.log('websocket is not ready yet')
            return
        }
        wsc.send(JSON.stringify(data))
    }

    $(document).ready(function() {
        console.log('WebSocket Chat V3 - Menu Interface Loaded');
        
        // Log active tab for debugging
        console.log('Active tab: ' + $('.menu-tab.active').data('tab'));
        console.log('Active menu: ' + $('.menu-content.active').attr('id'));
        
        // Update initial status display
//        $('#tab-status').html('Active tab: <strong>' + $('.menu-tab.active').data('tab') + '</strong> | Active menu: <strong>' + $('.menu-content.active').attr('id') + '</strong>');
        
        // Test tab switching
        console.log('Testing tab switching...');
        setTimeout(function() {
            console.log('Clicking on Rooms tab...');
            $('.menu-tab[data-tab="rooms"]').trigger('click');
            
            // Test other tabs after a delay
            setTimeout(function() {
                console.log('Clicking on Messages tab...');
                $('.menu-tab[data-tab="messages"]').trigger('click');
            }, 1000);
        }, 1000);
        
        // Tab switching functionality
        $(document).on('click', '.menu-tab', function() {
            // Log click for debugging
            console.log('Tab clicked: ' + $(this).data('tab'));
            
            // Remove active class from all tabs and contents
            $('.menu-tab').removeClass('active');
            $('.menu-content').removeClass('active');
            
            // Add active class to clicked tab
            $(this).addClass('active');
            
            // Show corresponding content
            const tabId = $(this).data('tab');
            console.log('Looking for menu: #' + tabId + '-menu');
            $('#' + tabId + '-menu').addClass('active');
            
            // Log active elements after change
            console.log('Now active tab: ' + $('.menu-tab.active').data('tab'));
            console.log('Now active menu: ' + $('.menu-content.active').attr('id'));
            
            // Update status display
          //  $('#tab-status').html('Active tab: <strong>' + tabId + '</strong> | Active menu: <strong>' + tabId + '-menu</strong>');
            
            // Prevent default action
            return false;
        });
        
        // Handle button clicks for stream actions
        function handleButtonClick(action, inputName) {
            let data = {
                "action": action,
                "room_id": $(`input[name=${inputName}]`).val()
            };
            sendMsg(ws, data);
        }

        // Connect to socket
        $('#connectSocket').click(function () {
            let selectedOption = $('select[name="token"]').val();

        let token = $('input[name="token"]').val() || $('select[name="token"]').val()


        if ($('select[name="token"] option:selected').text() === 'admin') {
            token = 'admin:' + token;
        }

        try {
            window.ws.close()

        } catch (e) {
            console.log(e)
        }


        window.ws = new WebSocket(base_url + token);
        ws.onmessage = function (event) {
            try {

                let data = JSON.parse(event.data)
                console.log(data)
                $('#messages').html(`<pre>${JSON.stringify(data, undefined, 2)}</pre>`)

            } catch (erro) {
                $('#messages').html(`<pre>${event.data}</pre>`)
            }
        };


    })

    $("#get-history").click(function () {
        let data = {
            "room": $("input[name=room-2]").val(),
            "action": "getHistory",
        }
        if (!isOpen(ws)) {
            console.log('websocket is not ready yet')
            return
        }
        sendMsg(ws, data)
    })

    $("#getConsultants").click(function () {
        let data = {
            "action": "getConsultants",
            "language_code": $('input[name="language_code"]').val(),
            "timezone": $('input[name="timezone"]').val(),
            "only_interacted": $('input[name="only_interacted"]').val(),
            "search": $('input[name="search_c"]').val(),
        }
        sendMsg(ws, data)
    })
    
    $("#getConsultant").click(function () {
        let data = {
            "action": "getConsultant",
            "username": $('input[name="consultant_username"]').val(),
            "language_code": $('input[name="consultant_language_code"]').val(),
        }
        sendMsg(ws, data)
    })
    $("#getRecentCall").click(function () {
        let data = {
            "action": "recentCalls",
            "consultant": $("input[name=get-recent-call]").val(),
            'page': $('input[name=recent-page]').val() || 1,
            'per_page': $('input[name=recent-per-page]').val() || 10,
        }
        console.log(data)
        sendMsg(ws, data)
    })

    $("#get-rooms").click(function () {
        let data = {
            "action": "getRooms",
            "consultant": $("input[name=get-rooms]").val(),
            'page': $('input[name=room-page]').val() || 1,
            'per_page': $('input[name=room-per-page]').val() || 10,
            'date': $('input[name=room-filter-data]').val() || 10,
            "unread_messages": $('select[name="unread"]').val(),
            "not_replied": $('select[name="not_replied"]').val(),
            "search": $('input[name="room_search"]').val(),
        }
        console.log(data)
        sendMsg(ws, data)
    })
    
    $("#start-room").click(function () {
        let data = {
            "action": "startRoom",
            "consultant": $("input[name=c-username]").val(),
            "room_type": "chat"
        }
        sendMsg(ws, data)
    })

    $("#close-room").click(function () {
        let data = {
            "action": "closeRoom",
            "room_id": $('input[name=close-room]').val(),
            "reason": $('input[name=reason]').val(),
        }
        sendMsg(ws, data)
    })
    $("#open-room").click(function () {
        let data = {
            "action": "openRoom",
            "room_id": $('input[name=close-room]').val()
        }
        sendMsg(ws, data)
    })

    $("#read-msg").click(function () {
        let data = {
            "action": "readMsg",
            "room_id": $('input[name=read-msg-room]').val(),
        }
        sendMsg(ws, data)
    })

    $("#send-message").click(function () {
        let data = {
            "room": $("input[name=room-1]").val(),
            "action": "sendMessage",
            "text": $("input[name=send-message]").val(),
            "payload": $("input[name=payloads]").val()
        }
        sendMsg(ws, data)
    })

    $("#send-message-s").click(function () {
        let data = {
            "room": $("input[name=room-3]").val(),
            "action": "ctest",
        }
        sendMsg(ws, data)
    })

    $("#getStats").click(function () {
        let data = {
            "action": "getStats",
        }
        sendMsg(ws, data)
    })

    $("#getCategories").click(function () {
        let data = {
            "action": "getCategories",
        }
        sendMsg(ws, data)
    })

    $("#getSubscriptionInfo").click(function () {
        let data = {
            "action": "subscriptionInfo",
        }
        sendMsg(ws, data)
    })
    
    $("#purchaseSubscription").click(function () {
        let data = {
            "action": "purchaseSubscription",
            "subscription_id": $("input[name=subscription_id]").val(),
        }
        sendMsg(ws, data)
    })

    $("#changeStatus").click(function () {
        let data = {
            "action": "changeStatus",
            "status": $('select[name=status]').val(),
        }
        sendMsg(ws, data)
    })
    
    $("#getDynamicConsultants").click(function () {
        let data = {
            "action": "getDynamicConsultants",
            "language_code": $('input[name="dynamic_language_code"]').val(),
            "timezone": $('input[name="dynamic_timezone"]').val(),
            "only_interacted": $('input[name="dynamic_only_interacted"]').val(),
            "search": $('input[name="dynamic_search"]').val(),
        }
        sendMsg(ws, data)
    })

    // این بخش حذف می‌شود چون قبلاً با آیدی UpdateMessage جایگزین شده است

    // این بخش حذف می‌شود چون قبلاً با کلاس delete-message-btn جایگزین شده است

    // این بخش حذف می‌شود چون قبلاً با آیدی set-fcm-btn جایگزین شده است
    // Video/Voice call request
    $("#vm-request-btn").click(function () {
        let data = {
            "action": "vmRequest",
            "consultant": $('input[name=consultant]').val(),
            'chat_type': $('#chat-type').val()
        }
        sendMsg(ws, data);
    });
        
    // Response to call
    $("#vm-response-btn").click(function () {
        let data = {
            "action": "consultantResponseCall",
            'call_id': $('input[name="call-id-1"]').val(),
            "response": $('#response-call-status').val(),
        }
        sendMsg(ws, data);
    });
    
    // Cancel call
    $("#vm-cancel-btn").click(function () {
        let data = {
            "action": "cancelCall",
            'call_id': $('input[name="call-id-2"]').val(),
            "canceled": $('#canceled').val(),
        }
        sendMsg(ws, data);
    });
    
    // Set FCM token
    $("#set-fcm-btn").click(function () {
        let data = {
            "action": "setFCM",
            "token": $('.fcm-token').val(),
        }
        sendMsg(ws, data);
    });
    
    $("#addPreferredLanguage").click(function () {
        let data = {
            "action": "addPreferredLanguage",
            "language_code": $('input[name="preferred_language"]').val(),
        }
        sendMsg(ws, data);
    });
    
    // Delete message
    $(".delete-message-btn").click(function () {
        let data = {
            "action": "deleteMessage",
            "message_id": $('.delete-message-id').val(),
        }
        sendMsg(ws, data);
    });
    
    // Update message
    $("#UpdateMessage").click(function () {
        let data = {
            "action": "updateMessage",
            "message_id": $('.update-message-id').val(),
            "text": $('.update-message-text').val(),
        }
        sendMsg(ws, data);
    });
    
    // Stream actions
    $("#stream-started").click(function () {
        handleButtonClick("streamStarted", "stream-room-id");
    });

    $("#stream-stopped").click(function () {
        handleButtonClick("streamStopped", "stream-room-id-stop");
    });

    $("#pay-for-next-period").click(function () {
        handleButtonClick("payForNextPeriod", "stream-room-id-next-period");
    });
    
    // Reservation actions
    $("#getReservationSlots").click(function () {
        let data = {
            "action": "getReservationSlots",
            "consultant": $('input[name="reservation-consultant"]').val(),
            "timezone": $('input[name="reservation-timezone"]').val() || "Asia/Tehran"
        };
        sendMsg(ws, data);
    });
    
    $("#reserveCallSlot").click(function () {
        let data = {
            "action": "reserveCallSlot",
            "consultant": $('input[name="reserve-consultant"]').val(),
            "date": $('input[name="reserve-date"]').val(),
            "start_time": $('input[name="reserve-start-time"]').val(),
            "end_time": $('input[name="reserve-end-time"]').val(),
            "timezone": $('input[name="reserve-timezone"]').val() || "Asia/Tehran",
            "call_type": $('select[name="reserve-call-type"]').val()
        };
        sendMsg(ws, data);
    });
    
    $("#getUserReservations").click(function () {
        let data = {
            "action": "getUserReservations"
        };
        sendMsg(ws, data);
    });
    
    $("#getConsultantReservations").click(function () {
        let dateFilter = $('select[name="consultant-reservations-filter"]').val();
        let data = {
            "action": "getConsultantReservations"
        };
        
        // اضافه کردن فیلتر تاریخ اگر انتخاب شده باشد
        if (dateFilter) {
            data.date_filter = dateFilter;
        }
        
        sendMsg(ws, data);
    });
    
    // Audio message
    $("#send-audio-message").click(async function () {
        let room_id = $('input[name=audio-room]').val();
        let file_input = $('input[name=audio-file]').get(0);
        if (!file_input.files.length) {
            alert('Please select an audio file');
            return;
        }
        let file = file_input.files[0];
        let formData = new FormData();
        formData.append('file', file);

        // Upload audio file to server
        let response = await fetch('/upload_audio/', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            alert('Failed to upload audio file');
            return;
        }

        let result = await response.json();
        let audio_url = result.audio_url;

        // ارسال پیام صوتی
        let data = {
            "room_id": room_id,
            "action": "sendAudioMessage",
            "audio_url": audio_url
        };
        sendMsg(ws, data);
    });
    
    // Helper function to show documentation modal
    function showDocsModal(title, params) {
        // Update modal title
        $("#docsModalLabel").text(title);
        
        // Format JSON with indentation for better readability
        $("#connectJsonEditor").val(JSON.stringify(params, null, 4));
        
        // Show the modal
        const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
        docsModal.show();
    }
    
    // Documentation button for Connect section
    $("#connectDocs").click(function() {
        // Create JSON object with all possible parameters
        const connectParams = {
            "action": "connect",
            "description": "Establishes a WebSocket connection to the server",
            "parameters": {
                "token": {
                    "type": "string",
                    "description": "Authentication token for the user",
                    "required": true,
                    "example": "consultant:06b0516cee0256b4c39372ed0d693004ccae397e"
                }
            },
            "example": {
                "token": "consultant:06b0516cee0256b4c39372ed0d693004ccae397e"
            },
            "response": {
                "success": {
                    "status": "connected",
                    "user_id": "user_123456"
                },
                "error": {
                    "status": "error",
                    "message": "Invalid token"
                }
            }
        };
        
        showDocsModal("Connect to Socket Documentation", connectParams);
    });
    
    // Documentation button for Get Consultants List
    $("#getConsultantsDocs").click(function() {
        const params = {
            "action": "getConsultants",
            "description": "Retrieves a list of consultants based on specified filters",
            "parameters": {
                "language_code": {
                    "type": "string",
                    "description": "Language code for filtering consultants",
                    "required": false,
                    "example": "en"
                },
                "timezone": {
                    "type": "string",
                    "description": "Timezone for filtering consultants",
                    "required": false,
                    "example": "Asia/Tehran"
                },
                "only_interacted": {
                    "type": "boolean",
                    "description": "Filter to show only consultants the user has interacted with",
                    "required": false,
                    "example": "False"
                },
                "search": {
                    "type": "string",
                    "description": "Search term to filter consultants by name or username",
                    "required": false,
                    "example": "John"
                }
            },
            "example": {
                "action": "getConsultants",
                "language_code": "en",
                "timezone": "Asia/Tehran",
                "only_interacted": "False",
                "search": ""
            }
        };
        
        showDocsModal("Get Consultants List Documentation", params);
    });
    
    // Documentation button for Get Consultant Info
    $("#getConsultantInfoDocs").click(function() {
        const params = {
            "action": "getConsultant",
            "description": "Retrieves detailed information about a specific consultant",
            "parameters": {
                "consultant_username": {
                    "type": "string",
                    "description": "Username of the consultant",
                    "required": true,
                    "example": "<EMAIL>"
                },
                "language_code": {
                    "type": "string",
                    "description": "Language code for localized information",
                    "required": false,
                    "example": "en"
                }
            },
            "example": {
                "action": "getConsultant",
                "consultant_username": "<EMAIL>",
                "language_code": "en"
            }
        };
        
        showDocsModal("Get Consultant Info Documentation", params);
    });
    
    // Documentation button for Change Status
    $("#changeStatusDocs").click(function() {
        const params = {
            "action": "changeStatus",
            "description": "Changes the online status of the consultant",
            "parameters": {
                "status": {
                    "type": "string",
                    "description": "New status value",
                    "required": true,
                    "enum": ["online", "offline", "busy"],
                    "example": "online"
                }
            },
            "example": {
                "action": "changeStatus",
                "status": "online"
            }
        };
        
        showDocsModal("Change Status Documentation", params);
    });
    
    // Documentation button for Get Dynamic Consultants
    $("#getDynamicConsultantsDocs").click(function() {
        const params = {
            "action": "getDynamicConsultants",
            "description": "Retrieves a dynamic list of consultants with real-time status updates",
            "parameters": {
                "language_code": {
                    "type": "string",
                    "description": "Language code for filtering consultants",
                    "required": false,
                    "example": "en"
                },
                "timezone": {
                    "type": "string",
                    "description": "Timezone for filtering consultants",
                    "required": false,
                    "example": "Asia/Tehran"
                },
                "only_interacted": {
                    "type": "boolean",
                    "description": "Filter to show only consultants the user has interacted with",
                    "required": false,
                    "example": "False"
                },
                "search": {
                    "type": "string",
                    "description": "Search term to filter consultants by name or username",
                    "required": false,
                    "example": ""
                }
            },
            "example": {
                "action": "getDynamicConsultants",
                "language_code": "en",
                "timezone": "Asia/Tehran",
                "only_interacted": "False",
                "search": ""
            }
        };
        
        showDocsModal("Get Dynamic Consultants Documentation", params);
    });
    
    // Documentation button for Get Rooms
    $("#getRoomsDocs").click(function() {
        const params = {
            "action": "getRooms",
            "description": "Retrieves a list of chat rooms based on specified filters",
            "parameters": {
                "consultant": {
                    "type": "string",
                    "description": "Filter rooms by consultant username",
                    "required": false,
                    "example": "<EMAIL>"
                },
                "page": {
                    "type": "integer",
                    "description": "Page number for pagination",
                    "required": false,
                    "example": "1"
                },
                "per_page": {
                    "type": "integer",
                    "description": "Number of items per page",
                    "required": false,
                    "example": "10"
                },
                "filter_date": {
                    "type": "string",
                    "description": "Filter rooms by date",
                    "required": false,
                    "example": "2023-01-01"
                },
                "search": {
                    "type": "string",
                    "description": "Search term to filter rooms by name",
                    "required": false,
                    "example": ""
                },
                "unread": {
                    "type": "boolean",
                    "description": "Filter to show only rooms with unread messages",
                    "required": false,
                    "example": "false"
                }
            },
            "example": {
                "action": "getRooms",
                "consultant": "",
                "page": "",
                "per_page": "",
                "filter_date": "",
                "search": "",
                "unread": "false"
            }
        };
        
        showDocsModal("Get Rooms Documentation", params);
    });
    
    // Documentation button for Start Room
    $("#startRoomDocs").click(function() {
        const params = {
            "action": "startRoom",
            "description": "Creates a new chat room with a consultant",
            "parameters": {
                "consultant": {
                    "type": "string",
                    "description": "Username of the consultant to start a room with",
                    "required": true,
                    "example": "<EMAIL>"
                },
                "room_type": {
                    "type": "string",
                    "description": "Type of room to create",
                    "required": true,
                    "enum": ["chat"],
                    "example": "chat"
                }
            },
            "example": {
                "action": "startRoom",
                "consultant": "<EMAIL>",
                "room_type": "chat"
            }
        };
        
        showDocsModal("Start Room Documentation", params);
    });
    
    // Documentation button for Room Management
    $("#roomManagementDocs").click(function() {
        const params = {
            "actions": {
                "closeRoom": {
                    "description": "Closes an active chat room",
                    "parameters": {
                        "room_id": {
                            "type": "string",
                            "description": "ID of the room to close",
                            "required": true,
                            "example": "room_123456"
                        },
                        "reason": {
                            "type": "string",
                            "description": "Reason for closing the room",
                            "required": false,
                            "example": "Consultation completed"
                        }
                    },
                    "example": {
                        "action": "closeRoom",
                        "room_id": "room_123456",
                        "reason": "Consultation completed"
                    }
                },
                "openRoom": {
                    "description": "Reopens a closed chat room",
                    "parameters": {
                        "room_id": {
                            "type": "string",
                            "description": "ID of the room to reopen",
                            "required": true,
                            "example": "room_123456"
                        }
                    },
                    "example": {
                        "action": "openRoom",
                        "room_id": "room_123456"
                    }
                }
            }
        };
        
        showDocsModal("Room Management Documentation", params);
    });
    
    // Documentation button for Read All Messages
    $("#readMsgDocs").click(function() {
        const params = {
            "action": "readMsg",
            "description": "Marks all messages in a room as read",
            "parameters": {
                "room_id": {
                    "type": "string",
                    "description": "ID of the room to mark messages as read",
                    "required": true,
                    "example": "room_123456"
                }
            },
            "example": {
                "action": "readMsg",
                "room_id": "room_123456"
            }
        };
        
        showDocsModal("Read All Messages Documentation", params);
    });
    
    // Documentation button for Get History
    $("#getHistoryDocs").click(function() {
        const params = {
            "action": "getHistory",
            "description": "Retrieves message history for a specific room",
            "parameters": {
                "room": {
                    "type": "string",
                    "description": "ID of the room to get history for",
                    "required": true,
                    "example": "room_123456"
                }
            },
            "example": {
                "action": "getHistory",
                "room": "room_123456"
            }
        };
        
        showDocsModal("Get History Documentation", params);
    });
    
    // Documentation button for Send Message
    $("#sendMessageDocs").click(function() {
        const params = {
            "action": "sendMessage",
            "description": "Sends a message to a specific room",
            "parameters": {
                "message": {
                    "type": "string",
                    "description": "Text content of the message",
                    "required": true,
                    "example": "Hello, how can I help you today?"
                },
                "room_id": {
                    "type": "string",
                    "description": "ID of the room to send the message to",
                    "required": true,
                    "example": "room_123456"
                },
                "payloads": {
                    "type": "string",
                    "description": "Additional data to send with the message",
                    "required": false,
                    "example": "{\"attachment\": \"url_to_file\"}"
                }
            },
            "example": {
                "action": "sendMessage",
                "message": "Hello, how can I help you today?",
                "room_id": "room_123456",
                "payloads": ""
            }
        };
        
        showDocsModal("Send Message Documentation", params);
    });
    
    // Documentation button for Update Message
    $("#updateMessageDocs").click(function() {
        const params = {
            "action": "updateMessage",
            "description": "Updates the content of an existing message",
            "parameters": {
                "message_id": {
                    "type": "string",
                    "description": "ID of the message to update",
                    "required": true,
                    "example": "msg_123456"
                },
                "text": {
                    "type": "string",
                    "description": "New text content for the message",
                    "required": true,
                    "example": "Updated message content"
                }
            },
            "example": {
                "action": "updateMessage",
                "message_id": "msg_123456",
                "text": "Updated message content"
            }
        };
        
        showDocsModal("Update Message Documentation", params);
    });
    
    // Documentation button for Delete Message
    $("#deleteMessageDocs").click(function() {
        const params = {
            "action": "deleteMessage",
            "description": "Deletes a specific message",
            "parameters": {
                "message_id": {
                    "type": "string",
                    "description": "ID of the message to delete",
                    "required": true,
                    "example": "msg_123456"
                }
            },
            "example": {
                "action": "deleteMessage",
                "message_id": "msg_123456"
            }
        };
        
        showDocsModal("Delete Message Documentation", params);
    });
    
    // Documentation button for Send Audio Message
    $("#sendAudioMessageDocs").click(function() {
        const params = {
            "action": "sendAudioMessage",
            "description": "Sends an audio message to a specific room",
            "parameters": {
                "room_id": {
                    "type": "string",
                    "description": "ID of the room to send the audio message to",
                    "required": true,
                    "example": "room_123456"
                },
                "audio_url": {
                    "type": "string",
                    "description": "URL of the uploaded audio file",
                    "required": true,
                    "example": "https://example.com/audio/file.mp3"
                }
            },
            "note": "The audio file must be uploaded first using the file input field",
            "example": {
                "action": "sendAudioMessage",
                "room_id": "room_123456",
                "audio_url": "https://example.com/audio/file.mp3"
            }
        };
        
        showDocsModal("Send Audio Message Documentation", params);
    });
    
    // Documentation button for Voice/Video Call Request
    $("#vmRequestDocs").click(function() {
        const params = {
            "action": "vmRequest",
            "description": "Initiates a voice or video call request to a consultant",
            "parameters": {
                "consultant": {
                    "type": "string",
                    "description": "Username of the consultant to call",
                    "required": true,
                    "example": "<EMAIL>"
                },
                "chat_type": {
                    "type": "string",
                    "description": "Type of call to initiate",
                    "required": true,
                    "enum": ["voice", "video"],
                    "example": "voice"
                }
            },
            "example": {
                "action": "vmRequest",
                "consultant": "<EMAIL>",
                "chat_type": "voice"
            }
        };
        
        showDocsModal("Voice/Video Call Request Documentation", params);
    });
    
    // Documentation button for Call Response
    $("#vmResponseDocs").click(function() {
        const params = {
            "action": "consultantResponseCall",
            "description": "Responds to an incoming call request",
            "parameters": {
                "call_id": {
                    "type": "string",
                    "description": "ID of the call to respond to",
                    "required": true,
                    "example": "call_123456"
                },
                "response": {
                    "type": "string",
                    "description": "Response status for the call",
                    "required": true,
                    "enum": ["confirmed", "rejected", "unconfirmed"],
                    "example": "confirmed"
                }
            },
            "example": {
                "action": "consultantResponseCall",
                "call_id": "call_123456",
                "response": "confirmed"
            }
        };
        
        showDocsModal("Call Response Documentation", params);
    });
    
    // Documentation button for Cancel Call
    $("#vmCancelDocs").click(function() {
        const params = {
            "action": "cancelCall",
            "description": "Cancels an active or pending call",
            "parameters": {
                "call_id": {
                    "type": "string",
                    "description": "ID of the call to cancel",
                    "required": true,
                    "example": "call_123456"
                },
                "canceled": {
                    "type": "string",
                    "description": "Who is canceling the call",
                    "required": true,
                    "enum": ["consultant1", "client1"],
                    "example": "consultant1"
                }
            },
            "example": {
                "action": "cancelCall",
                "call_id": "call_123456",
                "canceled": "consultant1"
            }
        };
        
        showDocsModal("Cancel Call Documentation", params);
    });
    
    // Documentation button for Get Recent Calls
    $("#getRecentCallDocs").click(function() {
        const params = {
            "action": "getRecentCalls",
            "description": "Retrieves a list of recent calls for a consultant",
            "parameters": {
                "consultant": {
                    "type": "string",
                    "description": "Username of the consultant",
                    "required": false,
                    "example": "<EMAIL>"
                },
                "page": {
                    "type": "integer",
                    "description": "Page number for pagination",
                    "required": false,
                    "example": "1"
                },
                "per_page": {
                    "type": "integer",
                    "description": "Number of items per page",
                    "required": false,
                    "example": "10"
                }
            },
            "example": {
                "action": "getRecentCalls",
                "consultant": "<EMAIL>",
                "page": "1",
                "per_page": "10"
            }
        };
        
        showDocsModal("Get Recent Calls Documentation", params);
    });
    
    // Documentation button for Get Reservation Slots
    $("#getReservationSlotsDocs").click(function() {
        const params = {
            "action": "getReservationSlots",
            "description": "Retrieves available reservation slots for a consultant",
            "parameters": {
                "consultant": {
                    "type": "string",
                    "description": "Username of the consultant",
                    "required": true,
                    "example": "<EMAIL>"
                },
                "timezone": {
                    "type": "string",
                    "description": "Timezone for displaying slots",
                    "required": false,
                    "example": "Asia/Tehran"
                }
            },
            "example": {
                "action": "getReservationSlots",
                "consultant": "<EMAIL>",
                "timezone": "Asia/Tehran"
            }
        };
        
        showDocsModal("Get Reservation Slots Documentation", params);
    });
    
    // Documentation button for Reserve Call Slot
    $("#reserveCallSlotDocs").click(function() {
        const params = {
            "action": "reserveCallSlot",
            "description": "Reserves a call slot with a consultant",
            "parameters": {
                "consultant": {
                    "type": "string",
                    "description": "Username of the consultant",
                    "required": true,
                    "example": "<EMAIL>"
                },
                "date": {
                    "type": "string",
                    "description": "Date for the reservation (YYYY-MM-DD)",
                    "required": true,
                    "example": "2023-12-25"
                },
                "start_time": {
                    "type": "string",
                    "description": "Start time for the reservation (HH:MM)",
                    "required": true,
                    "example": "14:00"
                },
                "end_time": {
                    "type": "string",
                    "description": "End time for the reservation (HH:MM)",
                    "required": true,
                    "example": "15:00"
                },
                "timezone": {
                    "type": "string",
                    "description": "Timezone for the reservation",
                    "required": false,
                    "example": "Asia/Tehran"
                },
                "call_type": {
                    "type": "string",
                    "description": "Type of call to reserve",
                    "required": true,
                    "enum": ["voice", "video"],
                    "example": "voice"
                }
            },
            "example": {
                "action": "reserveCallSlot",
                "consultant": "<EMAIL>",
                "date": "2023-12-25",
                "start_time": "14:00",
                "end_time": "15:00",
                "timezone": "Asia/Tehran",
                "call_type": "voice"
            }
        };
        
        showDocsModal("Reserve Call Slot Documentation", params);
    });
    
    // Documentation button for Get User Reservations
    $("#getUserReservationsDocs").click(function() {
        const params = {
            "action": "getUserReservations",
            "description": "Retrieves all reservations made by the current user",
            "parameters": {},
            "example": {
                "action": "getUserReservations"
            }
        };
        
        showDocsModal("Get User Reservations Documentation", params);
    });
    
    // Documentation button for Get Consultant Reservations
    $("#getConsultantReservationsDocs").click(function() {
        const params = {
            "action": "getConsultantReservations",
            "description": "Retrieves all reservations made with the consultant",
            "parameters": {
                "date_filter": {
                    "type": "string",
                    "description": "Filter reservations by date range",
                    "required": false,
                    "enum": ["week", "month", "three_months", "six_months", "year"],
                    "example": "month"
                }
            },
            "example": {
                "action": "getConsultantReservations",
                "date_filter": "month"
            }
        };
        
        showDocsModal("Get Consultant Reservations Documentation", params);
    });
    
    // Documentation button for Get Categories
    $("#getCategoriesDocs").click(function() {
        const params = {
            "action": "getCategories",
            "description": "Retrieves all available subscription categories",
            "parameters": {},
            "example": {
                "action": "getCategories"
            }
        };
        
        showDocsModal("Get Categories Documentation", params);
    });
    
    // Documentation button for Get Subscription Info
    $("#getSubscriptionInfoDocs").click(function() {
        const params = {
            "action": "subscriptionInfo",
            "description": "Retrieves information about the user's current subscription",
            "parameters": {},
            "example": {
                "action": "subscriptionInfo"
            }
        };
        
        showDocsModal("Get Subscription Info Documentation", params);
    });
    
    // Documentation button for Purchase Subscription
    $("#purchaseSubscriptionDocs").click(function() {
        const params = {
            "action": "purchaseSubscription",
            "description": "Purchases a subscription plan",
            "parameters": {
                "subscription_id": {
                    "type": "string",
                    "description": "ID of the subscription to purchase",
                    "required": true,
                    "example": "sub_123456"
                }
            },
            "example": {
                "action": "purchaseSubscription",
                "subscription_id": "sub_123456"
            }
        };
        
        showDocsModal("Purchase Subscription Documentation", params);
    });
    
    // Documentation button for Stream Started
    $("#streamStartedDocs").click(function() {
        const params = {
            "action": "streamStarted",
            "description": "Notifies the server that a stream has started",
            "parameters": {
                "room_id": {
                    "type": "string",
                    "description": "ID of the room where the stream is starting",
                    "required": true,
                    "example": "room_123456"
                }
            },
            "example": {
                "action": "streamStarted",
                "room_id": "room_123456"
            }
        };
        
        showDocsModal("Stream Started Documentation", params);
    });
    
    // Documentation button for Stream Stopped
    $("#streamStoppedDocs").click(function() {
        const params = {
            "action": "streamStopped",
            "description": "Notifies the server that a stream has stopped",
            "parameters": {
                "room_id": {
                    "type": "string",
                    "description": "ID of the room where the stream is stopping",
                    "required": true,
                    "example": "room_123456"
                }
            },
            "example": {
                "action": "streamStopped",
                "room_id": "room_123456"
            }
        };
        
        showDocsModal("Stream Stopped Documentation", params);
    });
    
    // Documentation button for Pay For Next Period
    $("#payForNextPeriodDocs").click(function() {
        const params = {
            "action": "payForNextPeriod",
            "description": "Pays for the next period of a streaming service",
            "parameters": {
                "room_id": {
                    "type": "string",
                    "description": "ID of the room to extend the streaming period for",
                    "required": true,
                    "example": "room_123456"
                }
            },
            "example": {
                "action": "payForNextPeriod",
                "room_id": "room_123456"
            }
        };
        
        showDocsModal("Pay For Next Period Documentation", params);
    });
    
    // Documentation button for Set FCM Token
    $("#setFcmDocs").click(function() {
        const params = {
            "action": "setFCM",
            "description": "Sets the Firebase Cloud Messaging token for push notifications",
            "parameters": {
                "token": {
                    "type": "string",
                    "description": "FCM token for the device",
                    "required": true,
                    "example": "fcm_token_123456"
                }
            },
            "example": {
                "action": "setFCM",
                "token": "fcm_token_123456"
            }
        };
        
        showDocsModal("Set FCM Token Documentation", params);
    });
    
    // Documentation button for Add Preferred Language
    $("#addPreferredLanguageDocs").click(function() {
        const params = {
            "action": "addPreferredLanguage",
            "description": "Sets the user's preferred language for the application",
            "parameters": {
                "language_code": {
                    "type": "string",
                    "description": "Language code to set as preferred",
                    "required": true,
                    "example": "en"
                }
            },
            "example": {
                "action": "addPreferredLanguage",
                "language_code": "en"
            }
        };
        
        showDocsModal("Add Preferred Language Documentation", params);
    });
    
    // Documentation button for Get Statistics
    $("#getStatsDocs").click(function() {
        const params = {
            "action": "getStats",
            "description": "Retrieves usage statistics for the current user or consultant",
            "parameters": {},
            "example": {
                "action": "getStats"
            },
            "response": {
                "description": "Returns various statistics including:",
                "fields": {
                    "total_calls": "Total number of calls made/received",
                    "total_messages": "Total number of messages sent/received",
                    "active_rooms": "Number of currently active chat rooms",
                    "subscription_status": "Current subscription status and details",
                    "usage_metrics": "Various usage metrics and analytics"
                }
            }
        };
        
        showDocsModal("Get Statistics Documentation", params);
    });
    
    // Function to show documentation modal
    function showDocsModal(title, params) {
        // Set modal title
        $("#docsModalLabel").text(title);
        
        // Format JSON with indentation for better readability
        const formattedJson = JSON.stringify(params, null, 4);
        
        // Set JSON content in the textarea
        $("#connectJsonEditor").val(formattedJson);
        
        // Show the modal
        const docsModal = new bootstrap.Modal(document.getElementById('docsModal'));
        docsModal.show();
    }
    
    // Documentation button for Connect to Socket
    $("#connectDocs").click(function() {
        const params = {
            "action": "connect",
            "description": "Establishes a WebSocket connection with the server",
            "parameters": {
                "token": {
                    "type": "string",
                    "description": "Authentication token for the user",
                    "required": true,
                    "example": "consultant:06b0516cee0256b4c39372ed0d693004ccae397e"
                }
            },
            "example": {
                "action": "connect",
                "token": "consultant:06b0516cee0256b4c39372ed0d693004ccae397e"
            },
            "notes": [
                "The token can be selected from the dropdown or entered manually",
                "Different token types provide different access levels",
                "consultant: Full access to consultant features",
                "client: Access to client features",
                "admin: Administrative access"
            ]
        };
        
        showDocsModal("Connect to Socket Documentation", params);
    });
    
    // Close document ready function
    });
</script>
</body>
</html>
