import datetime
from typing import Optional, List
from fastapi_sqlalchemy import db
from pydantic import BaseModel, validator, Field

from models import Consultant

__all__ = [
    'GetConsultants', 'GetConsultant', 'RecentActivity', 'StartRoom', 'ConsultantsChangeStatus', 'UpdateMessage',
    'GetReservationSlots', 'ReserveCallSlot', 'GetUserReservations', 'GetConsultantReservations', 'AddPreferredLanguage',
]


class VMRequest(BaseModel):
    consultant: str = ...  # consultant username
    chat_type: str = ...  # voice / video


class ConsultantsChangeStatus(BaseModel):
    type: str = 'changeStatus'
    status: str  # offline, online, or busy


class GetConsultants(BaseModel):
    type: str = 'getConsultants'
    language_code: str = Field(description='Client language code(s). eg: fa or en,fa,ar', examples=['fa', 'en,fa,ar'])
    timezone: str = Field(description='client timezone. eg:Asia/Tehran', examples=['Asia/Tehran'])
    only_interacted: bool = Field(default=False, description='If true, only return consultants the user has interacted with.')
    search: str = Field(default=None, description='Search term to filter consultants by fullname or username.')


class GetConsultant(BaseModel):
    type: str = 'getConsultant'
    username: str = Field(description='Consultant username to retrieve', examples=['<EMAIL>'])
    language_code: str = Field(description='Client language code(s). eg: fa or en,fa,ar', examples=['fa', 'en,fa,ar'])
    
    @validator('username')
    def validate_username(cls, value):
        with db():
            if not db.session.query(Consultant).filter(Consultant.username == value).first():
                raise ValueError(f'consultant with username {value} does not exist')
        return value


class RecentActivity(BaseModel):
    type: str = 'recentActivity'
    consultant: str = Field(description='consultant username')

    @validator('consultant')
    def get_consultant(cls, value):
        with db():
            if obj := db.session.query(
                    Consultant
            ).filter(Consultant.username == value).first():
                return obj

            raise ValueError(f'consultant with username {value} does not exist')


class StartRoom(BaseModel):
    type: str = 'startRoom'
    consultant: str = ...
    room_type: str = 'chat'

    @validator('consultant')
    def get_consultant(cls, value):
        with db():
            if obj := db.session.query(Consultant).filter(Consultant.username == value).first():
                return obj

        raise ValueError(f'consultant with username {value} does not exist')

    @validator('room_type')
    def validate_room_type(cls, value):
        if value not in ['chat', 'voice', 'video']:
            raise ValueError('room type should be one of [chat, voice, video] options')

        return value
class SendAudioMessageRequest(BaseModel):
    type: str = 'sendAudioMessage'
    room_id: int
    audio_url: str

class UpdateMessage(BaseModel):
    type: str = 'updateMessage'
    message_id: int = ...
    text: str = ...

class UserUpdateRequest(BaseModel):
    type: str = 'userUpdate'
    age: Optional[int] = None
    preferred_languages: Optional[List[str]] = None  # لیست زبان‌ها
    gender: Optional[str] = None
    
    @validator("preferred_languages", pre=True)
    def convert_preferred_languages_to_list(cls, v):
        if isinstance(v, str):
            return v.split(", ") if v else []
        return v or []


class GetReservationSlots(BaseModel):
    action: str = 'getReservationSlots'
    consultant: str = Field(description='Consultant username to get reservation slots for', examples=['<EMAIL>'])
    timezone: str = Field(description='Client timezone. eg:Asia/Tehran', examples=['Asia/Tehran'])
    
    @validator('consultant')
    def validate_consultant(cls, value):
        with db():
            if not db.session.query(Consultant).filter(Consultant.username == value).first():
                raise ValueError(f'Consultant with username {value} does not exist')
        return value


class ReserveCallSlot(BaseModel):
    action: str = 'reserveCallSlot'
    consultant: str = Field(description='Consultant username to reserve a call slot with', examples=['<EMAIL>'])
    date: str = Field(description='Date of the reservation in YYYY-MM-DD format', examples=['2023-12-31'])
    start_time: str = Field(description='Start time of the reservation in HH:MM format', examples=['14:30'])
    end_time: str = Field(description='End time of the reservation in HH:MM format', examples=['15:00'])
    call_type: str = Field(description='Type of call: voice or video', examples=['voice', 'video'])
    timezone: Optional[str] = Field(default='UTC', description='Client timezone', examples=['Asia/Tehran', 'Europe/London'])
    
    @validator('consultant')
    def validate_consultant(cls, value):
        with db():
            if not db.session.query(Consultant).filter(Consultant.username == value).first():
                raise ValueError(f'Consultant with username {value} does not exist')
        return value
        
    @validator('call_type')
    def validate_call_type(cls, value):
        if value not in ['voice', 'video']:
            raise ValueError('Call type must be either "voice" or "video"')
        return value
        
    @validator('date')
    def validate_date(cls, value):
        try:
            datetime.datetime.strptime(value, '%Y-%m-%d')
            return value
        except ValueError:
            raise ValueError('Date must be in YYYY-MM-DD format')
            
    @validator('start_time', 'end_time')
    def validate_time(cls, value):
        try:
            datetime.datetime.strptime(value, '%H:%M')
            return value
        except ValueError:
            raise ValueError('Time must be in HH:MM format')


class GetUserReservations(BaseModel):
    action: str = 'getUserReservations'


class GetConsultantReservations(BaseModel):
    action: str = 'getConsultantReservations'
    date_filter: Optional[str] = Field(
        default=None, 
        description='Filter reservations by date range: week, month, three_months, six_months, year',
        examples=['week', 'month', 'three_months', 'six_months', 'year']
    )
    
    @validator('date_filter')
    def validate_date_filter(cls, value):
        if value is not None and value not in ['week', 'month', 'three_months', 'six_months', 'year']:
            raise ValueError('Date filter must be one of: week, month, three_months, six_months, year')
        return value


class AddPreferredLanguage(BaseModel):
    """
    Request to set a user's preferred language
    """
    language_code: str = Field(
        ..., 
        description='Language code to set as preferred (e.g., "en", "fa")',
        examples=['en', 'fa', 'ar']
    )
    
    @validator('language_code')
    def validate_language_code(cls, value):
        if not value or len(value) < 2:
            raise ValueError('Language code must be at least 2 characters')
        return value