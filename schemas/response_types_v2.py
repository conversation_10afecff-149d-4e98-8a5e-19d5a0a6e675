from datetime import datetime, <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import *
from pydantic import BaseModel, validator, root_validator
from fastapi_sqlalchemy import db
from models.rate import Rate
from models.consultant import Consultant
from services.subscription_service import SubscriptionService
from models.subscription import UserConsultantInteraction

from pydantic import BaseModel, validator

from schemas.models import *
from schemas.response_types import TextMessageResponse, ByUser

__all__ = [
    'ConsultantsResponse', 'ConsultantsListResponse', 'ConsultantResponse', 'ChangeStatusResponse',
    'DynamicConsultantResponse', 'DynamicConsultantsListResponse'
]


class ConsultantsResponse(BaseModel):
    act: str = "consultant"
    username: str = ...
    fullname: Optional[str] = None
    slogan: Optional[str] = None
    topics: Any = ...
    languages: Any = ...
    call_languages: Any = ...
    avatar_url: Optional[str] = None
    estimate_time: str = ""
    is_ai: bool = False
    ai_project: Optional[str] = None
    contact_type: Any
    bio: Optional[str]
    description: Any = None 
    status: Optional[str] = 'offline'  # تغییر وضعیت
    is_on_another_call: bool = False
    first_message: Optional[str] = None
    scheduling: Optional[dict] = {}
    categories: Optional[list] = []
    unread_count: int = 0
    avg_rate: float = 5.0
    session_duration: Optional[int] = None  # اضافه کردن مدت زمان هر جلسه
    video_call_cost: Optional[int] = None  # اضافه کردن هزینه هر جلسه تصویری
    voice_call_cost: Optional[int] = None  # اضافه کردن هزینه هر جلسه صوتی

    @validator('username', pre=True, always=True)
    def process_username(cls, v):
        if isinstance(v, str) and ':' in v:
            return v.split(':')[0]
        return v
    
    @root_validator(pre=True)
    def calculate_avg_rate(cls, values):
        consultant_username = values.get("username")
        if consultant_username:
            avg_rate = cls.fetch_avg_rate(consultant_username)
            values['avg_rate'] = avg_rate
        return values
    
    @staticmethod
    def fetch_avg_rate(consultant_username: int) -> float:
        try:
            with db():
                consultant = db.session.query(Consultant).filter(Consultant.username == consultant_username).first()                
                return consultant.calculate_avg_rate(db.session)
        except Exception as exp:
            return 5.0



class ConsultantResponse(BaseModel):
    act: str = "GetConsultant"
    username: str = ...
    fullname: Optional[str] = None
    slogan: Optional[str] = None
    topics: Any = ...
    languages: Any = ...
    call_languages: Any = ...
    avatar_url: Optional[str] = None
    estimate_time: str = ""
    is_ai: bool = False
    ai_project: Optional[str] = None
    contact_type: Any
    bio: Optional[str]
    description: Any = None 
    status: Optional[str] = 'offline'  # تغییر وضعیت
    is_on_another_call: bool = False
    first_message: Optional[str] = None
    scheduling: Optional[dict] = {}
    categories: Optional[list] = []
    unread_count: int = 0
    avg_rate: float = 5.0
    session_duration: Optional[int] = None  # اضافه کردن مدت زمان هر جلسه
    video_call_cost: Optional[int] = None  # اضافه کردن هزینه هر جلسه تصویری
    voice_call_cost: Optional[int] = None  # اضافه کردن هزینه هر جلسه صوتی
    subscription_info: Optional[dict] = None  # اطلاعات اشتراک کاربر

    @validator('username', pre=True, always=True)
    def process_username(cls, v):
        if isinstance(v, str) and ':' in v:
            return v.split(':')[0]
        return v
    
    @root_validator(pre=True)
    def calculate_avg_rate(cls, values):
        consultant_username = values.get("username")
        if consultant_username:
            avg_rate = cls.fetch_avg_rate(consultant_username)
            values['avg_rate'] = avg_rate
        return values
    
    @staticmethod
    def fetch_avg_rate(consultant_username: int) -> float:
        try:
            with db():
                consultant = db.session.query(Consultant).filter(Consultant.username == consultant_username).first()                
                return consultant.calculate_avg_rate(db.session)
        except Exception as exp:
            return 5.0
            
    @staticmethod
    def get_subscription_info(user_id: int, consultant_id: int) -> dict:
        """
        Get subscription information for a user and consultant
        
        Args:
            user_id: ID of the user
            consultant_id: ID of the consultant
            
        Returns:
            dict: Dictionary with subscription information
        """
        # Get consultant to check if it's AI-enabled and get all needed consultant data
        with db():
            consultant = db.session.query(Consultant).filter(Consultant.id == consultant_id).first()
            if not consultant or not consultant.is_ai:
                return None  # Return None for non-AI consultants
                
            # Store consultant data we'll need outside the session
            free_limit_period = consultant.free_limit_period
            free_messages_per_period = consultant.free_messages_per_period
        
            # Get active subscription
            active_subscription = SubscriptionService.get_active_subscription(user_id)
            
            # Get user-consultant interaction and process it within the session
            interaction = db.session.query(UserConsultantInteraction).filter(
                UserConsultantInteraction.user_id == user_id,
                UserConsultantInteraction.consultant_id == consultant_id
            ).first()
            
            # Process interaction data within the session
            messages_in_period = 0
            can_send = False
            reason = "No interaction data available"
            
            if interaction:
                messages_in_period = interaction.get_messages_in_period()
                can_send, reason = interaction.can_send_message(active_subscription)
        
        # Initialize subscription info
        subscription_info = {
            # Free message limits
            'message_limit_type': free_limit_period,  # Type of message limit (daily/weekly/monthly)
            'messages_per_period': free_messages_per_period,  # Number of messages allowed per period
            'messages_left_in_period': 0,  # Number of messages left in current period
            
            # Subscription status
            'has_active_subscription': False,  # Whether user has active subscription
            'subscription_duration_days': 0,  # Duration of subscription in days
            'subscription_days_left': 0,  # Number of days left in subscription
            
            # Additional info
            'subscription_name': None,
            'subscription_max_messages': None,
            'subscription_messages_used': 0,
            'can_send_message': False,
            'message_status_reason': "No active subscription or free message limit reached"
        }
        
        # Check if user has active subscription
        if active_subscription and active_subscription.is_valid():
            days_left = (active_subscription.end_date - datetime.now()).days
            total_days = active_subscription.subscription.duration_days
            
            subscription_info.update({
                'has_active_subscription': True,
                'subscription_duration_days': total_days,
                'subscription_days_left': max(0, days_left),
                'subscription_name': active_subscription.subscription.name,
                'subscription_max_messages': active_subscription.subscription.max_messages,
                'subscription_messages_used': active_subscription.messages_used,
                'can_send_message': True,
                'message_status_reason': "Active subscription"
            })
        
        # Calculate free messages left in the current period if interaction exists
        if interaction:
            free_messages_left = max(0, free_messages_per_period - messages_in_period)
            
            subscription_info.update({
                'messages_left_in_period': free_messages_left,
                'can_send_message': can_send,
                'message_status_reason': reason
            })
        
        return subscription_info
    
    @root_validator
    def add_subscription_info(cls, values):
        """Add subscription information to the response"""
        # This would be populated from the API endpoint with actual user_id
        # For now, we'll leave it as a placeholder that needs to be filled by the API
        values['subscription_info'] = None  # Will be populated by the API endpoint
        return values


class ConsultantsListResponse(BaseModel):
    act: str = "consultantList"
    results: List[ConsultantsResponse] = ...


class ChangeStatusResponse(BaseModel):
    act: str = 'changeStatus'
    consultant: str
    status: str  # تغییر وضعیت


class DynamicConsultantResponse(BaseModel):
    """
    Simplified consultant response with only dynamic data (username, status, unread_count)
    """
    act: str = "consultant"
    username: str = ...
    status: Optional[str] = 'offline'
    unread_count: int = 0
    
    @validator('username', pre=True, always=True)
    def process_username(cls, v):
        if isinstance(v, str) and ':' in v:
            return v.split(':')[0]
        return v


class DynamicConsultantsListResponse(BaseModel):
    """
    Response containing a list of simplified consultant data
    """
    act: str = "dynamicConsultantList"
    results: List[DynamicConsultantResponse] = ...
