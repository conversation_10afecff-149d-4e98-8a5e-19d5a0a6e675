from datetime import datetime
from typing import Optional, Type, Any

from pydantic import BaseModel, Field, validator

__all__ = [
    'MessageModel', 'PhotoModel', 'VideoModel', 'VoiceModel', 'DocumentModel',
]

class BaseMessageModel(BaseModel):
    room_id: str
    id: Any = ''
    date: datetime = Field(default_factory=datetime.now)
    type: str
    reply_to: Optional[str] = None
    edited: bool = False
    by_user: dict = ...
    has_read: bool = False
    payload: Optional[str] = None

class MessageModel(BaseMessageModel):
    type: str = "text"
    act: str = "message"
    content: Optional[str] = None
    audio_url: Optional[str] = None
    audio_duration: Optional[int] = None

class FileBaseModel(BaseModel):
    preview: str = ...
    original: str = ...
    name: str = ...
    mime_type: str = ...

class VideoModel(BaseMessageModel):
    video: FileBaseModel = ...

class PhotoModel(BaseMessageModel):
    photo: FileBaseModel = ...

class VoiceModel(BaseMessageModel):
    voice: FileBaseModel = ...

class DocumentModel(BaseMessageModel):
    document: FileBaseModel = ...
