from typing import List

from fastapi_sqlalchemy import db
from pydantic import BaseModel, validator
from sqlalchemy import exists

from models import Category


class BaseCategory(BaseModel):
    id: int = ...
    title: str = ...
    created_at: str = ...


class CategoryCreate(BaseModel):
    title: str = ...

    @validator('title')
    def validate_title(cls, value):
        with db():
            if db.session.query(exists().where(Category.title == value)).scalar():
                raise ValueError(f'category with name `{value}` already exists')

        return value


class CategoryList(BaseModel):
    results: List[BaseCategory] = []
