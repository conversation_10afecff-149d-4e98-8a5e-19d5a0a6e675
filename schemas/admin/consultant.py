import datetime
from typing import List, Any, Optional, Dict, Union

from pydantic import BaseModel, validator
from fastapi_sqlalchemy import db


class BaseConsultant(BaseModel):
    username: str = ...
    fullname: Optional[str] = None
    password: str = None
    topics: Any = None
    languages: Any = None
    call_languages: Any = None
    avatar_url: Optional[str] = None
    contact_type: Any
    is_supporter: bool = False
    status: Optional[str] = 'offline'
    gender: Optional[str] = None

    expertise_field: Optional[str] = None
    expertise: Optional[str] = None
    description: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    country: Optional[str] = None
    state: Optional[str] = None
    city: Optional[str] = None
    slogan: Optional[str] = None
    address: Optional[str] = None

    is_banned: bool = False
    banned_reason: Optional[str] = None
    banned_at: Any = None

    is_ai: bool = False
    ai_project: Optional[str] = None
    ai_prompt: Optional[str] = None

    session_duration: int = None
    scheduling: Optional[Dict] = {}
    categories: Optional[List] = []
    timezone: Optional[str] = None
    first_message: Optional[str] = None

    @validator('is_supporter', pre=True, always=True)
    def set_default_is_supporter(cls, v):
        return v if v is not None else False
    
    @validator('scheduling', pre=True, always=True)
    def set_default_scheduling(cls, v):
        if not isinstance(v, dict) or not v:
            return {
                "saturday": [],
                "sunday": [],
                "monday": [],
                "tuesday": [],
                "wednesday": [],
                "thursday": [],
                "friday": []
            }
        return v
    
class Consultant(BaseConsultant):
    availability_status: Optional[str] = 'offline'

    voice_call_count: int = 0
    video_call_count: int = 0
    text_chat_count: int = 0

    updated_at: Any
    created_at: Any

    @validator('languages', 'call_languages', 'contact_type')
    def validate_fields(cls, val):
        if val and type(val) is str:
            # تبدیل رشته به لیست و حذف رشته‌های خالی
            return [item for item in val.split(',') if item.strip()]
        elif val is None or val == '':
            return []
        
        return val
    
    @validator('topics')
    def validate_field_topics(cls, val):
        if val and type(val) is str:
            return [int(item) for item in val.split(',') if item.strip().isdigit()]
        elif val is None or val == '':
            return []
        return val

    @validator('categories', )
    def validate_categories(cls, val):
        if val and type(val) is str:
            return val.split(',')

        return val

    def dict(self, *args, **kwargs):
        data = super().dict(*args, **kwargs)
        # data['topics'] = ",".join(data.get('topics', []))
        # data['languages'] = ",".join(data.get('languages', []))
        # data['contact_type'] = ",".join(data.get('contact_type', []))
        return data


class ConsultantCreateSchema(BaseConsultant):
    @validator('username', pre=True)
    def validate_username(cls, val):
        from models import Consultant

        with db():
            if db.session.query(Consultant).filter(Consultant.username == val).count():
                raise ValueError(f"Consultant with username {val} already exist")

        return val

    @validator('is_banned')
    def validate_is_banned(cls, value, values, config, field):
        return bool(values.get('banned_at'))

    @validator('banned_at')
    def validate_banned_at(cls, value, values, config, field):
        if values.get('banned_reason'):
            return datetime.datetime.now()
        return None


class ConsultantUpdateSchema(BaseConsultant):
    @validator('is_banned')
    def validate_is_banned(cls, value, values, config, field):
        return bool(values.get('banned_at'))

    @validator('banned_at')
    def validate_banned_at(cls, value, values, config, field):
        if values.get('banned_reason'):
            return datetime.datetime.now()
        return None
    
    @validator('topics', 'languages', 'call_languages', 'contact_type', pre=True)
    def convert_empty_list_to_none(cls, value):
        if isinstance(value, list) and not value:  # اگر لیست خالی بود
            return ""
        return value
    

class ConsultantInList(Consultant):
    @validator('username')
    def validate_username(cls, val):
        return val

    class Config:
        validate_assignment = False

class ConsultantDetail(Consultant):
    topics: List[str] 

    class Config:
        validate_assignment = False
        
        
class ConsultantList(BaseModel):
    results: List[ConsultantInList] = ...

    @property
    def count(self):
        return len(self.results)

class TopicList(BaseModel):
    title: str = ...
