from pydantic import BaseModel, validator
from fastapi_sqlalchemy import db


class BanUser(BaseModel):
    username: str = ...
    reason: str = ...

    @validator('username', pre=True)
    def validate_username(cls, val):
        with db():
            from models import User

            if db.session.query(User).filter(User.username == val).count() == 0:
                raise ValueError(f"user with username {val} not exist")

            return val
