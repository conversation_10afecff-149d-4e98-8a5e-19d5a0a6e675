#!/usr/bin/env python3
"""
Test script to analyze current scheduling data structure and test timezone conversion
"""

import asyncio
import json
import sys
import os
from datetime import datetime
import pytz

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.consultant import Consultant
from models import SessionLocal
from schemas.request_types_v2 import GetConsultants
from apps.actions_v2 import ActionHandlerV2
from models.user import User

def analyze_current_scheduling_data():
    """Analyze current scheduling data structure in database"""
    print("=== تحلیل ساختار فعلی scheduling ===")

    with SessionLocal() as session:
        # Get some consultants with scheduling data
        consultants = session.query(Consultant).filter(
            Consultant.scheduling.isnot(None),
            Consultant.visible == True,
            Consultant.is_banned == False
        ).limit(5).all()
        
        print(f"تعداد مشاوران با scheduling: {len(consultants)}")
        
        for consultant in consultants:
            print(f"\n--- مشاور: {consultant.username} ---")
            print(f"Timezone: {consultant.timezone}")
            print(f"Status from schedule: {consultant.status_from_schedule}")
            print(f"Scheduling: {json.dumps(consultant.scheduling, indent=2, ensure_ascii=False)}")
            
            # Test to_dict method
            consultant_dict = consultant.to_dict('en')
            print(f"Scheduling in to_dict: {json.dumps(consultant_dict.get('scheduling', {}), indent=2, ensure_ascii=False)}")

def test_timezone_conversion_logic():
    """Test timezone conversion logic"""
    print("\n=== تست منطق تبدیل timezone ===")
    
    # Sample scheduling data
    sample_scheduling = {
        "monday": ["08:00-12:00", "14:00-18:00"],
        "tuesday": ["09:00-13:00"],
        "wednesday": [],
        "thursday": ["10:00-14:00", "16:00-20:00"],
        "friday": ["08:30-12:30"],
        "saturday": [],
        "sunday": ["09:00-17:00"]
    }
    
    consultant_timezone = "Asia/Tehran"  # UTC+3:30
    client_timezone = "Asia/Dubai"       # UTC+4:00
    
    print(f"مشاور timezone: {consultant_timezone}")
    print(f"کاربر timezone: {client_timezone}")
    print(f"Scheduling اصلی: {json.dumps(sample_scheduling, indent=2, ensure_ascii=False)}")
    
    # Convert scheduling
    converted_scheduling = convert_scheduling_timezone(
        sample_scheduling, 
        consultant_timezone, 
        client_timezone
    )
    
    print(f"Scheduling تبدیل شده: {json.dumps(converted_scheduling, indent=2, ensure_ascii=False)}")

def convert_scheduling_timezone(scheduling, from_timezone, to_timezone):
    """
    Convert scheduling times from consultant's timezone to client's timezone
    
    Args:
        scheduling: Dict with day names as keys and list of time ranges as values
        from_timezone: Consultant's timezone (e.g., 'Asia/Tehran')
        to_timezone: Client's timezone (e.g., 'Asia/Dubai')
    
    Returns:
        Dict with converted scheduling times
    """
    if not scheduling or not isinstance(scheduling, dict):
        return {}
    
    if not from_timezone or not to_timezone:
        return scheduling
    
    try:
        from_tz = pytz.timezone(from_timezone)
        to_tz = pytz.timezone(to_timezone)
    except pytz.exceptions.UnknownTimeZoneError:
        # If timezone is invalid, return original scheduling
        return scheduling
    
    converted_scheduling = {}
    
    # Days of week mapping
    days_order = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    
    for day, time_ranges in scheduling.items():
        if not time_ranges or not isinstance(time_ranges, list):
            converted_scheduling[day] = []
            continue
        
        converted_ranges = []
        
        for time_range in time_ranges:
            if not isinstance(time_range, str) or '-' not in time_range:
                continue
            
            try:
                start_time_str, end_time_str = time_range.split('-')
                
                # Create datetime objects for conversion
                # Use a reference date (e.g., next Monday) to handle day changes
                base_date = datetime(2024, 1, 1)  # A Monday
                day_offset = days_order.index(day.lower())
                current_date = base_date.replace(day=base_date.day + day_offset)
                
                # Parse start and end times
                start_hour, start_minute = map(int, start_time_str.split(':'))
                end_hour, end_minute = map(int, end_time_str.split(':'))
                
                # Create datetime objects in consultant's timezone
                start_dt = from_tz.localize(current_date.replace(hour=start_hour, minute=start_minute))
                end_dt = from_tz.localize(current_date.replace(hour=end_hour, minute=end_minute))
                
                # Convert to client's timezone
                start_converted = start_dt.astimezone(to_tz)
                end_converted = end_dt.astimezone(to_tz)
                
                # Format back to time strings
                converted_start = start_converted.strftime('%H:%M')
                converted_end = end_converted.strftime('%H:%M')
                
                converted_range = f"{converted_start}-{converted_end}"
                converted_ranges.append(converted_range)
                
            except (ValueError, IndexError) as e:
                # If parsing fails, keep original time range
                converted_ranges.append(time_range)
        
        converted_scheduling[day] = converted_ranges
    
    return converted_scheduling

async def test_websocket_response():
    """Test WebSocket response with timezone conversion"""
    print("\n=== تست response WebSocket ===")
    
    # This would require a full WebSocket setup, so we'll simulate it
    print("برای تست کامل WebSocket، نیاز به راه‌اندازی کامل محیط است")
    print("فعلاً فقط منطق تبدیل را تست می‌کنیم")

if __name__ == "__main__":
    print("شروع تست تبدیل timezone برای scheduling")
    
    try:
        # Analyze current data
        analyze_current_scheduling_data()
        
        # Test conversion logic
        test_timezone_conversion_logic()
        
        # Test WebSocket response (simulated)
        asyncio.run(test_websocket_response())
        
        print("\n=== تست کامل شد ===")
        
    except Exception as e:
        print(f"خطا در تست: {e}")
        import traceback
        traceback.print_exc()
