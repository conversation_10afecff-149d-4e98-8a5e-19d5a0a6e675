<div style="text-align: center;">**بسم الله الرحمن الرحیم**</div>

<p align="center">
  <a href="https://fastapi.tiangolo.com"><img src="https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png" alt="FastAPI"></a>
</p>
<p align="center">
    <em>FastAPI framework, high performance, easy to learn, fast to code, ready for production</em>
</p>
<p align="center">
<a href="https://github.com/tiangolo/fastapi/actions?query=workflow%3ATest+event%3Apush+branch%3Amaster" target="_blank">
    <img src="https://github.com/tiangolo/fastapi/workflows/Test/badge.svg?event=push&branch=master" alt="Test">
</a>
<a href="https://codecov.io/gh/tiangolo/fastapi" target="_blank">
    <img src="https://img.shields.io/codecov/c/github/tiangolo/fastapi?color=%2334D058" alt="Coverage">
</a>
<a href="https://pypi.org/project/fastapi" target="_blank">
    <img src="https://img.shields.io/pypi/v/fastapi?color=%2334D058&label=pypi%20package" alt="Package version">
</a>
<a href="https://pypi.org/project/fastapi" target="_blank">
    <img src="https://img.shields.io/pypi/pyversions/fastapi.svg?color=%2334D058" alt="Supported Python versions">
</a>
</p>

# Rahneshan Chat Application (BACKEND)

## Actions

| Action                                     | Description                |
|--------------------------------------------|----------------------------|
| [`new-message`](#send-message)             | send a new message         |
| [`edit-message`](#edit-message)            | edit a message             |
| [`delete-message`](#delete-message)        | delete a message           |
| [`get-history`](#get-messages-list)        | List of discussion message |
| [`get-file [HTTP GET]`](#get-media)        | download file              |
| [`upload-file [HTTP POST]`](#upload-media) | upload file                |

<hr>

#### Send Message

```yaml
{
  'action': 'sendMessage',
  'chat': '', # team:teamID | private:userID
  'text': 'markdown or text',
  'reply_to': 'message_id', # optional
}
```

#### Send Photo

```yaml
{
  'action': 'sendPhoto',
  'chat': '', # team:teamID | private:userID
  'photo': 'file_id',
  'caption': '', # optional
  'reply_to': 'message_id', # optional
}
```

#### Send Video

```yaml
{
  'action': 'sendVideo',
  'chat': '', # team:teamID | private:userID
  'video': 'file_id',
  'caption': '', # optional
  'reply_to': 'message_id', # optional
}
```

#### Send Voice

```yaml
{
  'action': 'sendVoice',
  'chat': '', # team:teamID | private:userID
  'voice': 'file_id',
  'caption': '', # optional
  'reply_to': 'message_id', # optional
}
```

#### Send Document

```yaml
{
  'action': 'sendDocument',
  'chat': '', # team:teamID | private:userID
  'document': 'file_id',
  'caption': '', # optional
  'reply_to': 'message_id', # optional
}
```

#### Edit Message

```yaml
{
  'action': 'editMessage',
  'chat': '', # team:teamID | private:userID
  'message_id': "",
  'text': '',
}
```

#### Edit Message Caption

```yaml
{
  'action': 'editMessageCaption',
  'chat': '', # team:teamID | private:userID
  'message_id': "",
  'caption': '',
}
```

#### Edit Media Message

```yaml
{
  'action': 'editMedia',
  'chat': '', # team:teamID | private:userID
  'message_id': '',
  'media': {
    'type': '', # photo, voice, document
    'url': ''
  },
}
```

#### Delete Message

```yaml
{
  'action': 'deleteMessage',
  'chat': '', # team:teamID | private:userID
  'message_id': "",
}
```

#### Message Response Object

```yaml
{
  'from_user': {
    'username': '',
    'avatar': '',
    'fname': '',
    'lname': ''
  },
  'type': '', # message | video | photo | voice | document
  'message': '<MessageModel>'
}

```

#### Get Chat History

```yaml
{ # each page contains 20 messages
  'action': 'getHistory',
  'page': 1,
},
  #  response
  {
    'count': 1,
    'next': '',
    'prev': '',
    'results': [ "Message Object" ],
  }
```

#### Upload File

```yaml
{ # [HTTP POST]:  /api/chat/upload-file/
  file: '',
} # returns File object
```