"""
Timezone conversion utilities for consultant scheduling
"""

import pytz
from datetime import datetime
from typing import Dict, List, Optional, Union
import logging

logger = logging.getLogger(__name__)

def convert_numeric_timezone_to_string(numeric_timezone: Union[float, int, str]) -> str:
    """
    Convert numeric timezone offset to timezone string
    
    Args:
        numeric_timezone: Numeric timezone offset (e.g., 3.5 for UTC+3:30)
    
    Returns:
        Timezone string (e.g., 'Asia/Tehran')
    """
    if not numeric_timezone:
        return 'UTC'
    
    try:
        offset = float(numeric_timezone)
    except (ValueError, TypeError):
        return 'UTC'
    
    # Common timezone mappings based on offset
    timezone_mappings = {
        0.0: 'UTC',
        1.0: 'Europe/London',  # UTC+1 (could be CET)
        2.0: 'Europe/Berlin',  # UTC+2 (CET)
        3.0: 'Europe/Moscow',  # UTC+3
        3.5: 'Asia/Tehran',    # UTC+3:30
        4.0: 'Asia/Dubai',     # UTC+4
        4.5: 'Asia/Kabul',     # UTC+4:30
        5.0: 'Asia/Karachi',   # UTC+5
        5.5: 'Asia/Kolkata',   # UTC+5:30
        6.0: 'Asia/Dhaka',     # UTC+6
        7.0: 'Asia/Bangkok',   # UTC+7
        8.0: 'Asia/Shanghai',  # UTC+8
        9.0: 'Asia/Tokyo',     # UTC+9
        -5.0: 'America/New_York',  # UTC-5 (EST)
        -8.0: 'America/Los_Angeles',  # UTC-8 (PST)
    }
    
    return timezone_mappings.get(offset, 'UTC')

def convert_scheduling_timezone(
    scheduling: Dict[str, List[str]], 
    from_timezone: Union[str, float, int], 
    to_timezone: str
) -> Dict[str, List[str]]:
    """
    Convert scheduling times from consultant's timezone to client's timezone
    
    Args:
        scheduling: Dict with day names as keys and list of time ranges as values
        from_timezone: Consultant's timezone (string like 'Asia/Tehran' or numeric like 3.5)
        to_timezone: Client's timezone (string like 'Asia/Dubai')
    
    Returns:
        Dict with converted scheduling times
    """
    if not scheduling or not isinstance(scheduling, dict):
        return {}
    
    if not to_timezone:
        return scheduling
    
    # Convert numeric timezone to string if needed
    if isinstance(from_timezone, (int, float)):
        from_timezone_str = convert_numeric_timezone_to_string(from_timezone)
    else:
        from_timezone_str = from_timezone or 'UTC'
    
    try:
        from_tz = pytz.timezone(from_timezone_str)
        to_tz = pytz.timezone(to_timezone)
    except pytz.exceptions.UnknownTimeZoneError as e:
        logger.warning(f"Unknown timezone: {e}. Returning original scheduling.")
        return scheduling
    
    converted_scheduling = {}
    
    # Days of week mapping
    days_order = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    
    for day, time_ranges in scheduling.items():
        if not time_ranges or not isinstance(time_ranges, list):
            converted_scheduling[day] = []
            continue
        
        converted_ranges = []
        
        for time_range in time_ranges:
            if not isinstance(time_range, str):
                continue

            if '-' not in time_range:
                # Keep invalid time ranges as-is
                converted_ranges.append(time_range)
                continue

            try:
                start_time_str, end_time_str = time_range.split('-', 1)
                
                # Create datetime objects for conversion
                # Use a reference date (e.g., next Monday) to handle day changes
                base_date = datetime(2024, 1, 1)  # A Monday
                day_offset = days_order.index(day.lower()) if day.lower() in days_order else 0
                current_date = base_date.replace(day=base_date.day + day_offset)
                
                # Parse start and end times
                start_parts = start_time_str.split(':')
                end_parts = end_time_str.split(':')
                
                if len(start_parts) != 2 or len(end_parts) != 2:
                    converted_ranges.append(time_range)
                    continue
                
                start_hour, start_minute = int(start_parts[0]), int(start_parts[1])
                end_hour, end_minute = int(end_parts[0]), int(end_parts[1])
                
                # Handle 24:00 as 00:00 of next day
                if start_hour == 24:
                    start_hour = 0
                if end_hour == 24:
                    end_hour = 0
                
                # Create datetime objects in consultant's timezone
                start_dt = from_tz.localize(current_date.replace(hour=start_hour, minute=start_minute))
                end_dt = from_tz.localize(current_date.replace(hour=end_hour, minute=end_minute))
                
                # Convert to client's timezone
                start_converted = start_dt.astimezone(to_tz)
                end_converted = end_dt.astimezone(to_tz)
                
                # Format back to time strings
                converted_start = start_converted.strftime('%H:%M')
                converted_end = end_converted.strftime('%H:%M')
                
                converted_range = f"{converted_start}-{converted_end}"
                converted_ranges.append(converted_range)
                
            except (ValueError, IndexError) as e:
                logger.warning(f"Error parsing time range '{time_range}': {e}")
                # If parsing fails, keep original time range
                converted_ranges.append(time_range)
        
        converted_scheduling[day] = converted_ranges
    
    return converted_scheduling


def should_consultant_be_online_by_schedule(consultant, client_timezone_str):
    """
    Check if consultant should be online based on their scheduling configuration.

    Args:
        consultant: Consultant object with status_from_schedule and scheduling fields
        client_timezone_str: Client timezone string (e.g., 'Asia/Tehran')

    Returns:
        bool: True if consultant should be online according to their schedule, False otherwise

    Note:
        - Project timezone is always Asia/Tehran (UTC+3.5)
        - All scheduling times are stored based on Iran time
        - Only client timezone is used for conversion
    """
    # If status_from_schedule is not enabled, don't change status
    if not consultant.status_from_schedule:
        return False

    # Project timezone is always Asia/Tehran
    project_timezone_str = 'Asia/Tehran'

    # Get current time in client timezone and convert to project timezone (Iran time)
    try:
        client_tz = pytz.timezone(client_timezone_str)
        project_tz = pytz.timezone(project_timezone_str)

        # Get current time in client timezone
        current_time_client = datetime.now(client_tz)

        # Convert to project timezone (Iran time)
        current_time_iran = current_time_client.astimezone(project_tz)

    except Exception:
        # If timezone conversion fails, use Iran time directly
        iran_tz = pytz.timezone('Asia/Tehran')
        current_time_iran = datetime.now(iran_tz)

    # Get current day name in lowercase (e.g., 'monday', 'tuesday')
    current_day = current_time_iran.strftime('%A').lower()
    current_time_str = current_time_iran.strftime('%H:%M')

    # Get scheduling for current day
    scheduling = consultant.scheduling or {}
    day_schedule = scheduling.get(current_day, [])

    # Check if current time falls within any scheduled time slot
    for time_slot in day_schedule:
        if '-' not in time_slot:
            continue

        try:
            start_time_str, end_time_str = time_slot.split('-')
            start_time = datetime.strptime(start_time_str, '%H:%M').time()
            end_time = datetime.strptime(end_time_str, '%H:%M').time()
            current_time = datetime.strptime(current_time_str, '%H:%M').time()

            # Handle cases where end time is before start time (crosses midnight)
            if end_time < start_time:
                # Time slot crosses midnight
                if current_time >= start_time or current_time <= end_time:
                    return True
            else:
                # Normal time slot within same day
                if start_time <= current_time <= end_time:
                    return True

        except ValueError:
            # Skip invalid time format
            continue

    return False


def test_timezone_conversion():
    """Test function for timezone conversion"""
    
    # Test cases
    test_cases = [
        {
            'name': 'Tehran to Dubai',
            'scheduling': {
                "monday": ["08:00-12:00", "14:00-18:00"],
                "tuesday": ["09:00-13:00"],
                "wednesday": [],
                "thursday": ["10:00-14:00", "16:00-20:00"],
                "friday": ["08:30-12:30"],
                "saturday": [],
                "sunday": ["09:00-17:00"]
            },
            'from_tz': 3.5,  # Tehran (numeric)
            'to_tz': 'Asia/Dubai'
        },
        {
            'name': 'String timezone test',
            'scheduling': {
                "monday": ["15:00-19:00"],
                "tuesday": ["15:00-19:00"]
            },
            'from_tz': 'Asia/Tehran',
            'to_tz': 'Asia/Dubai'
        },
        {
            'name': 'Invalid timezone test',
            'scheduling': {
                "monday": ["15:00-19:00"]
            },
            'from_tz': 'Invalid/Timezone',
            'to_tz': 'Asia/Dubai'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        print(f"Original: {test_case['scheduling']}")
        
        converted = convert_scheduling_timezone(
            test_case['scheduling'],
            test_case['from_tz'],
            test_case['to_tz']
        )
        
        print(f"Converted: {converted}")

if __name__ == "__main__":
    test_timezone_conversion()
