import asyncio
import json
import logging
import websockets
from typing import Optional, Dict, Any
from websockets.exceptions import ConnectionClosed, WebSocketException
from pydantic import ValidationError
from settings import config

logger = logging.getLogger(__name__)


class WebSocketHealthChecker:
    """
    WebSocket client for health checking the consultant list functionality.
    
    This client connects to the WebSocket server, authenticates, sends a getConsultants
    request, and validates the response to ensure the WebSocket service is working properly.
    """
    
    def __init__(self, 
                 websocket_url: str = None, 
                 auth_token: str = None,
                 timeout: int = 90):
        """
        Initialize the WebSocket health checker.
        
        Args:
            websocket_url: WebSocket URL to connect to
            auth_token: Authentication token for WebSocket connection
            timeout: Connection and operation timeout in seconds
        """
        self.websocket_url = self._get_default_websocket_url()
        self.auth_token = self._get_default_auth_token()
        self.timeout = timeout
        self.websocket = None
        
    def _get_default_websocket_url(self) -> str:
        """Get default WebSocket URL from configuration or environment."""
        # Always use qa.habibapp.com for WebSocket connections
        base_url = 'qa.habibapp.com'

        # If localhost or development environment, use local WebSocket
        if 'localhost' in base_url or '127.0.0.1' in base_url:
            protocol = 'ws'
            return f"{protocol}://{base_url}/ws/"
        else:
            # Production environment - always use qa.habibapp.com
            protocol = 'wss' if '.com' in base_url else 'ws'
            return f"{protocol}://{base_url}/ws/"
        
    def _get_default_auth_token(self) -> str:
        """Get default authentication token from configuration."""
        # Use a test token or get from environment
        return '516e059d2a6b31d74b6a9e4c8f98fe4e8413efbc'
        
    async def connect(self) -> bool:
        """
        Establish WebSocket connection with authentication.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Construct WebSocket URL with authentication parameters
            # Based on the codebase analysis, use version=3 and language=az as specified in requirements
            url = f"{self.websocket_url}?version=3&t={self.auth_token}&language=az"
            
            logger.info(f"Connecting to WebSocket: {url}")
            
            # Connect with timeout
            self.websocket = await asyncio.wait_for(
                websockets.connect(url),
                timeout=self.timeout
            )
            
            # Wait for initial authentication response
            auth_response = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=self.timeout
            )
            
            logger.info(f"Authentication response received: {auth_response}")
            
            # Parse and validate authentication response
            try:
                auth_data = json.loads(auth_response)
                if auth_data.get('result') is False:
                    logger.error(f"Authentication failed: {auth_data.get('error', 'Unknown error')}")
                    return False
            except json.JSONDecodeError:
                logger.warning("Could not parse authentication response as JSON")
            
            logger.info("WebSocket connection established successfully")
            return True
            
        except asyncio.TimeoutError:
            logger.error(f"WebSocket connection timeout after {self.timeout} seconds")
            return False
        except ConnectionClosed as e:
            logger.error(f"WebSocket connection closed during handshake: {e}")
            return False
        except WebSocketException as e:
            logger.error(f"WebSocket error during connection: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during WebSocket connection: {e}")
            return False
            
    async def send_get_consultants_request(self) -> Optional[Dict[Any, Any]]:
        """
        Send getConsultants request and wait for response.
        
        Returns:
            Dict containing the response data, or None if failed
        """
        if not self.websocket:
            logger.error("WebSocket not connected")
            return None
            
        try:
            # Prepare getConsultants request based on requirements
            request_data = {
                "action": "getConsultants",
                "language_code": "ar,ur,az",
                "timezone": "Asia/Tehran",
                "only_interacted": False,
                "search": None
            }
            
            logger.info(f"Sending getConsultants request: {request_data}")
            
            # Send request
            await asyncio.wait_for(
                self.websocket.send(json.dumps(request_data)),
                timeout=self.timeout
            )
            
            # Wait for response
            response = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=self.timeout
            )
            
            logger.info(f"Received response: {response}")  # Log full response for debugging
            
            # Parse response
            try:
                response_data = json.loads(response)
                return response_data
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse response as JSON: {e}")
                return None
                
        except asyncio.TimeoutError:
            logger.error(f"Timeout waiting for getConsultants response after {self.timeout} seconds")
            return None
        except ConnectionClosed as e:
            logger.error(f"WebSocket connection closed during request: {e}")
            return None
        except WebSocketException as e:
            logger.error(f"WebSocket error during request: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during getConsultants request: {e}")
            return None
            
    def validate_consultants_response(self, response_data: Dict[Any, Any]) -> bool:
        """
        Validate that the response matches the expected consultant list schema.

        Uses both basic validation and Pydantic schema validation for robust checking.

        Args:
            response_data: The response data to validate

        Returns:
            bool: True if response is valid, False otherwise
        """
        try:
            logger.info("Starting response validation...")
            logger.info(f"Response type: {type(response_data)}")
            logger.info(f"Response keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")

            # Basic structure validation
            if not isinstance(response_data, dict):
                logger.error("Response is not a dictionary")
                return False

            # Check for required fields based on ConsultantsListResponse schema
            act_value = response_data.get('act')
            logger.info(f"Response 'act' field: '{act_value}'")
            if act_value != 'consultantList':
                logger.error(f"Invalid action in response: '{act_value}' (expected 'consultantList')")
                return False

            results = response_data.get('results')
            logger.info(f"Results type: {type(results)}, length: {len(results) if isinstance(results, list) else 'Not a list'}")
            if not isinstance(results, list):
                logger.error("Response results is not a list")
                return False

            # Validate each consultant in the results
            logger.info(f"Validating {len(results)} consultants...")
            for i, consultant in enumerate(results):
                if not isinstance(consultant, dict):
                    logger.error(f"Consultant {i} is not a dictionary")
                    return False

                # Check required fields for each consultant
                required_fields = ['act', 'username']
                for field in required_fields:
                    if field not in consultant:
                        logger.error(f"Consultant {i} missing required field: {field}")
                        return False

                # Validate consultant action
                consultant_act = consultant.get('act')
                if consultant_act != 'consultant':
                    logger.error(f"Consultant {i} has invalid act: '{consultant_act}' (expected 'consultant')")
                    return False

                logger.debug(f"Consultant {i} basic validation passed: {consultant.get('username')}")

            logger.info("Basic validation passed, attempting Pydantic validation...")

            # Advanced validation using Pydantic schemas
            try:
                from schemas.response_types_v2 import ConsultantsListResponse
                logger.info("Successfully imported ConsultantsListResponse schema")

                # Validate the entire response using Pydantic schema
                logger.info("Attempting Pydantic validation...")
                validated_response = ConsultantsListResponse(**response_data)
                logger.info(f"Pydantic validation successful. Found {len(validated_response.results)} consultants")

                # Additional validation checks
                for i, consultant in enumerate(validated_response.results):
                    # Check that username is not empty
                    if not consultant.username or consultant.username.strip() == '':
                        logger.error(f"Consultant {i} has empty username")
                        return False

                    # Check that required fields have valid values
                    if not hasattr(consultant, 'fullname'):
                        logger.warning(f"Consultant {i} missing fullname field")

                logger.info("All validation checks passed successfully!")
                return True

            except ValidationError as e:
                logger.error(f"Pydantic validation failed with ValidationError:")
                logger.error(f"Error details: {e}")
                logger.error(f"Error count: {len(e.errors())}")
                for error in e.errors():
                    logger.error(f"  - Field: {error.get('loc')}, Error: {error.get('msg')}, Value: {error.get('input')}")
                return False
            except ImportError as e:
                logger.warning(f"Could not import Pydantic schemas, using basic validation: {e}")
                # Fall back to basic validation which already passed
                logger.info(f"Basic validation successful. Found {len(results)} consultants")
                return True
            except Exception as e:
                logger.error(f"Unexpected error during Pydantic validation: {e}")
                logger.error(f"Error type: {type(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return False

        except Exception as e:
            logger.error(f"Error during response validation: {e}")
            logger.error(f"Error type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
            
    async def disconnect(self):
        """Close the WebSocket connection."""
        if self.websocket:
            try:
                await self.websocket.close()
                logger.info("WebSocket connection closed")
            except Exception as e:
                logger.error(f"Error closing WebSocket connection: {e}")
            finally:
                self.websocket = None

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform complete health check of WebSocket consultant list functionality.

        Returns:
            Dict containing health check results with status and details
        """
        result = {
            'status': 'failed',
            'connected': False,
            'request_sent': False,
            'response_received': False,
            'response_valid': False,
            'error': None,
            'consultant_count': 0,
            'response_time_ms': 0
        }

        start_time = asyncio.get_event_loop().time()

        try:
            # Step 1: Connect to WebSocket
            if not await self.connect():
                result['error'] = 'Failed to connect to WebSocket'
                return result

            result['connected'] = True

            # Step 2: Send getConsultants request
            response_data = await self.send_get_consultants_request()
            if response_data is None:
                result['error'] = 'Failed to get response from getConsultants request'
                return result

            result['request_sent'] = True
            result['response_received'] = True

            # Step 3: Validate response
            if not self.validate_consultants_response(response_data):
                result['error'] = 'Response validation failed'
                return result

            result['response_valid'] = True
            result['consultant_count'] = len(response_data.get('results', []))

            # Calculate response time
            end_time = asyncio.get_event_loop().time()
            result['response_time_ms'] = int((end_time - start_time) * 1000)

            result['status'] = 'success'
            logger.info(f"Health check completed successfully in {result['response_time_ms']}ms")

        except Exception as e:
            result['error'] = f'Unexpected error during health check: {str(e)}'
            logger.error(f"Health check failed: {e}")

        finally:
            await self.disconnect()

        return result


async def perform_websocket_health_check(websocket_url: str = None,
                                       auth_token: str = None,
                                       timeout: int = 30) -> Dict[str, Any]:
    """
    Convenience function to perform a WebSocket health check.

    Args:
        websocket_url: WebSocket URL to connect to
        auth_token: Authentication token
        timeout: Operation timeout in seconds

    Returns:
        Dict containing health check results
    """
    checker = WebSocketHealthChecker(websocket_url, auth_token, timeout)
    return await checker.health_check()
