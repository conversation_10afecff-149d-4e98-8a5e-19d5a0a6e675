from models import *
from sqlalchemy import extract, func
from fastapi_sqlalchemy import db

def consultant_activity_chart2(consultant_id=None):
    labels = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ]

    with db():
        # محاسبه تعداد کل روم‌ها برای هر ماه
        if consultant_id:
            total_rooms_query = db.session.query(
                extract('month', Room.created_at).label('month'),
                func.count(Room.id).label('count')
            ).filter(Room.consultant_id == consultant_id).group_by('month').all()
        else:
            total_rooms_query = db.session.query(
                extract('month', Room.created_at).label('month'),
                func.count(Room.id).label('count')
            ).group_by('month').all()

        # محاسبه تعداد روم‌های پاسخ داده شده برای هر ماه
        if consultant_id:
            answered_rooms_query = db.session.query(
                extract('month', Room.created_at).label('month'),
                func.count(Room.id).label('count')
            ).join(Message).filter(
                Room.consultant_id == consultant_id,
                Message.by_user_type == 'Consultant'
            ).group_by('month').all()
        else:
            answered_rooms_query = db.session.query(
                extract('month', Room.created_at).label('month'),
                func.count(Room.id).label('count')
            ).join(Message).filter(
                Message.by_user_type == 'Consultant'
            ).group_by('month').all()

        total_rooms_data = [0 for _ in range(len(labels))]
        answered_rooms_data = [0 for _ in range(len(labels))]

        for month, count in total_rooms_query:
            total_rooms_data[int(month) - 1] = count

        for month, count in answered_rooms_query:
            answered_rooms_data[int(month) - 1] = count

    return {
        'labels': labels,
        'consultant_questions': total_rooms_data,
        'consultant_answers': answered_rooms_data,
    }
def consultant_activity_chart(consultant=None):
    labels = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ]

    with db():
        if consultant:
            rows = db.session.query(extract('month', Room.created_at).label('month'),
                            func.count(Room.id).label('count')).group_by(
                'month').filter(Room.consultant_id == 1).all()
        else:
            rows = db.session.query(extract('month', Room.created_at).label('month'),
                            func.count(Room.id).label('count')).group_by(
                'month').filter(Room.consultant_id == 1).all()

        chart_data = [0 for _ in range(len(labels))]
        for month, count in rows:
            chart_data.insert(int(month), int(count))

    return {
        'labels': labels,
        'consultant_answers': chart_data,
    }

def activity_chart():
    labels = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ]

    with db():
        rows = db.session.query(extract('month', Room.created_at).label('month'),
                        func.count(Room.id).label('count')).group_by(
            'month').all()

        chart_data = [0 for _ in range(len(labels))]
        for month, count in rows:
            chart_data[int(month) - 1] = int(count)  # اینجا از "insert" به "assign" تغییر داده شده

    return {
        'labels': labels,
        'consultant_answers': chart_data,
    }