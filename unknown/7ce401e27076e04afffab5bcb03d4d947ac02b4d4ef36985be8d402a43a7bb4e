import datetime

from sqlalchemy import Column, String, DateTime, <PERSON>teger, Foreign<PERSON>ey, <PERSON>olean
from sqlalchemy.orm import relationship

from models import Base
from fastapi_sqlalchemy import db


class Room(Base):
    __tablename__ = "rooms"

    STATUS = (
        ('o', 'open'),
        ('c', 'closed'),
    )
    id = Column(Integer, primary_key=True, index=True)
    subject = Column(String, nullable=True)

    client_id = Column(Integer, ForeignKey("users.id"), index=True)
    consultant_id = Column(Integer, ForeignKey("consultants.id"), index=True)
    client = relationship("User", backref='rooms', lazy='joined')
    consultant = relationship("Consultant", backref='rooms', lazy='joined')

    created_at = Column(DateTime, default=datetime.datetime.now)
    closed_at = Column(DateTime, nullable=True)
    status = Column(String, default='o', )
    reason = Column(String, )
    is_public = Column(Boolean, default=False)
    messages = relationship("Message", back_populates='room', cascade="all, delete")

    room_type = Column(String, default='chat')  # video, voice, chat

    def get_room_msg_display(self):
        from models import Message
        with db():
            msg = db.session.query(Message).filter(
                Message.room_id == self.id,
            ).order_by(Message.id.desc()).first()
            if msg:
                return msg.to_dict()

        return {}

    def get_session_duration(self):
        if self.created_at and self.closed_at:
            duration = self.closed_at - self.created_at
            days, seconds = duration.days, duration.seconds
            hours = days * 24 + seconds // 3600
            minutes = (seconds % 3600) // 60

            if days > 0:
                return f"{days} days, {hours} hours, {minutes} minutes"
            elif hours > 0:
                return f"{hours} hours, {minutes} minutes"
            elif minutes:
                return f"{minutes} minutes"
            elif seconds:
                return f"{seconds} seconds"
        else:
            return "open"


    def can_user_send_message(self):
        """
        Check if client can send message in this room
        
        Returns:
            tuple: (bool, str) - (can_send, reason)
        """
        from services.subscription_service import SubscriptionService
        return SubscriptionService.can_send_message_in_room(self.id)
    
    def record_message_sent(self):
        """
        Record that a message was sent in this room
        
        Returns:
            bool: True if message was recorded, False otherwise
        """
        from services.subscription_service import SubscriptionService
        return SubscriptionService.record_message_sent(self.client_id, self.consultant_id)
    
    def to_dict(self):
        # Check if user can send message in this room
        # can_send, reason = self.can_user_send_message()
        
        return {
            'id': str(self.id),
            'subject': self.subject,
            'room_type': self.room_type or 'chat',
            'client': self.client.to_dict(),
            'consultant': self.consultant.to_dict() if self.consultant else None,
            'created_at': str(self.created_at),
            'status': 'open' if self.status == 'o' else 'closed',
            'is_public': self.is_public,
            'last_message': self.get_room_msg_display(),
            'session_duration': self.get_session_duration(),
            # 'can_send_message': can_send,
            # 'message_status_reason': reason
        }
