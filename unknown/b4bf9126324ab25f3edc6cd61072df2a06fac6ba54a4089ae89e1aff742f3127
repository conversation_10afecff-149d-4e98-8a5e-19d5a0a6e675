"""Initial migration

Revision ID: 1c21fe919ecc
Revises: c5bcf7e4c825
Create Date: 2024-06-07 17:25:12.997639

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1c21fe919ecc'
down_revision: Union[str, None] = 'c5bcf7e4c825'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
