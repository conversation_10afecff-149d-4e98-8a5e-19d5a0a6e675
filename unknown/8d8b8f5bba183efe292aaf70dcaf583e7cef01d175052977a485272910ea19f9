import datetime
import json
import os
import pytz
import requests
import logging
from urllib.parse import urlencode
from typing import List, Dict, Optional, Tuple
from sqlalchemy import and_, or_, func
from fastapi_sqlalchemy import db

from models.calls import Call
from models.consultant import Consultant
from models.user import User

# Set up logging
logger = logging.getLogger(__name__)


class ReservationService:
    """
    سرویس مدیریت رزرواسیون تماس‌ها
    
    این کلاس تمام عملیات مربوط به رزرواسیون تماس‌ها را مدیریت می‌کند.
    """
    
    @staticmethod
    def calculate_reservation_slots(consultant_id: int, client_timezone: str = 'UTC') -> Dict:
        """
        محاسبه بازه‌های زمانی قابل رزرو برای دو روز آینده
        
        Args:
            consultant_id: شناسه مشاور
            client_timezone: منطقه زمانی کاربر
            
        Returns:
            Dict: دیکشنری شامل بازه‌های زمانی قابل رزرو برای دو روز آینده
        """
        try:
            with db():
                # دریافت اطلاعات مشاور
                consultant = db.session.query(Consultant).filter(Consultant.id == consultant_id).first()
                
                if not consultant or not consultant.scheduling:
                    return {"error": "مشاور یافت نشد یا برنامه زمانی ندارد"}
                    
                # تبدیل scheduling از رشته به دیکشنری اگر لازم باشد
                scheduling = consultant.scheduling
                if isinstance(scheduling, str):
                    try:
                        scheduling = json.loads(scheduling)
                    except:
                        scheduling = {
                            "saturday": [], "sunday": [], "monday": [], 
                            "tuesday": [], "wednesday": [], "thursday": [], "friday": []
                        }
                
                # مدت زمان هر جلسه (به دقیقه)
                session_duration = consultant.session_duration or 20  # پیش‌فرض 20 دقیقه
                
                # محاسبه دو روز آینده
                today = datetime.datetime.now(pytz.timezone(client_timezone))
                tomorrow = today + datetime.timedelta(days=1)
                day_after_tomorrow = today + datetime.timedelta(days=2)
                
                days_to_check = [
                    (tomorrow.strftime("%A").lower(), tomorrow.date()),
                    (day_after_tomorrow.strftime("%A").lower(), day_after_tomorrow.date())
                ]
                
                result = {}
                
                for day_name, date_obj in days_to_check:
                    available_slots = []
                    
                    # بررسی آیا این روز در برنامه زمانی مشاور وجود دارد
                    if day_name in scheduling and scheduling[day_name]:
                        # برای هر بازه زمانی در این روز
                        for time_range in scheduling[day_name]:
                            start_time_str, end_time_str = time_range.split('-')
                            
                            # تبدیل زمان‌ها به datetime
                            start_time = datetime.datetime.strptime(start_time_str, "%H:%M").time()
                            end_time = datetime.datetime.strptime(end_time_str, "%H:%M").time()
                            
                            # ساخت datetime کامل با تاریخ روز مورد نظر
                            start_datetime = datetime.datetime.combine(date_obj, start_time)
                            end_datetime = datetime.datetime.combine(date_obj, end_time)
                            
                            # محاسبه تعداد اسلات‌های زمانی بر اساس مدت جلسه
                            current_slot = start_datetime
                            while current_slot + datetime.timedelta(minutes=session_duration) <= end_datetime:
                                slot_end = current_slot + datetime.timedelta(minutes=session_duration)
                                
                                # بررسی آیا این اسلات قبلاً رزرو شده است
                                is_reserved = db.session.query(Call).filter(
                                    and_(
                                        Call.consultant_id == consultant_id,
                                        Call.is_reservation == True,
                                        Call.status.in_(['unconfirmed', 'confirmed']),
                                        Call.start_time <= slot_end,
                                        Call.end_time >= current_slot
                                    )
                                ).first() is not None
                                
                                if not is_reserved:
                                    # اضافه کردن اسلات به لیست اسلات‌های در دسترس
                                    available_slots.append({
                                        "start": current_slot.strftime("%H:%M"),
                                        "end": slot_end.strftime("%H:%M"),
                                        "date": date_obj.strftime("%Y-%m-%d")
                                    })
                                
                                # حرکت به اسلات بعدی
                                current_slot = slot_end
                                
                                # محدود کردن به حداکثر 6 اسلات برای هر روز
                                if len(available_slots) >= 6:
                                    break
                    
                    # اضافه کردن اسلات‌های این روز به نتیجه نهایی
                    result[date_obj.strftime("%Y-%m-%d")] = available_slots
                
                return result
                
        except Exception as e:
            logger.error(f"Error calculating reservation slots: {e}")
            return {"error": str(e)}
    
    @staticmethod
    def get_available_slots(consultant_id: int, client_timezone: str = 'UTC') -> Dict:
        """
        دریافت بازه‌های زمانی قابل رزرو برای دو روز آینده
        
        Args:
            consultant_id: شناسه مشاور
            client_timezone: منطقه زمانی کاربر
            
        Returns:
            Dict: دیکشنری شامل بازه‌های زمانی قابل رزرو برای دو روز آینده
        """
        return ReservationService.calculate_reservation_slots(consultant_id, client_timezone)
    
    @staticmethod
    def create_reservation(
        client_id: int, 
        consultant_id: int, 
        start_time: str, 
        call_type: str, 
        client_timezone: str = 'UTC'
    ) -> Tuple[bool, str, Optional[int]]:
        """
        ایجاد یک رزرواسیون جدید
        
        Args:
            client_id: شناسه کاربر
            consultant_id: شناسه مشاور
            start_time: زمان شروع (فرمت: YYYY-MM-DD HH:MM)
            call_type: نوع تماس ('voice' یا 'video')
            client_timezone: منطقه زمانی کاربر
            
        Returns:
            Tuple[bool, str, Optional[int]]: (موفقیت، پیام، شناسه رزرواسیون)
        """
        try:
            with db():
                # دریافت اطلاعات مشاور
                consultant = db.session.query(Consultant).filter(Consultant.id == consultant_id).first()
                if not consultant:
                    return False, "مشاور یافت نشد", None
                
                # دریافت اطلاعات کاربر
                user = db.session.query(User).filter(User.id == client_id).first()
                if not user:
                    return False, "کاربر یافت نشد", None
                
                # تبدیل زمان شروع به datetime
                try:
                    start_datetime = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M")
                    # تبدیل به timezone کاربر
                    start_datetime = pytz.timezone(client_timezone).localize(start_datetime)
                except ValueError:
                    return False, "فرمت زمان نامعتبر است", None
                
                # محاسبه زمان پایان بر اساس مدت جلسه
                session_duration = consultant.session_duration or 20  # پیش‌فرض 20 دقیقه
                end_datetime = start_datetime + datetime.timedelta(minutes=session_duration)
                
                # بررسی آیا این زمان قبلاً رزرو شده است
                existing_reservation = db.session.query(Call).filter(
                    and_(
                        Call.consultant_id == consultant_id,
                        Call.is_reservation == True,
                        Call.status.in_(['unconfirmed', 'confirmed']),
                        Call.start_time <= end_datetime,
                        Call.end_time >= start_datetime
                    )
                ).first()
                
                if existing_reservation:
                    return False, "این زمان قبلاً رزرو شده است", None
                
                # محاسبه هزینه بر اساس نوع تماس
                cost = consultant.video_call_cost if call_type == 'video' else consultant.voice_call_cost
                if not cost:
                    return False, "هزینه تماس برای این مشاور تعریف نشده است", None
                
                # ایجاد رزرواسیون جدید
                new_reservation = Call(
                    consultant_id=consultant_id,
                    client_id=client_id,
                    start_time=start_datetime,
                    end_time=end_datetime,
                    call_type=call_type,
                    cost=cost,
                    status='unconfirmed',
                    is_reservation=True,
                    reservation_date=datetime.datetime.now(pytz.UTC)
                )
                
                db.session.add(new_reservation)
                db.session.commit()
                
                return True, "رزرواسیون با موفقیت ایجاد شد", new_reservation.id
                
        except Exception as e:
            logger.error(f"Error creating reservation: {e}")
            return False, f"خطا در ایجاد رزرواسیون: {str(e)}", None
    
    @staticmethod
    def cancel_reservation(reservation_id: int, user_id: int) -> Tuple[bool, str]:
        """
        لغو یک رزرواسیون
        
        Args:
            reservation_id: شناسه رزرواسیون
            user_id: شناسه کاربر (برای تأیید مالکیت)
            
        Returns:
            Tuple[bool, str]: (موفقیت، پیام)
        """
        try:
            with db():
                # دریافت اطلاعات رزرواسیون
                reservation = db.session.query(Call).filter(
                    and_(
                        Call.id == reservation_id,
                        Call.is_reservation == True,
                        Call.client_id == user_id
                    )
                ).first()
                
                if not reservation:
                    return False, "رزرواسیون یافت نشد یا شما مجاز به لغو آن نیستید"
                
                # بررسی وضعیت رزرواسیون
                if reservation.status == 'completed':
                    return False, "این رزرواسیون قبلاً انجام شده و قابل لغو نیست"
                
                # لغو رزرواسیون
                reservation.status = 'cancelled'
                db.session.commit()
                
                return True, "رزرواسیون با موفقیت لغو شد"
                
        except Exception as e:
            logger.error(f"Error cancelling reservation: {e}")
            return False, f"خطا در لغو رزرواسیون: {str(e)}"
    
    @staticmethod
    def confirm_reservation(reservation_id: int, consultant_id: int) -> Tuple[bool, str]:
        """
        تأیید یک رزرواسیون توسط مشاور
        
        Args:
            reservation_id: شناسه رزرواسیون
            consultant_id: شناسه مشاور (برای تأیید مالکیت)
            
        Returns:
            Tuple[bool, str]: (موفقیت، پیام)
        """
        try:
            with db():
                # دریافت اطلاعات رزرواسیون
                reservation = db.session.query(Call).filter(
                    and_(
                        Call.id == reservation_id,
                        Call.is_reservation == True,
                        Call.consultant_id == consultant_id
                    )
                ).first()
                
                if not reservation:
                    return False, "رزرواسیون یافت نشد یا شما مجاز به تأیید آن نیستید"
                
                # بررسی وضعیت رزرواسیون
                if reservation.status != 'unconfirmed':
                    return False, f"این رزرواسیون در وضعیت {reservation.status} است و قابل تأیید نیست"
                
                # تأیید رزرواسیون
                reservation.status = 'confirmed'
                db.session.commit()
                
                return True, "رزرواسیون با موفقیت تأیید شد"
                
        except Exception as e:
            logger.error(f"Error confirming reservation: {e}")
            return False, f"خطا در تأیید رزرواسیون: {str(e)}"
    
    @staticmethod
    def get_user_reservations(user_id: int) -> List[Dict]:
        """
        دریافت لیست تماس‌های رزرو شده توسط کاربر
        
        Args:
            user_id: شناسه کاربر
            
        Returns:
            List[Dict]: لیست تماس‌های رزرو شده
        """
        try:
            with db():
                # دریافت رزروهای کاربر
                reservations = db.session.query(Call).filter(
                    and_(
                        Call.client_id == user_id,
                        Call.is_reservation == True
                    )
                ).order_by(Call.start_time).all()
                
                result = []
                for reservation in reservations:
                    # دریافت اطلاعات مشاور
                    consultant = db.session.query(Consultant).filter(Consultant.id == reservation.consultant_id).first()
                    
                    result.append({
                        "id": reservation.id,
                        "consultant_id": consultant.id if consultant else None,
                        "consultant_name": consultant.fullname if consultant else "Unknown",
                        "consultant_username": consultant.username if consultant else None,
                        "consultant_avatar": consultant.avatar_url if consultant else None,
                        "start_time": reservation.start_time.strftime("%Y-%m-%d %H:%M"),
                        "end_time": reservation.end_time.strftime("%Y-%m-%d %H:%M") if reservation.end_time else None,
                        "call_type": reservation.call_type,
                        "cost": reservation.cost,
                        "status": reservation.status,
                    })
                    
                return result
                
        except Exception as e:
            logger.error(f"Error getting user reservations: {e}")
            return []
    
    @staticmethod
    def get_consultant_reservations(consultant_id: int, date_filter: str = None) -> List[Dict]:
        """
        دریافت لیست تماس‌های رزرو شده برای مشاور
        
        Args:
            consultant_id: شناسه مشاور
            date_filter: فیلتر تاریخ (week, month, three_months, six_months, year)
            
        Returns:
            List[Dict]: لیست تماس‌های رزرو شده
        """
        try:
            # ایجاد فیلتر پایه
            base_filter = and_(
                Call.consultant_id == consultant_id,
                Call.is_reservation == True
            )
            
            # اضافه کردن فیلتر تاریخ اگر مشخص شده باشد
            if date_filter:
                now = datetime.datetime.now(pytz.UTC)
                filter_date = None
                
                if date_filter == 'week':
                    filter_date = now - datetime.timedelta(days=7)
                elif date_filter == 'month':
                    filter_date = now - datetime.timedelta(days=30)
                elif date_filter == 'three_months':
                    filter_date = now - datetime.timedelta(days=90)
                elif date_filter == 'six_months':
                    filter_date = now - datetime.timedelta(days=180)
                elif date_filter == 'year':
                    filter_date = now - datetime.timedelta(days=365)
                
                if filter_date:
                    # فیلتر بر اساس تاریخ ایجاد رزرو (reservation_date)
                    # اگر reservation_date خالی باشد، از start_time استفاده می‌کنیم
                    base_filter = and_(
                        base_filter,
                        or_(
                            and_(Call.reservation_date != None, Call.reservation_date >= filter_date),
                            and_(Call.reservation_date == None, Call.start_time >= filter_date)
                        )
                    )
            
            with db():
                # دریافت رزروهای مشاور
                reservations = db.session.query(Call).filter(base_filter).order_by(Call.start_time).all()
                
                result = []
                for reservation in reservations:
                    # دریافت اطلاعات کاربر
                    user = db.session.query(User).filter(User.id == reservation.client_id).first()
                    
                    result.append({
                        "id": reservation.id,
                        "client_name": user.fullname if user else "Unknown",
                        "client_id": reservation.client_id,
                        "client_avatar": user.avatar_url if user else None,
                        "start_time": reservation.start_time.strftime("%Y-%m-%d %H:%M"),
                        "end_time": reservation.end_time.strftime("%Y-%m-%d %H:%M") if reservation.end_time else None,
                        "call_type": reservation.call_type,
                        "cost": reservation.cost,
                        "status": reservation.status,
                        "duration": (reservation.end_time - reservation.start_time).total_seconds() // 60 if reservation.end_time else None
                    })
                    
                return result
                
        except Exception as e:
            logger.error(f"Error getting consultant reservations: {e}")
            return []
            
    @staticmethod
    def process_reservation_payment(user, cost, consultant_id, call_type):
        """
        پردازش پرداخت برای رزرو تماس
        
        Args:
            user: آبجکت کاربر
            cost: هزینه تماس
            consultant_id: شناسه مشاور
            call_type: نوع تماس ('voice' یا 'video')
            
        Returns:
            tuple: (bool, str) - (موفقیت، پیام)
        """
        # پردازش پرداخت
        url = "https://habibapp.com/habcoin/pay/"
        username = user.username.split(':')[0] if ':' in user.username else user.username
        params = {
            "username": username,
            "amount": cost,
            "token": "t5yugymks5458fd4ghfg6h6fg",
            "service": "calls",
            "app_name": "q_and_a",
            "object_id": consultant_id,
            "call_type": call_type
        }
        
        logger.info(f"Call reservation payment request: {params}")
        
        encoded_params = urlencode(params)
        admin_token = os.environ.get('admin_token') or 'e9a236c586d4fb90f7f7ce2c70392d80069022d2'
        session = requests.Session()
        session.headers = {
            'Content-Type': 'application/json',
            'AUTHORIZATION': f'Token {admin_token}',
        }
        
        try:
            response = session.get(f"{url}?{encoded_params}", headers={
                'user-agent': 'dart:io'
            })
            response.raise_for_status()
            
            try:
                result = response.json()
                if result.get("status") == "Coins deducted successfully":
                    return True, "Payment successful"
                else:
                    error_message = result.get("error", "Unknown error")
                    logger.error(f"Error in call reservation payment: {error_message}")
                    return False, f"Payment failed: {error_message}"
            except requests.exceptions.JSONDecodeError:
                error_message = f"Failed to decode JSON response: {response.text}"
                logger.error(error_message)
                return False, "Failed to process payment response"
        except requests.exceptions.RequestException as e:
            error_message = f"Request failed: {e}"
            logger.error(error_message)
            return False, "Payment service unavailable"
            
    @staticmethod
    def process_call_reservation(client_id, consultant_id, start_time, end_time, call_type, cost, client_timezone='UTC'):
        """
        پردازش کامل رزرو تماس شامل پرداخت و ایجاد رزرو
        
        Args:
            client_id: شناسه کاربر
            consultant_id: شناسه مشاور
            start_time: زمان شروع (datetime)
            end_time: زمان پایان (datetime)
            call_type: نوع تماس ('voice' یا 'video')
            cost: هزینه تماس
            client_timezone: منطقه زمانی کاربر
            
        Returns:
            dict: نتیجه عملیات رزرو با وضعیت و پیام
        """
        with db():
            # دریافت اطلاعات کاربر
            user = db.session.query(User).filter(User.id == client_id).first()
            if not user:
                return {
                    'status': 'error',
                    'code': 'user_not_found',
                    'message': 'User not found'
                }
                
            # دریافت اطلاعات مشاور
            consultant = db.session.query(Consultant).filter(Consultant.id == consultant_id).first()
            if not consultant:
                return {
                    'status': 'error',
                    'code': 'consultant_not_found',
                    'message': 'Consultant not found'
                }
                
            # بررسی آیا این زمان قبلاً رزرو شده است
            existing_reservation = db.session.query(Call).filter(
                and_(
                    Call.consultant_id == consultant_id,
                    Call.is_reservation == True,
                    Call.status.in_(['unconfirmed', 'confirmed']),
                    Call.start_time <= end_time,
                    Call.end_time >= start_time
                )
            ).first()
            
            if existing_reservation:
                return {
                    'status': 'error',
                    'code': 'time_slot_reserved',
                    'message': 'This time slot is already reserved'
                }
                
            # پردازش پرداخت
            payment_success, payment_message = ReservationService.process_reservation_payment(user, cost, consultant_id, call_type)
            
            if not payment_success:
                return {
                    'status': 'error',
                    'code': 'payment_failed',
                    'message': payment_message
                }
                
            # ایجاد رزرو جدید
            new_reservation = Call(
                consultant_id=consultant_id,
                client_id=client_id,
                start_time=start_time,
                end_time=end_time,
                call_type=call_type,
                cost=cost,
                status='unconfirmed',
                is_reservation=True,
                reservation_date=datetime.datetime.now(pytz.UTC)
            )
            
            db.session.add(new_reservation)
            db.session.commit()
            
            return {
                'status': 'success',
                'code': 'reservation_created',
                'reservation_id': new_reservation.id,
                'consultant_id': consultant_id,
                'consultant_name': consultant.fullname,
                'start_time': start_time.strftime("%Y-%m-%d %H:%M"),
                'end_time': end_time.strftime("%Y-%m-%d %H:%M"),
                'call_type': call_type,
                'cost': cost
            }