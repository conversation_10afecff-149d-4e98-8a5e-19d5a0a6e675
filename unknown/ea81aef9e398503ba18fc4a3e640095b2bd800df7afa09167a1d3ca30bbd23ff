from datetime import datetime
from typing import Optional

__all__ = [
    'get_or_save_user',
]

from models import User
from services.consumer import ApiConsumer


def fetch_user(token):
    consumer = ApiConsumer()
    return consumer.get_user(token)


def get_user_from_api(token, db_session):
    if _user := fetch_user(token):
        username = _user.pop('username')
        if user := db_session.session.query(User).filter(User.username == username).first():
            update_user_data_if_needed(user, db_session)
            return user

        else:
            user = User(
                username=username,
                fullname=_user['fullname'],
                avatar_url=_user['avatar_url'],
                token=token,
                fcm=_user['fcm'],
                language_code=_user['language_code'],
                is_tester=_user.get('is_tester')
            )
            db_session.session.add(user)
            db_session.session.commit()
            db_session.session.refresh(user)

            return user


def update_user_data_if_needed(user, db_session):
    try:
        # Check if last_update exists, if not, set it to update
        if not hasattr(user, 'last_update') or user.last_update is None:
            should_update = True
            print("User has no last_update timestamp, updating data")
        else:
            # update if last update is over than 5 Min
            time_diff = (datetime.now() - user.last_update).seconds
            should_update = time_diff > 5 * 60
            if not should_update:
                print(f"Skipping user data update - last update was {time_diff} seconds ago (< 300 seconds)")
        
        if should_update:
            print("updating user from api -> ", user)
            consumer = ApiConsumer()
            user_data = consumer.get_user(user.token, email=user.username)
            if user_data:
                print("user refreshed data")
                user_data['last_update'] = datetime.now()
                db_session.session.query(User).filter(User.username == user.username).update(user_data)
                db_session.session.commit()
            else:
                print("--------------> user not found to update data", user)

    except Exception as e:
        print(f"Error updating user data: {e}")


def get_or_save_user(token, db) -> Optional[User]:
    if not token:
        return None

    if user := db.session.query(User).filter(User.token == token).first():
        update_user_data_if_needed(user, db)
        return user
    else:
        print("getting user from api:", token)
        return get_user_from_api(token, db)
