#!/usr/bin/env python3
"""
Test script for project cache clearing functionality.

This script creates test cache entries and then validates that the cache
clearing script works correctly for all project caches.

Usage:
    python test_cache_clearing.py
"""

import asyncio
import sys
import os
import json
from typing import Dict, List

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.redis_client import redis_client
from clear_consultant_caches import ProjectCacheCleaner
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CacheClearingTester:
    """
    Test class for validating project cache clearing functionality.
    """
    
    def __init__(self):
        self.test_keys = []
        self.test_data = {
            "consultant_list": [
                {"id": 1, "username": "test_consultant_1", "status": "online"},
                {"id": 2, "username": "test_consultant_2", "status": "offline"}
            ],
            "consultant_detail": {
                "id": 1, 
                "username": "test_consultant_1", 
                "fullname": "Test Consultant",
                "status": "online",
                "unread_count": 5
            },
            "stats_data": {
                "total_calls": 100,
                "total_messages": 500,
                "consultant_id": 1
            }
        }
    
    async def create_test_cache_entries(self) -> bool:
        """
        Create test cache entries that match the patterns used by the application.
        
        Returns:
            True if all test entries were created successfully
        """
        logger.info("Creating test cache entries...")
        
        # Test consultant list cache keys
        consultant_list_keys = [
            "consultants:123:en:False:None",
            "consultants:456:ar,en:True:doctor",
            "consultants:789:fa:False:None",
        ]
        
        # Test individual consultant cache keys
        consultant_detail_keys = [
            "consultant:test_consultant_1:123:en",
            "consultant:test_consultant_2:456:ar",
            "consultant:test_consultant_3:789:fa",
        ]
        
        # Test stats cache keys
        stats_keys = [
            "stats_info_consultant_1_month_2024-01-15",
            "stats_info_consultant_2_week_2024-01-15",
            "stats_info_all_month_2024-01-15",
            "period_stats_2024-01-01_2024-01-31_January_1_2024-01-15",
        ]

        # Test other project cache keys
        other_keys = [
            "test_project_cache_1",
            "test_project_cache_2",
        ]

        all_test_keys = consultant_list_keys + consultant_detail_keys + stats_keys + other_keys
        
        success = True
        for key in all_test_keys:
            try:
                if key.startswith("consultants:"):
                    data = self.test_data["consultant_list"]
                elif key.startswith("consultant:"):
                    data = self.test_data["consultant_detail"]
                elif key.startswith(("stats_info_", "period_stats_")):
                    data = self.test_data["stats_data"]
                else:  # other project keys
                    data = {"test": True, "timestamp": "2024-01-15"}
                
                result = await redis_client.set_json(key, data, expiry=3600)
                if result:
                    self.test_keys.append(key)
                    logger.info(f"✓ Created test key: {key}")
                else:
                    logger.error(f"✗ Failed to create test key: {key}")
                    success = False
            except Exception as e:
                logger.error(f"Error creating test key {key}: {e}")
                success = False
        
        logger.info(f"Created {len(self.test_keys)} test cache entries")
        return success
    
    async def verify_test_keys_exist(self) -> bool:
        """
        Verify that all test keys exist in Redis.
        
        Returns:
            True if all test keys exist
        """
        logger.info("Verifying test keys exist...")
        
        existing_keys = 0
        for key in self.test_keys:
            try:
                exists = await redis_client.exists(key)
                if exists:
                    existing_keys += 1
                    logger.info(f"✓ Key exists: {key}")
                else:
                    logger.warning(f"✗ Key missing: {key}")
            except Exception as e:
                logger.error(f"Error checking key {key}: {e}")
        
        logger.info(f"Found {existing_keys}/{len(self.test_keys)} test keys")
        return existing_keys == len(self.test_keys)
    
    async def test_dry_run_mode(self) -> bool:
        """
        Test the dry run mode of the cache cleaner.
        
        Returns:
            True if dry run works correctly
        """
        logger.info("Testing dry run mode...")

        cleaner = ProjectCacheCleaner(dry_run=True, verbose=True)
        results = await cleaner.clear_all_project_caches()
        
        # Verify that keys still exist after dry run
        keys_still_exist = await self.verify_test_keys_exist()
        
        if keys_still_exist:
            logger.info("✓ Dry run mode working correctly - no keys were deleted")
            return True
        else:
            logger.error("✗ Dry run mode failed - some keys were deleted")
            return False
    
    async def test_actual_clearing(self) -> bool:
        """
        Test the actual cache clearing functionality.
        
        Returns:
            True if cache clearing works correctly
        """
        logger.info("Testing actual cache clearing...")

        cleaner = ProjectCacheCleaner(dry_run=False, verbose=True)
        results = await cleaner.clear_all_project_caches()
        
        # Verify that keys were actually deleted
        remaining_keys = 0
        for key in self.test_keys:
            try:
                exists = await redis_client.exists(key)
                if exists:
                    remaining_keys += 1
                    logger.warning(f"Key still exists: {key}")
            except Exception as e:
                logger.error(f"Error checking key {key}: {e}")
        
        if remaining_keys == 0:
            logger.info("✓ Cache clearing working correctly - all test keys were deleted")
            return True
        else:
            logger.error(f"✗ Cache clearing failed - {remaining_keys} keys still exist")
            return False
    
    async def cleanup_test_keys(self):
        """Clean up any remaining test keys."""
        logger.info("Cleaning up test keys...")
        
        for key in self.test_keys:
            try:
                await redis_client.delete(key)
            except Exception as e:
                logger.warning(f"Error cleaning up key {key}: {e}")
    
    async def run_full_test(self) -> bool:
        """
        Run the complete test suite.
        
        Returns:
            True if all tests pass
        """
        logger.info("Starting project cache clearing test suite...")
        
        try:
            # Step 1: Create test cache entries
            if not await self.create_test_cache_entries():
                logger.error("Failed to create test cache entries")
                return False
            
            # Step 2: Verify test keys exist
            if not await self.verify_test_keys_exist():
                logger.error("Test keys verification failed")
                return False
            
            # Step 3: Test dry run mode
            if not await self.test_dry_run_mode():
                logger.error("Dry run mode test failed")
                return False
            
            # Step 4: Test actual clearing
            if not await self.test_actual_clearing():
                logger.error("Actual clearing test failed")
                return False
            
            logger.info("✓ All tests passed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Test suite failed with error: {e}")
            return False
        finally:
            # Always cleanup
            await self.cleanup_test_keys()


async def main():
    """Main function to run the test suite."""
    
    try:
        # Test Redis connection
        logger.info("Testing Redis connection...")
        if not await redis_client.ping():
            logger.error("Failed to connect to Redis. Please check your Redis configuration.")
            return 1
        logger.info("✓ Redis connection successful")
        
        # Run the test suite
        tester = CacheClearingTester()
        success = await tester.run_full_test()
        
        if success:
            logger.info("🎉 All tests completed successfully!")
            logger.info("The cache clearing script is ready for production use.")
            return 0
        else:
            logger.error("❌ Some tests failed. Please review the issues above.")
            return 1
            
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        return 1
    finally:
        # Close Redis connection
        await redis_client.close()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
