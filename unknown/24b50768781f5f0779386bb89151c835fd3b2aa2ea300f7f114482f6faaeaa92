# راهنمای حذف کش‌های پروژه

## خلاصه

این راهنما برای حذف **همه کش‌های پروژه Habib** از Redis طراحی شده است. این اسکریپت تمام داده‌های کش شده شامل اطلاعات مشاوران، آمار، و سایر داده‌های کش شده را حذف می‌کند.

⚠️ **هشدار مهم**: این اسکریپت همه کش‌های پروژه را حذف می‌کند!

## فایل‌های اصلی

- **`clear_all_caches.py`** - اسکریپت اصلی برای حذف همه کش‌ها
- **`validate_cache_setup.py`** - اسکریپت بررسی صحت تنظیمات
- **`test_cache_clearing.py`** - اسکریپت تست عملکرد

## نحوه استفاده

### 1. بررسی تنظیمات (اختیاری)

```bash
python validate_cache_setup.py
```

### 2. پیش‌نمایش (توصیه می‌شود)

```bash
# مشاهده کش‌هایی که حذف خواهند شد
python clear_all_caches.py --dry-run --verbose
```

### 3. حذف کش‌ها در محیط تولید

```bash
# حذف با تأیید دستی
python clear_all_caches.py --verbose

# حذف بدون تأیید (با احتیاط استفاده کنید)
python clear_all_caches.py --verbose --confirm
```

## انواع کش‌هایی که حذف می‌شوند

### 1. کش‌های مشاوران
- `consultants:*` - لیست مشاوران (کش 3 دقیقه‌ای)
- `consultant:*` - اطلاعات تک مشاور (کش 3 دقیقه‌ای)

### 2. کش‌های آماری
- `stats_info_consultant_*` - آمار مشاوران
- `stats_info_all_*` - آمار کلی
- `period_stats_*` - آمار دوره‌ای

### 3. سایر کش‌ها
- `*` - همه کش‌های باقی‌مانده

## مثال‌های عملی

### مشاهده کش‌های موجود
```bash
# پیش‌نمایش کامل
python clear_all_caches.py --dry-run --verbose
```

خروجی نمونه:
```
2024-01-15 10:30:00,123 - INFO - Pattern 'consultants:*' matched 45 keys
2024-01-15 10:30:00,124 - INFO - Pattern 'consultant:*' matched 23 keys
2024-01-15 10:30:00,125 - INFO - Pattern 'stats_info_*' matched 12 keys
2024-01-15 10:30:00,126 - INFO - Total keys to be analyzed: 80
```

### حذف کش‌ها
```bash
# حذف با تأیید
python clear_all_caches.py --verbose
```

خروجی نمونه:
```
⚠️  WARNING: This will delete ALL cached data for the project!
Are you sure you want to proceed? (yes/no): yes

=== DELETION SUMMARY ===
  consultants:*: 45 keys
  consultant:*: 23 keys
  stats_info_consultant_*: 8 keys
  stats_info_all_*: 4 keys
  period_stats_*: 0 keys
  *: 0 keys
  Total: 80 keys deleted

✓ All project caches have been successfully cleared
```

## تأثیرات حذف کش

### تأثیرات فوری
- همه داده‌های کش شده حذف می‌شوند
- درخواست‌های بعدی داده‌های تازه از دیتابیس دریافت می‌کنند
- افزایش موقت بار دیتابیس
- کاهش موقت سرعت پاسخ‌دهی

### بازیابی عملکرد
- کش‌ها به تدریج بازسازی می‌شوند
- عملکرد طبیعی ظرف 1-2 ساعت بازمی‌گردد

## نکات ایمنی

### قبل از اجرا
1. **حتماً ابتدا `--dry-run` استفاده کنید**
2. در ساعات کم‌ترافیک اجرا کنید
3. منابع دیتابیس را مانیتور کنید
4. از وضعیت Redis اطمینان حاصل کنید

### بعد از اجرا
1. عملکرد اپلیکیشن را بررسی کنید
2. زمان پاسخ‌دهی API را مانیتور کنید
3. بار دیتابیس را بررسی کنید

## عیب‌یابی

### خطاهای رایج

#### 1. خطای اتصال Redis
```
ERROR - Failed to connect to Redis
```
**راه‌حل**: 
- وضعیت سرور Redis را بررسی کنید
- تنظیمات اتصال را بررسی کنید

#### 2. حذف ناقص کش‌ها
```
WARNING - 15 cache keys still remain
```
**راه‌حل**:
- دوباره اسکریپت را اجرا کنید
- محدودیت‌های حافظه Redis را بررسی کنید

#### 3. کاهش عملکرد
**راه‌حل**:
- منابع دیتابیس را افزایش دهید
- در ساعات کم‌ترافیک اجرا کنید

## بررسی وضعیت کش‌ها

### قبل از حذف
```bash
redis-cli info memory
redis-cli --scan --pattern "consultants:*" | wc -l
```

### بعد از حذف
```bash
redis-cli --scan --pattern "*" | wc -l
```

## پشتیبانی

در صورت بروز مشکل:
1. لاگ‌های تولید شده توسط اسکریپت را بررسی کنید
2. اتصال Redis را تست کنید
3. ابتدا در محیط تست آزمایش کنید

## مثال کامل

```bash
# 1. بررسی تنظیمات
python validate_cache_setup.py

# 2. پیش‌نمایش
python clear_all_caches.py --dry-run --verbose

# 3. حذف کش‌ها
python clear_all_caches.py --verbose

# 4. بررسی نتیجه
redis-cli --scan --pattern "*" | wc -l
```

---

**نکته**: این اسکریپت برای حذف کامل همه کش‌های پروژه طراحی شده است. در محیط‌های مشترک Redis با احتیاط استفاده کنید.
