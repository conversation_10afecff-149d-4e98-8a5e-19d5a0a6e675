"""chat type

Revision ID: 99c2b02a4e96
Revises: f95fe453c809
Create Date: 2024-04-16 13:15:55.655673

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '99c2b02a4e96'
down_revision: Union[str, None] = 'f95fe453c809'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('chat_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messages', 'chat_type')
    # ### end Alembic commands ###
