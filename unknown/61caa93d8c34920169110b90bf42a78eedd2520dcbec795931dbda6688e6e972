import datetime
import time
import logging
import json
import decimal
from fastapi import Request, Query
from starlette.responses import JSONResponse
from sqlalchemy import func, and_
from sqlalchemy.orm import joinedload

from apps.admin import router
from models import Consultant, Room, Message, db, User
from utils.redis_client import redis_client

# Custom JSON encoder to handle Decimal types
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Redis cache configuration
CACHE_EXPIRY = 86400  # 1 day in seconds

def convert_decimal_to_float(obj):
    """
    Recursively convert Decimal objects to float in a nested structure.
    Works with dictionaries, lists and primitive types.
    """
    if isinstance(obj, decimal.Decimal):
        return float(obj)
    elif isinstance(obj, dict):
        return {k: convert_decimal_to_float(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_decimal_to_float(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_decimal_to_float(item) for item in obj)
    else:
        return obj


from enum import Enum

class TimePeriod(str, Enum):
    """
    Enum for time periods available in the stats API.

    Values:
    - YEAR: Last 12 months of data (commented out)
    - SIX_MONTHS: Last 6 months of data
    - THREE_MONTHS: Last 3 months of data
    - MONTH: Current month data
    - TWO_WEEKS: Last 2 weeks of data (no cache)
    - ONE_WEEK: Last 7 days of data (no cache)
    - THREE_DAYS: Last 3 days of data (no cache)
    - ONE_DAY: Last 48 hours of data (no cache)
    """
    # YEAR = "year"  # Commented out - yearly calculation disabled
    SIX_MONTHS = "six_months"
    THREE_MONTHS = "three_months"
    MONTH = "month"
    TWO_WEEKS = "two_weeks"
    ONE_WEEK = "one_week"
    THREE_DAYS = "three_days"
    ONE_DAY = "one_day"

@router.get("/info/", name="Consultants Stats Info")
async def consultants_stats_info(
    period: TimePeriod = Query(..., description="Required time period (year, six_months, three_months, month)"),
    consultant: str = Query(None, description="Optional consultant username")
):
    """
    Get statistics for all consultants or a specific consultant if username is provided.

    Returns statistics for the specified time period including total questions, answers,
    unanswered questions, score, and breakdowns by country and language.

    Available periods:
    - six_months: Last 6 months (cached)
    - three_months: Last 3 months (cached)
    - month: Current month (cached)
    - two_weeks: Last 2 weeks (no cache)
    - one_week: Last 7 days (no cache)
    - three_days: Last 3 days (no cache)
    - one_day: Last 48 hours (no cache)

    Note: year period is commented out/disabled
    """
    start_time = time.time()
    logger.info(f"Stats info request received for consultant: {consultant}")
    
    # Create a cache key based on the consultant parameter, period and current day
    # This ensures cache is refreshed daily but reused for repeated requests
    # All queries (consultant-specific or all consultants) use daily cache
    logger.info(f"Calculating stats for period: {period.value}")
    
    # Check if this period should use cache (real-time periods don't use cache)
    use_cache = period not in [TimePeriod.ONE_DAY, TimePeriod.THREE_DAYS, TimePeriod.ONE_WEEK, TimePeriod.TWO_WEEKS]

    if use_cache:
        # Daily cache for all queries (consultant-specific or all consultants)
        current_day = datetime.datetime.now().strftime("%Y-%m-%d")
        if consultant:
            cache_key = f"stats_info_consultant_{consultant}_{period.value}_{current_day}"
        else:
            cache_key = f"stats_info_all_{period.value}_{current_day}"

        # Check if we have a valid cached result in Redis
        cached_data = await redis_client.get_json(cache_key)
        if cached_data:
            logger.info(f"Cache hit for {cache_key}. Returning cached data.")
            logger.info(f"Response time: {time.time() - start_time:.3f} seconds (cached)")
            return cached_data

        logger.info(f"Cache miss for {cache_key}. Calculating new data.")
    else:
        logger.info(f"Live calculation for period: {period.value} (no cache)")
        cache_key = None
    
    # Get current date for calculations
    current_date = datetime.datetime.now()

    # Year stats (last 12 months) - Commented out
    # year_start = (current_date - datetime.timedelta(days=365)).replace(day=1, hour=0, minute=0, second=0)
    # year_labels = get_last_n_months(12)

    # Six months stats
    six_months_start = (current_date - datetime.timedelta(days=180)).replace(day=1, hour=0, minute=0, second=0)
    six_month_labels = get_last_n_months(6)

    # Three months stats
    three_months_start = (current_date - datetime.timedelta(days=90)).replace(day=1, hour=0, minute=0, second=0)
    three_month_labels = get_last_n_months(3)

    # Current month stats
    current_month_start = current_date.replace(day=1, hour=0, minute=0, second=0)
    current_month_label = [current_date.strftime("%B")]

    # Two weeks stats (no cache)
    two_weeks_start = current_date - datetime.timedelta(days=14)
    two_weeks_labels = get_daily_labels(14)

    # One week stats (no cache)
    one_week_start = current_date - datetime.timedelta(days=7)
    one_week_labels = get_daily_labels(7)

    # Three days stats (no cache)
    three_days_start = current_date - datetime.timedelta(days=3)
    three_days_labels = get_daily_labels(3)

    # One day stats (last 48 hours, no cache)
    one_day_start = current_date - datetime.timedelta(days=2)
    one_day_labels = get_daily_labels(2)
    
    # If consultant username is provided, get stats for that consultant only
    consultant_obj = None
    if consultant:
        with db():
            consultant_obj = db.session.query(Consultant).filter(Consultant.username == consultant).first()
        if not consultant_obj:
            return JSONResponse({"error": "Consultant not found"}, status_code=404)
        
        consultant_name = consultant_obj.fullname
        if not consultant_name or consultant_name.strip() == "":
            consultant_name = consultant_obj.username
        
        # Calculate only the requested period
        # if period == TimePeriod.YEAR:  # Commented out - yearly calculation disabled
        #     period_stats = await calculate_period_stats(year_start, current_date, year_labels, consultant_obj.id)
        if period == TimePeriod.SIX_MONTHS:
            period_stats = await calculate_period_stats(six_months_start, current_date, six_month_labels, consultant_obj.id)
        elif period == TimePeriod.THREE_MONTHS:
            period_stats = await calculate_period_stats(three_months_start, current_date, three_month_labels, consultant_obj.id)
        elif period == TimePeriod.TWO_WEEKS:
            period_stats = await calculate_period_stats(two_weeks_start, current_date, two_weeks_labels, consultant_obj.id, use_cache=False)
        elif period == TimePeriod.ONE_WEEK:
            period_stats = await calculate_period_stats(one_week_start, current_date, one_week_labels, consultant_obj.id, use_cache=False)
        elif period == TimePeriod.THREE_DAYS:
            period_stats = await calculate_period_stats(three_days_start, current_date, three_days_labels, consultant_obj.id, use_cache=False)
        elif period == TimePeriod.ONE_DAY:
            period_stats = await calculate_period_stats(one_day_start, current_date, one_day_labels, consultant_obj.id, use_cache=False)
        else:  # TimePeriod.MONTH
            period_stats = await calculate_period_stats(current_month_start, current_date, current_month_label, consultant_obj.id)
            
        result = {
            "expert": consultant_name,
            "period": period.value,
            "stats": period_stats
        }
    else:
        # Get stats for all consultants, but only for the requested period
        # if period == TimePeriod.YEAR:  # Commented out - yearly calculation disabled
        #     period_stats = await calculate_period_stats(year_start, current_date, year_labels)
        if period == TimePeriod.SIX_MONTHS:
            period_stats = await calculate_period_stats(six_months_start, current_date, six_month_labels)
        elif period == TimePeriod.THREE_MONTHS:
            period_stats = await calculate_period_stats(three_months_start, current_date, three_month_labels)
        elif period == TimePeriod.TWO_WEEKS:
            period_stats = await calculate_period_stats(two_weeks_start, current_date, two_weeks_labels, use_cache=False)
        elif period == TimePeriod.ONE_WEEK:
            period_stats = await calculate_period_stats(one_week_start, current_date, one_week_labels, use_cache=False)
        elif period == TimePeriod.THREE_DAYS:
            period_stats = await calculate_period_stats(three_days_start, current_date, three_days_labels, use_cache=False)
        elif period == TimePeriod.ONE_DAY:
            period_stats = await calculate_period_stats(one_day_start, current_date, one_day_labels, use_cache=False)
        else:  # TimePeriod.MONTH
            period_stats = await calculate_period_stats(current_month_start, current_date, current_month_label)
        result = {
            "expert": "All Consultants",
            "period": period.value,
            "stats": period_stats
        }
    
    # Convert Decimal values to float before storing in Redis
    json_safe_result = convert_decimal_to_float(result)

    # Store the result in Redis cache with expiry (only if caching is enabled)
    if use_cache and cache_key:
        try:
            await redis_client.set_json(cache_key, json_safe_result, CACHE_EXPIRY)
            logger.info(f"Redis cache: Key '{cache_key}' stored with expiry {CACHE_EXPIRY} seconds (1 day)")
        except Exception as e:
            logger.error(f"Redis set_json error: {str(e)}")

    # Log performance metrics
    execution_time = time.time() - start_time
    logger.info(f"Stats calculation completed in {execution_time:.3f} seconds")

    # Determine if we're dealing with days or months for logging
    time_unit = "days" if period in [TimePeriod.ONE_DAY, TimePeriod.THREE_DAYS, TimePeriod.ONE_WEEK, TimePeriod.TWO_WEEKS] else "months"
    logger.info(f"Result contains data for {len(result['stats']['labels'])} {time_unit}")

    # Log detailed performance metrics
    if not consultant:
        logger.info(f"All consultants query - {period.value} data: {len(result['stats']['Questions'])} {time_unit}, "
                   f"{sum(result['stats']['Questions'])} questions, {sum(result['stats']['Answers'])} answers")
        logger.info(f"All consultants query - Countries: {len(result['stats']['country'])} countries, "
                   f"Languages: {len(result['stats']['language'])} languages")

    # Log cache status
    if use_cache:
        logger.info(f"Result cached for future requests")
    else:
        logger.info(f"Live calculation completed - result not cached")
    
    return result

def get_last_n_months(n):
    """Get the last n months in chronological order"""
    current_date = datetime.datetime.now()
    months = []
    for i in range(n):
        # Calculate month that is i months ago
        month_date = current_date - datetime.timedelta(days=30*i)
        months.append(month_date.strftime("%B"))
    months.reverse()  # Reverse to get chronological order
    return months

def get_daily_labels(n):
    """Get the last n days in chronological order"""
    current_date = datetime.datetime.now()
    days = []
    for i in range(n):
        # Calculate day that is i days ago
        day_date = current_date - datetime.timedelta(days=i)
        days.append(day_date.strftime("%Y-%m-%d"))
    days.reverse()  # Reverse to get chronological order
    return days


async def calculate_period_stats(start_date, end_date, month_labels, consultant_id=None, use_cache=True):
    """
    Calculate statistics for a given time period.
    If consultant_id is provided, calculate stats only for that consultant.
    Otherwise, calculate stats for all consultants.
    If use_cache is False, skip caching for real-time calculations.
    """
    period_start_time = time.time()
    period_name = f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
    logger.info(f"Calculating stats for period: {period_name}, consultant_id: {consultant_id}")
    
    # Create a cache key for this specific calculation with daily refresh
    current_day = datetime.datetime.now().strftime("%Y-%m-%d")
    cache_key = f"period_stats_{start_date.strftime('%Y-%m-%d')}_{end_date.strftime('%Y-%m-%d')}_{'-'.join(month_labels)}_{consultant_id}_{current_day}"

    # Check if we have a valid cached result in Redis (only if caching is enabled)
    if use_cache:
        cached_data = await redis_client.get_json(cache_key)
        if cached_data:
            logger.info(f"Cache hit for period calculation: {period_name}")
            return cached_data

        logger.info(f"Cache miss for period calculation: {period_name}")
    else:
        logger.info(f"Live calculation (no cache) for period: {period_name}")
    
    # Initialize data structures for stats (can be monthly or daily)
    period_questions = {label: 0 for label in month_labels}
    period_answers = {label: 0 for label in month_labels}
    period_reply_times = {label: [] for label in month_labels}
    
    # Initialize country and language stats
    country_stats = {}
    language_stats = {}
    
    # Get total counts directly from database using aggregation
    from models.rate import Rate
    
    # Calculate average score for consultants using direct SQL aggregation
    total_score = 0
    with db():
        if consultant_id:
            # Direct SQL aggregation for a single consultant
            avg_score = db.session.query(func.avg(Rate.rate)).filter(
                Rate.consultant == consultant_id
            ).scalar()
            
            total_score = round(avg_score, 2) if avg_score is not None else 0
        else:
            # For all consultants, use a more efficient query with grouping
            # Get average score per consultant
            consultant_scores = db.session.query(
                Rate.consultant, 
                func.avg(Rate.rate).label('avg_score')
            ).group_by(Rate.consultant).all()
            
            # Calculate overall average
            if consultant_scores:
                total_score = sum(score for _, score in consultant_scores) / len(consultant_scores)
                total_score = round(total_score, 2)
    
    # Get rooms for the time period with eager loading of client relationship
    rooms = []
    with db():
        # First, get a count of rooms to determine if we need to optimize
        room_count_query = db.session.query(func.count(Room.id)).filter(
            Room.created_at >= start_date,
            Room.created_at <= end_date
        )
        
        # If consultant_id is provided, filter by that consultant
        if consultant_id:
            room_count_query = room_count_query.filter(Room.consultant_id == consultant_id)
        
        room_count = room_count_query.scalar()
        
        logger.info(f"Found {room_count} rooms for period {period_name}")
        
        # If we have a very large number of rooms and this is a year-long query without consultant filter,
        # we can optimize by sampling or limiting
        max_rooms = 10000  # Maximum number of rooms to process for performance
        
        if room_count > max_rooms and not consultant_id and (end_date - start_date).days > 180:
            logger.info(f"Large dataset detected ({room_count} rooms). Optimizing query.")
            
            # For very large datasets, we'll use a different approach:
            # 1. Get all rooms for the most recent 3 months (these are most important)
            recent_cutoff = end_date - datetime.timedelta(days=90)
            
            # Get recent rooms
            recent_rooms_query = db.session.query(Room).options(
                joinedload(Room.client)
            ).filter(
                Room.created_at >= recent_cutoff,
                Room.created_at <= end_date
            )
            
            if consultant_id:
                recent_rooms_query = recent_rooms_query.filter(Room.consultant_id == consultant_id)
                
            recent_rooms = recent_rooms_query.all()
            
            # 2. Sample older rooms to get a representative set
            older_rooms_query = db.session.query(Room).options(
                joinedload(Room.client)
            ).filter(
                Room.created_at >= start_date,
                Room.created_at < recent_cutoff
            )
            
            if consultant_id:
                older_rooms_query = older_rooms_query.filter(Room.consultant_id == consultant_id)
            
            # Get a sample of older rooms (ordered by ID to ensure deterministic results)
            older_rooms_query = older_rooms_query.order_by(Room.id)
            
            # Calculate how many older rooms to sample
            older_rooms_to_sample = max_rooms - len(recent_rooms)
            if older_rooms_to_sample > 0:
                # Get total count of older rooms
                older_count = db.session.query(func.count(Room.id)).filter(
                    Room.created_at >= start_date,
                    Room.created_at < recent_cutoff
                )
                
                if consultant_id:
                    older_count = older_count.filter(Room.consultant_id == consultant_id)
                    
                older_count = older_count.scalar()
                
                # Calculate sampling interval
                if older_count > older_rooms_to_sample:
                    sampling_interval = older_count // older_rooms_to_sample
                    older_rooms = []
                    
                    # Sample rooms at regular intervals
                    for i in range(0, older_count, sampling_interval):
                        sampled_room = older_rooms_query.offset(i).limit(1).first()
                        if sampled_room:
                            older_rooms.append(sampled_room)
                else:
                    # If we have fewer older rooms than our sample size, get all of them
                    older_rooms = older_rooms_query.all()
            else:
                older_rooms = []
            
            # Combine recent and sampled older rooms
            rooms = recent_rooms + older_rooms
            logger.info(f"Using {len(rooms)} rooms ({len(recent_rooms)} recent, {len(older_rooms)} older) out of {room_count} total")
        else:
            # For smaller datasets or specific consultant queries, use the original approach
            rooms_query = db.session.query(Room).options(
                joinedload(Room.client)  # Eager load client to avoid N+1 queries
            ).filter(
                Room.created_at >= start_date,
                Room.created_at <= end_date
            )
            
            # If consultant_id is provided, filter by that consultant
            if consultant_id:
                rooms_query = rooms_query.filter(Room.consultant_id == consultant_id)
            
            rooms = rooms_query.all()
    
    if not rooms:
        # Get count of all consultants for empty result
        consultant_count = 0
        with db():
            consultant_count = db.session.query(func.count(Consultant.id)).scalar()
            
        # Return empty data if no rooms found
        empty_result = {
            "labels": month_labels,
            "Questions": [0] * len(month_labels),
            "Answers": [0] * len(month_labels),
            "AvgReplies": [0] * len(month_labels),
            "total": {
                "total_questions": 0,
                "total_answers": 0,
                "total_unanswered_questions": 0,
                "score": 0,
                "experts": consultant_count
            },
            "country": {},
            "language": {}
        }
        # Convert Decimal values to float before storing in Redis
        json_safe_result = convert_decimal_to_float(empty_result)

        # Cache the empty result in Redis (only if caching is enabled)
        if use_cache:
            try:
                await redis_client.set_json(cache_key, json_safe_result, CACHE_EXPIRY)
            except Exception as e:
                logger.error(f"Redis set_json error: {str(e)}")
        return empty_result
    
    total_questions = 0
    total_answers = 0
    total_unanswered_questions = 0
    
    # Get all room IDs
    room_ids = [room.id for room in rooms]
    
    # Fetch all messages for the period and process them in Python
    # This is more database-agnostic than using database-specific date functions
    all_messages = []
    
    # For performance optimization, limit the number of messages processed
    # If there are too many rooms, we'll use a more efficient approach
    if len(room_ids) > 1500:
        logger.info(f"Large dataset detected: {len(room_ids)} rooms. Using optimized query approach.")
        # Process month by month to reduce memory usage
        current_month_start = start_date
        while current_month_start <= end_date:
            # Calculate end of current month
            if current_month_start.month == 12:
                next_month = datetime.datetime(current_month_start.year + 1, 1, 1)
            else:
                next_month = datetime.datetime(current_month_start.year, current_month_start.month + 1, 1)
            
            current_month_end = min(next_month, end_date)
            
            # Get messages for current month only
            with db():
                month_messages = db.session.query(
                    Message.by_user_type,
                    Message.at_time
                ).filter(
                    Message.room_id.in_(room_ids),
                    Message.at_time >= current_month_start,
                    Message.at_time < current_month_end
                ).all()
                
                all_messages.extend(month_messages)
            
            # Move to next month
            current_month_start = next_month
    else:
        # For smaller datasets, use the original approach
        with db():
            all_messages = db.session.query(
                Message.by_user_type,
                Message.at_time
            ).filter(
                Message.room_id.in_(room_ids),
                Message.at_time >= start_date,
                Message.at_time <= end_date
            ).all()
    
    # Process messages to count by period and type
    for user_type, message_time in all_messages:
        # For daily periods, use date format; for monthly periods, use month name
        if any(label.count('-') == 2 for label in month_labels):  # Daily format (YYYY-MM-DD)
            period_label = message_time.strftime("%Y-%m-%d")
        else:  # Monthly format
            period_label = message_time.strftime("%B")

        if period_label in month_labels:
            if user_type == 'User':
                period_questions[period_label] += 1
                total_questions += 1
            elif user_type == 'Consultant' or user_type == Consultant.__name__:
                period_answers[period_label] += 1
                total_answers += 1
    
    # Calculate unanswered questions
    total_unanswered_questions = total_questions - total_answers if total_questions > total_answers else 0
    
    # Fetch all messages for all rooms in a single query for response time calculation
    # and country/language stats
    all_messages_query = []
    
    # For large datasets, process in batches to reduce memory usage
    if len(room_ids) > 500:
        logger.info(f"Processing {len(room_ids)} rooms in batches for response time calculation")
        # Process in batches of 500 rooms
        batch_size = 500
        for i in range(0, len(room_ids), batch_size):
            batch_room_ids = room_ids[i:i+batch_size]
            logger.info(f"Processing batch {i//batch_size + 1} with {len(batch_room_ids)} rooms")
            
            with db():
                batch_messages = db.session.query(Message).filter(
                    Message.room_id.in_(batch_room_ids)
                ).order_by(Message.room_id, Message.at_time).all()
                
                all_messages_query.extend(batch_messages)
    else:
        # For smaller datasets, use the original approach
        with db():
            all_messages_query = db.session.query(Message).filter(
                Message.room_id.in_(room_ids)
            ).order_by(Message.room_id, Message.at_time).all()
    
    # Group messages by room_id for faster access
    messages_by_room = {}
    for message in all_messages_query:
        if message.room_id not in messages_by_room:
            messages_by_room[message.room_id] = []
        messages_by_room[message.room_id].append(message)
    
    # Process each room for country/language stats and response times
    # For large datasets, use sampling to improve performance
    sample_size = min(len(rooms), 1000)  # Maximum 1000 rooms for response time calculation
    
    # If we have a large dataset, sample rooms for response time calculation
    if len(rooms) > sample_size:
        logger.info(f"Sampling {sample_size} rooms out of {len(rooms)} for response time calculation")
        import random
        response_time_sample = random.sample(rooms, sample_size)
    else:
        response_time_sample = rooms
    
    # Process all rooms for country/language stats (these are lightweight calculations)
    for room in rooms:
        # Skip if no messages for this room
        if room.id not in messages_by_room:
            continue
            
        messages = messages_by_room.get(room.id, [])
        if not messages:
            continue
        
        # Get user country and language for this room
        if room.client:
            user_country = room.client.country or "Unknown"
            user_language = room.client.language_code or "Unknown"
            
            # Update country stats
            if user_country not in country_stats:
                country_stats[user_country] = {"questions": 0, "answers": 0}
            
            # Update language stats
            if user_language not in language_stats:
                language_stats[user_language] = {"questions": 0, "answers": 0}
            
            # Count messages by type for country and language stats
            # Use a more efficient approach with a single pass through the messages
            user_messages = 0
            consultant_messages = 0
            
            # Pre-compute the check for consultant type to avoid repeated string comparisons
            for m in messages:
                if m.by_user_type == 'User':
                    user_messages += 1
                elif m.by_user_type == 'Consultant' or m.by_user_type == Consultant.__name__:
                    consultant_messages += 1
            
            # Update stats only once per room
            country_stats[user_country]["questions"] += user_messages
            country_stats[user_country]["answers"] += consultant_messages
            language_stats[user_language]["questions"] += user_messages
            language_stats[user_language]["answers"] += consultant_messages
    
    # Process only sampled rooms for response time calculation (which is more CPU intensive)
    logger.info(f"Calculating response times for {len(response_time_sample)} rooms")
    for room in response_time_sample:
        # Skip if no messages for this room
        if room.id not in messages_by_room:
            continue
            
        messages = messages_by_room.get(room.id, [])
        if not messages:
            continue
            
        # Calculate response times
        last_user_message = None
        
        for message in messages:
            message_date = message.at_time
            # For daily periods, use date format; for monthly periods, use month name
            if any(label.count('-') == 2 for label in month_labels):  # Daily format (YYYY-MM-DD)
                period_label = message_date.strftime("%Y-%m-%d")
            else:  # Monthly format
                period_label = message_date.strftime("%B")

            if period_label in month_labels:
                if message.by_user_type == 'User':
                    last_user_message = message
                elif (message.by_user_type == 'Consultant' or message.by_user_type == Consultant.__name__) and last_user_message:
                    response_time = (message.at_time - last_user_message.at_time).total_seconds() / 60
                    period_reply_times[period_label].append(response_time)
                    last_user_message = None
    
    # Calculate average response times
    avg_replies = []
    for label in month_labels:
        if period_reply_times[label]:
            avg = sum(period_reply_times[label]) / len(period_reply_times[label])
            avg_replies.append(round(avg))
        else:
            avg_replies.append(0)
    
    # Get count of all consultants
    consultant_count = 0
    with db():
        consultant_count = db.session.query(func.count(Consultant.id)).scalar()
    
    result = {
        "labels": month_labels,
        "Questions": [period_questions[label] for label in month_labels],
        "Answers": [period_answers[label] for label in month_labels],
        "AvgReplies": avg_replies,
        "total": {
            "total_questions": total_questions,
            "total_answers": total_answers,
            "total_unanswered_questions": total_unanswered_questions,
            "score": total_score,
            "experts": consultant_count
        },
        "country": country_stats,
        "language": language_stats
    }
    
    # Convert Decimal values to float before storing in Redis
    json_safe_result = convert_decimal_to_float(result)

    # Cache the result in Redis (only if caching is enabled)
    if use_cache:
        try:
            await redis_client.set_json(cache_key, json_safe_result, CACHE_EXPIRY)
            logger.info(f"Redis cache: Key '{cache_key}' stored with expiry {CACHE_EXPIRY} seconds (1 day)")
        except Exception as e:
            logger.error(f"Redis set_json error: {str(e)}")
    else:
        logger.info("Live calculation completed - result not cached")
    
    # Log performance for this period calculation
    period_execution_time = time.time() - period_start_time
    logger.info(f"Period stats calculation completed in {period_execution_time:.3f} seconds for {period_name}")
    logger.info(f"Found {total_questions} questions and {total_answers} answers for period {period_name}")
    
    return result