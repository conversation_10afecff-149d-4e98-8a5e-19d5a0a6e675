import datetime
import enum

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, DateTime, Foreign<PERSON>ey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy_utils import ChoiceType

from models import Base


class ActivityLog(Base):
    """
        todo: implement websocket actions
    """
    __tablename__ = "activity_logs"

    class ActivityType(enum.Enum):
        text = 'text'
        video = 'video'
        voice = 'voice'

        @classmethod
        @property
        def choices(cls):
            return {m.name: m.value for m in cls}

    id = Column(Integer, primary_key=True, index=True)

    user_id = Column(Integer, ForeignKey('users.id'))
    consultant_id = Column(Integer, ForeignKey('consultants.id'))

    room_id = Column(Integer, ForeignKey('rooms.id'), nullable=True)
    activity_type = Column(String, default=ActivityType.text)

    download_link = Column(String, nullable=True)

    start_at = Column(DateTime, default=datetime.datetime.now)
    end_at = Column(DateTime, )
    duration = Column(String, default=0)

    consultant = relationship('Consultant', backref='activity_logs', lazy='joined')
    user = relationship('User', backref='activity_logs', lazy='joined')
    room = relationship('Room', backref='activity_logs', lazy='joined')
