<div style="text-align: center;">**بسم الله الرحمن الرحیم**</div>

# <PERSON><PERSON><PERSON><PERSON> Application (BACKEND)

### Mongo DB document structure

#### room document

```yaml
{
  "_id": "(UUID)",
  "ttl": "discussion title", # title,
  "mmbrs": "[<user_id>]", # members
  "crtdAt": "", # created at
  "team": {
    "name": "",
    "id": "", # team id in django
  },
  "admn": { # room admin
    "id": 23, # user id in django db
    "fn": "<PERSON><PERSON><PERSON>" # user full name
  },
  "stus": "", # open close
}
```

#### Users Document

```yaml
{
  "_id": "",
  "uId": "", # user id in django
  "tkn": "", # auth token django
  "fn": "", # full name
  "avtr": "", # avatar url
  "prvtChts": [ "<list of user ids>" ], # private chats
  "rooms": [ "<list of room ids>" ], # joined rooms
  "jat": "" # joined at date
}
```

### Messages Document

```yaml
{
  _id: "<uuid>",
  "mtyp": "<str>", # mime type text/plain or audio/ogg
  "ctnt": "<str>", # content or file url 
  "at": "", # at datetime 
  "rplyTo": "<uuid>", # replied to message id
  "byU": "<uuid>", # by user 
  "edit": "<bool>", # is edited ? 
  "chat": {
    "type": "r | p", # r: room & p: private
    "id": "<uuid>", # user id or room id
  }
}
```

#### Notifications

```yaml
{
  "_id": "",
  "chat": {
    "type": "r | p", # r: room & p: private
    "id": "<uuid>", # user id or room id,
    "nme": "", # chat name
    "avtr": "" # chat picture url
  },
  "msg": {
    "id": "",
    "prw": "",
  },
  "uId": "", # user id 
  "at": "", # created at
}
```

