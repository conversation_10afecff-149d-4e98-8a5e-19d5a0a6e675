class SocketException(Exception):
    pass


class UserNotInTeam(SocketException):
    """
        User Is Not In Team
    """

    def __str__(self):
        return "User Is Not In Team"


class InvalidToken(SocketException):
    """
        Invalid Auth Token
    """

    def __str__(self):
        return "Invalid Auth Token"


class TeamDoesNoExist(SocketException):
    """
        Invalid Team ID
    """

    def __str__(self):
        return "Invalid Team ID"


class ChatNotFound(SocketException):
    """
        Invalid Chat ID
    """

    def __str__(self):
        return "Invalid Chat ID"
