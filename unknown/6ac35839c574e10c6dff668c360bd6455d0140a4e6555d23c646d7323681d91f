"""Add session_duration, video_call_cost, and voice_call_cost to consultants

Revision ID: 723178515398
Revises: f1e1fc85ec38
Create Date: 2024-06-07 17:11:43.772773

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '723178515398'
down_revision: Union[str, None] = 'f1e1fc85ec38'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
