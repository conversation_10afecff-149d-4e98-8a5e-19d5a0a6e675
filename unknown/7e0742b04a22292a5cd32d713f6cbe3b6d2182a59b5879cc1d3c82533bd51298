"""add timezone to consultant

Revision ID: 4b0e276f7011
Revises: 863d1c4d9be4
Create Date: 2024-01-17 17:03:01.189897

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4b0e276f7011'
down_revision: Union[str, None] = '863d1c4d9be4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('consultants', sa.Column('timezone', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('consultants', 'timezone')
    # ### end Alembic commands ###
