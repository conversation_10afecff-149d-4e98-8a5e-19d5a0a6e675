from sqlalchemy.orm import Query


def paginate(query: Query, page: int, per_page: int):
    """
    Paginates a SQLAlchemy query object.

    :param query: The SQLAlchemy query object to paginate.
    :param page: The current page number.
    :param per_page: The number of items per page.
    :return: A tuple containing the items on the current page and the total number of items.
    """
    c = query.limit(per_page).offset((page - 1) * per_page).all()
    return c
    # items = query.limit(per_page).offset((page - 1) * per_page).all()
    # return items
