version: '3.8'

services:
  chat:
    restart: always
    container_name: chat
    env_file:
      - server.db.env
    build: .
    command: uvicorn main:app --host 0.0.0.0 --ws-ping-interval 9000 --ws-ping-timeout 60.1
    volumes:
      - .:/app
    ports:
      - 8008:8000
    depends_on:
      - chat_db2
    networks:
      - najm_najm


  chat_db2:
    restart: always
    container_name: chat_db
    ports:
      - "5439:5432"
    image: postgres:14.0
    volumes:
      - chat_db2:/var/lib/postgresql/data/
    env_file:
      - server.db.env
    networks:
      - najm_najm



volumes:
  chat_db2:

networks:
  najm_najm:
    external: true
