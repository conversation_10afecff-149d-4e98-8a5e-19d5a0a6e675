import datetime

from sqlalchemy import Column, String, DateTime, Integer

from models import Base
from utils.helpers import generate_token


class Admin(Base):
    __tablename__ = "admins"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True)
    fullname = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    password = Column(String)
    avatar_url = Column(String, nullable=True)
    token = Column(String, default=generate_token, unique=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

    def to_dict(self):
        return {
            'id': str(self.id),
            "username": self.username,
            'is_admin': True,
            'role': 'admin',
            'avatar_url': self.avatar_url,
            'token': self.token,
            'fullname': self.fullname,
            'created_at': str(self.created_at),
        }
