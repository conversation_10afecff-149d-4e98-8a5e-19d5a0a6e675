"""support request update

Revision ID: 59bd891b633b
Revises: d0c58530b65c
Create Date: 2024-04-20 13:48:12.369464

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '59bd891b633b'
down_revision: Union[str, None] = 'd0c58530b65c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(None, 'support_request', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'support_request', type_='foreignkey')
    # ### end Alembic commands ###
