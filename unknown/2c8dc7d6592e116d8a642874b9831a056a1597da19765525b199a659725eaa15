# -*- coding: UTF-8 -*-
import secrets
import hashlib
import time

def generate_signature(app_id, signature_nonce, server_secret, timestamp):
    """Generates an MD5 signature."""
    data = f"{app_id}{signature_nonce}{server_secret}{timestamp}"
    signature = hashlib.md5(data.encode("utf8")).hexdigest()
    return signature

def get_signature_data(app_id, server_secret):
    """Generates necessary data for signature."""
    signature_nonce = secrets.token_hex(8)
    timestamp = int(time.time())
    signature = generate_signature(app_id, signature_nonce, server_secret, timestamp)
    return signature, signature_nonce, timestamp
