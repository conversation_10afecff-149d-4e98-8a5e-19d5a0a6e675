#!/usr/bin/env python3
"""
Unit tests for timezone conversion functionality
"""

import unittest
import sys
import os
from datetime import datetime
import pytz

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.timezone_converter import convert_scheduling_timezone, convert_numeric_timezone_to_string


class TestTimezoneConversion(unittest.TestCase):
    """Test cases for timezone conversion utilities"""

    def test_convert_numeric_timezone_to_string(self):
        """Test conversion of numeric timezone to string"""
        
        # Test common timezone mappings
        test_cases = [
            (3.5, 'Asia/Tehran'),
            (4.0, 'Asia/Dubai'),
            (5.5, 'Asia/Kolkata'),
            (0.0, 'UTC'),
            (1.0, 'Europe/London'),
            (-5.0, 'America/New_York'),
            (None, 'UTC'),
            ('invalid', 'UTC'),
        ]
        
        for input_tz, expected in test_cases:
            with self.subTest(input_tz=input_tz):
                result = convert_numeric_timezone_to_string(input_tz)
                self.assertEqual(result, expected)

    def test_convert_scheduling_timezone_basic(self):
        """Test basic timezone conversion for scheduling"""
        
        scheduling = {
            "monday": ["08:00-12:00", "14:00-18:00"],
            "tuesday": ["09:00-13:00"],
            "wednesday": [],
            "thursday": ["10:00-14:00"],
            "friday": ["08:30-12:30"],
            "saturday": [],
            "sunday": ["09:00-17:00"]
        }
        
        # Convert from Tehran (UTC+3:30) to Dubai (UTC+4:00)
        # Dubai is 30 minutes ahead of Tehran
        result = convert_scheduling_timezone(scheduling, 3.5, 'Asia/Dubai')
        
        # Check that times are shifted by 30 minutes
        self.assertEqual(result['monday'], ['08:30-12:30', '14:30-18:30'])
        self.assertEqual(result['tuesday'], ['09:30-13:30'])
        self.assertEqual(result['wednesday'], [])
        self.assertEqual(result['thursday'], ['10:30-14:30'])
        self.assertEqual(result['friday'], ['09:00-13:00'])
        self.assertEqual(result['saturday'], [])
        self.assertEqual(result['sunday'], ['09:30-17:30'])

    def test_convert_scheduling_timezone_string_input(self):
        """Test timezone conversion with string timezone input"""
        
        scheduling = {
            "monday": ["15:00-19:00"],
            "tuesday": ["15:00-19:00"]
        }
        
        result = convert_scheduling_timezone(scheduling, 'Asia/Tehran', 'Asia/Dubai')
        
        # Tehran to Dubai: +30 minutes
        self.assertEqual(result['monday'], ['15:30-19:30'])
        self.assertEqual(result['tuesday'], ['15:30-19:30'])

    def test_convert_scheduling_timezone_invalid_input(self):
        """Test timezone conversion with invalid inputs"""
        
        # Test with None scheduling
        result = convert_scheduling_timezone(None, 'Asia/Tehran', 'Asia/Dubai')
        self.assertEqual(result, {})
        
        # Test with empty scheduling
        result = convert_scheduling_timezone({}, 'Asia/Tehran', 'Asia/Dubai')
        self.assertEqual(result, {})
        
        # Test with invalid timezone
        scheduling = {"monday": ["15:00-19:00"]}
        result = convert_scheduling_timezone(scheduling, 'Invalid/Timezone', 'Asia/Dubai')
        self.assertEqual(result, scheduling)  # Should return original
        
        # Test with None to_timezone
        result = convert_scheduling_timezone(scheduling, 'Asia/Tehran', None)
        self.assertEqual(result, scheduling)  # Should return original

    def test_convert_scheduling_timezone_malformed_time_ranges(self):
        """Test timezone conversion with malformed time ranges"""
        
        scheduling = {
            "monday": ["15:00-19:00", "invalid-time", "20:00", "21:00-22:00"],
            "tuesday": ["not-a-time-range"],
            "wednesday": [123],  # Non-string value
        }
        
        result = convert_scheduling_timezone(scheduling, 'Asia/Tehran', 'Asia/Dubai')
        
        # Valid time ranges should be converted, invalid ones should be kept as-is
        self.assertEqual(result['monday'], ['15:30-19:30', 'invalid-time', '20:00', '21:30-22:30'])
        self.assertEqual(result['tuesday'], ['not-a-time-range'])
        self.assertEqual(result['wednesday'], [])  # Non-string values should be filtered out

    def test_convert_scheduling_timezone_edge_cases(self):
        """Test timezone conversion edge cases"""
        
        # Test with 24:00 time (should be handled as 00:00)
        scheduling = {
            "monday": ["23:00-24:00", "00:00-01:00"]
        }
        
        result = convert_scheduling_timezone(scheduling, 'Asia/Tehran', 'Asia/Dubai')
        
        # Should handle 24:00 correctly
        self.assertEqual(len(result['monday']), 2)
        self.assertTrue(all('-' in time_range for time_range in result['monday']))

    def test_convert_scheduling_timezone_large_offset(self):
        """Test timezone conversion with large time offset"""
        
        scheduling = {
            "monday": ["08:00-12:00"]
        }
        
        # Convert from Tehran to New York (large offset)
        result = convert_scheduling_timezone(scheduling, 'Asia/Tehran', 'America/New_York')
        
        # Should still work even with large offset
        self.assertEqual(len(result['monday']), 1)
        self.assertTrue('-' in result['monday'][0])

    def test_real_world_scheduling_data(self):
        """Test with real scheduling data from database"""
        
        # Real data from the test output
        real_scheduling = {
            "friday": ["00:00-07:45"],
            "monday": ["04:45-08:15", "00:00-04:45"],
            "sunday": [],
            "tuesday": ["00:00-06:45", "09:30-21:30"],
            "saturday": ["00:00-17:45"],
            "thursday": ["00:00-20:45", "20:45-00:00"],
            "wednesday": ["00:00-09:30", "09:30-21:30"]
        }
        
        # Convert from numeric timezone (1.0) to Asia/Dubai
        result = convert_scheduling_timezone(real_scheduling, 1.0, 'Asia/Dubai')
        
        # Should convert all time ranges
        self.assertIsInstance(result, dict)
        self.assertEqual(len(result), len(real_scheduling))
        
        # Check that non-empty days have converted time ranges
        for day, ranges in result.items():
            if real_scheduling[day]:  # If original had ranges
                self.assertTrue(len(ranges) > 0)  # Converted should also have ranges
                for time_range in ranges:
                    self.assertIn('-', time_range)  # Should be valid time range format


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
