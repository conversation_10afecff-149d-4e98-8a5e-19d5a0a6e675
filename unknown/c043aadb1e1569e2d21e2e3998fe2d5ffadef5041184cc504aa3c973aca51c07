import datetime
import re

from sqlalchemy import Column, String, DateTime, Integer, Boolean
from sqlalchemy.orm import relationship

from models import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True, unique=True)
    username = Column(String)
    fullname = Column(String, nullable=True)

    age = Column(Integer, nullable=True)
    gender = Column(String, nullable=True)
    language_code = Column(String, nullable=True)
    preferred_languages = Column(String, nullable=True)

    country = Column(String, nullable=True)
    city = Column(String, nullable=True)

    avatar_url = Column(String, nullable=True)
    token = Column(String, index=True)
    device_os = Column(String, nullable=True) 

    banned_reason = Column(String, nullable=True, )
    banned_at = Column(DateTime, nullable=True)
    last_update = Column(DateTime, default=datetime.datetime.now, index=True)
    fcm = Column(String, nullable=True, index=True)  # firebase token

    support_requests = relationship("SupportRequest", back_populates="user")
    calls = relationship("Call", back_populates="client")
    is_tester = Column(Boolean, default=False, nullable=False)
    
    def update_token(self, token):
        self.token = token
        self.save()

    def has_preferences(self):
        if self.username and ':' in self.username:
            first_part = self.username.split(':', 1)[0]
            if first_part == 'None':
                return True
        if self.preferred_languages is not None:
            return True
        return not self.age is None
    
    
    def add_preferred_language(self, db, language_code: str):
        """
        Set user's preferred languages. This method replaces the existing preferred languages
        with the new language codes provided.

        Args:
            db: Database session
            language_code: Single language code or comma-separated language codes
                          Examples: 'en' or 'en,fa,id,ur'
        """
        if not language_code:
            return

        # Always treat this as a replacement operation
        # Split the input into individual language codes (handles both single and multiple)
        if ',' in language_code:
            input_languages = language_code.split(',')
        else:
            input_languages = [language_code]

        # Clean and deduplicate the input languages
        unique_languages = []
        for lang in input_languages:
            lang = lang.strip()
            if lang and lang not in unique_languages:
                unique_languages.append(lang)

        # Join the unique languages with commas
        final_updated_languages = ",".join(unique_languages)

        # Update in database
        db.query(User).filter(
            User.id == self.id
        ).update({
            User.preferred_languages: final_updated_languages,
        })

        db.commit()
        
        
                
    def to_dict(self):
        return {
            "username": self.username,
            "fullname": self.fullname,
            "country": self.country,
            "city": self.city,
            "avatar_url": self.avatar_url,
            "is_consultant": False,
            "is_banned": bool(self.banned_at),
            "language_code": self.language_code,
            "has_preferences": self.has_preferences(),
            "age": self.age or None,
            "preferred_languages": list(dict.fromkeys([lang.strip() for lang in self.preferred_languages.split(",") if lang.strip()])) if self.preferred_languages else [],
            "gender": self.gender or None,            
        }

    def __str__(self):
        return self.fullname or '-'

    def __repr__(self):
        return self.fullname or '-'
