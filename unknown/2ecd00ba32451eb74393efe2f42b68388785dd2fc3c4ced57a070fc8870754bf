"""unique category

Revision ID: 863d1c4d9be4
Revises: e52094d514f2
Create Date: 2024-01-17 12:58:53.057197

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '863d1c4d9be4'
down_revision: Union[str, None] = 'e52094d514f2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'categories', ['title'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'categories', type_='unique')
    # ### end Alembic commands ###
