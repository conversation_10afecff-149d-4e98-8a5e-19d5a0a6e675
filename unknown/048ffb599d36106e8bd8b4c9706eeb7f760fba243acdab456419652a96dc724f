"""app v2 changes

Revision ID: e52094d514f2
Revises: 8d4f0119f2a2
Create Date: 2024-01-06 14:22:06.443011

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e52094d514f2'
down_revision: Union[str, None] = '8d4f0119f2a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_categories_id'), 'categories', ['id'], unique=False)
    op.create_table('consultant_categories',
    sa.Column('consultant_id', sa.Integer(), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], ),
    sa.ForeignKeyConstraint(['consultant_id'], ['consultants.id'], )
    )
    op.create_table('rates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user', sa.Integer(), nullable=True),
    sa.Column('consultant', sa.Integer(), nullable=True),
    sa.Column('rate', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['consultant'], ['consultants.id'], ),
    sa.ForeignKeyConstraint(['user'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rates_id'), 'rates', ['id'], unique=False)
    op.add_column('activity_logs', sa.Column('user_id', sa.Integer(), nullable=True))
    op.add_column('activity_logs', sa.Column('consultant_id', sa.Integer(), nullable=True))
    op.add_column('activity_logs', sa.Column('room_id', sa.Integer(), nullable=True))
    op.add_column('activity_logs', sa.Column('activity_type', sa.String(), nullable=True))
    op.add_column('activity_logs', sa.Column('download_link', sa.String(), nullable=True))
    op.create_foreign_key(None, 'activity_logs', 'consultants', ['consultant_id'], ['id'])
    op.create_foreign_key(None, 'activity_logs', 'rooms', ['room_id'], ['id'])
    op.create_foreign_key(None, 'activity_logs', 'users', ['user_id'], ['id'])
    op.drop_column('activity_logs', 'username')
    op.drop_column('activity_logs', 'messages_count')
    op.add_column('consultants', sa.Column('scheduling', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('consultants', 'scheduling')
    op.add_column('activity_logs', sa.Column('messages_count', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('activity_logs', sa.Column('username', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'activity_logs', type_='foreignkey')
    op.drop_constraint(None, 'activity_logs', type_='foreignkey')
    op.drop_constraint(None, 'activity_logs', type_='foreignkey')
    op.drop_column('activity_logs', 'download_link')
    op.drop_column('activity_logs', 'activity_type')
    op.drop_column('activity_logs', 'room_id')
    op.drop_column('activity_logs', 'consultant_id')
    op.drop_column('activity_logs', 'user_id')
    op.drop_index(op.f('ix_rates_id'), table_name='rates')
    op.drop_table('rates')
    op.drop_table('consultant_categories')
    op.drop_index(op.f('ix_categories_id'), table_name='categories')
    op.drop_table('categories')
    # ### end Alembic commands ###
