import datetime

from sqlalchemy import Column, String, DateTime, Integer, ForeignKey
from sqlalchemy.orm import relationship

from models import Base


class File(Base):
    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    path = Column(String)
    mime_type = Column(String)
    user_id = Column(Integer, ForeignKey("users.id"))
    user = relationship("User")
    date = Column(DateTime, default=datetime.datetime.now)
    file_hash = Column(String, nullable=True)

    def __str__(self):
        return self.name

    def dict(self):
        return {
            'path': self.path,
            'mime_type': self.mime_type,
            'id': self.id,
        }
