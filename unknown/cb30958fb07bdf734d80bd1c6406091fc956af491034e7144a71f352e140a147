import datetime


def generate_token():
    import binascii, os
    return binascii.hexlify(os.urandom(20)).decode()


def sqlalchemy_obj_to_dict(obj):
    from sqlalchemy.orm import class_mapper
    mapper = class_mapper(obj.__class__)
    columns = [column.key for column in mapper.columns]
    result = {}

    for column in columns:
        value = getattr(obj, column)
        result[column] = value.strftime('%Y-%m-%d %H:%M') if type(value) is datetime.datetime else value

    return result
