<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Client</title>
</head>
<body>
<h1>WebRTC Client</h1>
<video id="localVideo" autoplay playsinline muted></video>
<video id="remoteVideo" autoplay playsinline></video>
<button onclick="startWebRTC()">Start WebRTC</button>

<script>
    const localVideo = document.getElementById("localVideo");
    const remoteVideo = document.getElementById("remoteVideo");
    const ws = new WebSocket("ws://127.0.0.1:8000/e/12/");

    const configuration = {
        iceServers: [
            {urls: "stun:stun.l.google.com:19302"},
        ],
    };

    let pc = new RTCPeerConnection(configuration);

    navigator.mediaDevices.getUserMedia({video: true, audio: true})
        .then((stream) => {
            localVideo.srcObject = stream;
            stream.getTracks().forEach(track => {
                pc.addTrack(track, stream);
            });
        })
        .catch((error) => {
            console.error('Error accessing media devices:', error);
        });

    ws.addEventListener("message", async (event) => {
        const data = JSON.parse(event.data);

        if (data.type === "offer") {
            await pc.setRemoteDescription(new RTCSessionDescription(data.offer));
            const answer = await pc.createAnswer();
            await pc.setLocalDescription(answer);
            ws.send(JSON.stringify({type: 'answer', answer}));
        } else if (data.type === "answer") {
            await pc.setRemoteDescription(new RTCSessionDescription(data.answer));
        } else if (data.type === "ice-candidate") {
            await pc.addIceCandidate(new RTCIceCandidate(data.iceCandidate));
        }
    });

    pc.onicecandidate = (event) => {
        if (event.candidate) {
            ws.send(JSON.stringify({type: 'ice-candidate', iceCandidate: event.candidate}));
        }
    };

    pc.ontrack = (event) => {
        remoteVideo.srcObject = event.streams[0];
    };

    window.startWebRTC = async () => {
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);
        ws.send(JSON.stringify({type: 'offer', offer}));
    };
</script>
</body>
</html>
