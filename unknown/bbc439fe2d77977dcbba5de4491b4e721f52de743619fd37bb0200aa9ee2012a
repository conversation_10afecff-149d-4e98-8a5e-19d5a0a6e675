<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/html">

<head>
    <script
            src="https://code.jquery.com/jquery-3.4.1.min.js"
            integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo="
            crossorigin="anonymous"></script>

    <title>Chat</title>
    <style>
        html, body {
            height: 100%;
            background: #1a1717;
            outline: none;
            margin: 0;
            padding: 0;
            border: 0;
        }

        * {
            box-sizing: border-box;
        }

        .content {
            width: 100%;
            margin: auto;
            height: 100%;
            border: 1px dashed #414241;
            color: wheat;
        }

        #messageText {
            height: 200px;
            width: 450px;
            border: 1px dashed gray;
            background: #1a1717;
            color: wheat;
        }


        .box {
            padding: 5px;
            margin-bottom: 12px;
            border: 1px solid gray;
        }

        #messages {
            border-top: 1px solid gray;
        }

        aside {
            margin-top: 30px;
            width: 30%;
            float: left;
        }

        div#messages {
            width: 67%;
            float: right;
        }

    </style>
</head>
<body>
<div class="content">
    <h1 style="text-align: center">WebSocket Chat</h1>
    <aside>
        <div class="box">
            <select name="token" id="">
                <option value="consultant:06b0516cee0256b4c39372ed0d693004ccae397e">m</option>
            </select>
            <input type="text" name="token" placeholder="Token">
            <button id="connectSocket">
                Connect To Socket
            </button>
        </div>
        <div class="box">
            <input type="text" name="language" placeholder="Language Code. en">
            <button id="getConsultants">
                getConsultants
            </button>
        </div>
        <div class="box">
            <input type="text" name="room-2" placeholder="room id">
            <button id="get-history">
                Get History
            </button>
        </div>
        <div class="box">
            <br>
            <input name="send-message" type="text" placeholder="message"><br>
            <input name="room-1" type="text" placeholder="room id">
            <button id="send-message">
                Send Message
            </button>
        </div>
                <div class="box">
                    <br>
                    <input name="room-3" type="text" placeholder="room id">
                    <button id="send-message-s">
                        Send Message As Consultant
                    </button>
                </div>

        <div class="box">
            <br>
            <input name="get-rooms" type="text" placeholder="Consultants username">
            <input name="room-page" type="text" placeholder="page">
            <button id="get-rooms"> Get Rooms</button>
        </div>

        <div class="box">
            <br>
            <input name="c-username" type="text" placeholder="Consultants username"><br>
            <input name="c-txt" type="text" placeholder="Message"><br>
            <button id="start-room"> Start Room</button>
        </div>

        <div class="box">
            <br>
            <input name="close-room" type="text" placeholder="Room ID"><br>
            <input name="reason" type="text" placeholder="Reason"><br>
            <button id="close-room"> Close Room</button>
            <button id="open-room"> Open Room</button>
        </div>
        <div class="box">
            <br>
            <input name="read-msg-room" type="text" placeholder="Room ID"><br>
            <button id="read-msg"> Read All Room Message</button>
        </div>
        <div class="box">
            <br>
            <button id="getStats">GetStats</button>
        </div>


        <!--    <div class="box">-->
        <!--        <button id="send-photo">-->
        <!--            Send Photo-->
        <!--        </button>-->
        <!--        <input name="send-photo" type="file">-->
        <!--    </div>-->

        <!--    <div class="box">-->
        <!--        <button id="send-video">-->
        <!--            Send Video-->
        <!--        </button>-->
        <!--        <input name="send-video" type="file">-->
        <!--    </div>-->
        <!--    <div class="box">-->
        <!--        <button id="send-document">-->
        <!--            Send Document-->
        <!--        </button>-->
        <!--        <input name="send-document" type="file">-->
        <!--    </div>-->
    </aside>


    <div id='messages'></div>
</div>

<script>
    let base_url = "{{ socket_protocol }}://{{ host }}/ws/?t="

    function isOpen(wsc) {
        return wsc.readyState === wsc.OPEN
    }

    function sendMsg(wsc, data) {
        if (!isOpen(wsc)) {
            console.log('websocket is not ready yet')
            return
        }
        wsc.send(JSON.stringify(data))
    }

    $('#connectSocket').click(function () {
        let token = $('input[name="token"]').val() || $('select[name="token"]').val()

    {#    try {
            window.ws.close()

        } catch (e) {
            console.log(e)
        }#}


        window.ws = new WebSocket(base_url + token + "&language_code=en");
        ws.onmessage = function (event) {
            try {

                let data = JSON.parse(event.data)
                console.log(data)
                $('#messages').html(`<pre>${JSON.stringify(data, undefined, 2)}</pre>`)

            } catch (erro) {
                $('#messages').html(`<pre>${event.data}</pre>`)
            }
        };


    })

    $("#get-history").click(function () {
        let data = {
            "room": $("input[name=room-2]").val(),
            "action": "getHistory",
        }
        if (!isOpen(ws)) {
            console.log('websocket is not ready yet')
            return
        }
        sendMsg(ws, data)
    })

    $("#getConsultants").click(function () {
        let data = {
            "action": "getConsultants",
            "language": $('input[name="language"]').val(),
        }
        sendMsg(ws, data)
    })

    $("#get-rooms").click(function () {
        let data = {
            "action": "getRooms",
            "consultant": $("input[name=get-rooms]").val(),
            'page': $('input[name=room-page]').val() || 1,
        }
        sendMsg(ws, data)
    })

    $("#start-room").click(function () {
        let data = {
            "action": "startRoom",
            "consultant": $("input[name=c-username]").val(),
            "message": $("input[name=c-txt]").val(),
        }
        sendMsg(ws, data)
    })

    $("#close-room").click(function () {
        let data = {
            "action": "closeRoom",
            "room_id": $('input[name=close-room]').val(),
            "reason": $('input[name=reason]').val(),
        }
        sendMsg(ws, data)
    })
    $("#open-room").click(function () {
        let data = {
            "action": "openRoom",
            "room_id": $('input[name=close-room]').val()
        }
        sendMsg(ws, data)
    })

    $("#read-msg").click(function () {
        let data = {
            "action": "readMsg",
            "room_id": $('input[name=read-msg-room]').val(),
        }
        sendMsg(ws, data)
    })

    $("#send-message").click(function () {
        let data = {
            "room": $("input[name=room-1]").val(),
            "action": "sendMessage",
            "text": $("input[name=send-message]").val()
        }
        sendMsg(ws, data)
    })
    $("#send-message-s").click(function () {
        let data = {
            "room": $("input[name=room-3]").val(),
            "action": "ctest",
        }
        sendMsg(ws, data)
    })

    $("#getStats").click(function () {
        let data = {
            "action": "getStats",
        }
        sendMsg(ws, data)
    })


    // $("#send-video").click(function () {
    //     uploadFile("send-video", function (f) {
    //         let data = {
    //             "chat_type": "team",
    //             "chat_id": room,
    //             "action": "sendVideo",
    //             "video": f['result'],
    //         }
    //         ws.send(JSON.stringify(data))
    //     })
    // })

    // $("#send-photo").click(function () {
    //     uploadFile("send-photo", function (f) {
    //         let data = {
    //             "chat_type": "team",
    //             "chat_id": room,
    //             "action": "sendPhoto",
    //             "photo": f['result'],
    //             'preview': 'pEHV6nWB2yk8$NxujFpyoJadR*=ss:I[.7kCMdnjx]S2NHS#M|%1%2ENRis9Sis.slNHW:WBxZogaekBW;ofo0Rk',
    //         }
    //         ws.send(JSON.stringify(data))
    //     })
    // })
    //
    // $("#send-document").click(function () {
    //     uploadFile("send-document", function (f) {
    //         let data = {
    //             "chat_type": "team",
    //             "chat_id": room,
    //             "action": "sendDocument",
    //             "document": f['result'],
    //         }
    //         ws.send(JSON.stringify(data))
    //     })
    // })
    //
    // function uploadFile(fileINPUT = "file", onsuccess) {
    //     let file = $(`input[name=${fileINPUT}]`)[0].files[0]
    //     let formData = new FormData();
    //     formData.append('file', file);
    //     formData.append('token', user);
    //     $.ajax({
    //         url: '/upload/',
    //         type: 'POST',
    //         data: formData,
    //         processData: false,  // tell jQuery not to process the data
    //         contentType: false,  // tell jQuery not to set contentType
    //         success: function (data) {
    //             onsuccess(data)
    //         },
    //         error: function (data) {
    //             alert(data)
    //         }
    //     });
    // }


</script>

</body>
</html>
