!function(e){var t=window.webpackHotUpdate;window.webpackHotUpdate=function(e,r){!function(e,t){if(!x[e]||!w[e])return;for(var r in w[e]=!1,t)Object.prototype.hasOwnProperty.call(t,r)&&(g[r]=t[r]);0==--y&&0===v&&T()}(e,r),t&&t(e,r)};var r,n=!0,o="01ae00eea611ada5e9c7",a=1e4,i={},s=[],l=[];function c(e){var t=C[e];if(!t)return E;var n=function(n){return t.hot.active?(C[n]?-1===C[n].parents.indexOf(e)&&C[n].parents.push(e):(s=[e],r=n),-1===t.children.indexOf(n)&&t.children.push(n)):(console.warn("[HMR] unexpected require("+n+") from disposed module "+e),s=[]),E(n)},o=function(e){return{configurable:!0,enumerable:!0,get:function(){return E[e]},set:function(t){E[e]=t}}};for(var a in E)Object.prototype.hasOwnProperty.call(E,a)&&"e"!==a&&"t"!==a&&Object.defineProperty(n,a,o(a));return n.e=function(e){return"ready"===f&&p("prepare"),v++,E.e(e).then(t,(function(e){throw t(),e}));function t(){v--,"prepare"===f&&(b[e]||k(e),0===v&&0===y&&T())}},n.t=function(e,t){return 1&t&&(e=n(e)),E.t(e,-2&t)},n}function u(e){var t={_acceptedDependencies:{},_declinedDependencies:{},_selfAccepted:!1,_selfDeclined:!1,_disposeHandlers:[],_main:r!==e,active:!0,accept:function(e,r){if(void 0===e)t._selfAccepted=!0;else if("function"==typeof e)t._selfAccepted=e;else if("object"==typeof e)for(var n=0;n<e.length;n++)t._acceptedDependencies[e[n]]=r||function(){};else t._acceptedDependencies[e]=r||function(){}},decline:function(e){if(void 0===e)t._selfDeclined=!0;else if("object"==typeof e)for(var r=0;r<e.length;r++)t._declinedDependencies[e[r]]=!0;else t._declinedDependencies[e]=!0},dispose:function(e){t._disposeHandlers.push(e)},addDisposeHandler:function(e){t._disposeHandlers.push(e)},removeDisposeHandler:function(e){var r=t._disposeHandlers.indexOf(e);r>=0&&t._disposeHandlers.splice(r,1)},check:P,apply:V,status:function(e){if(!e)return f;d.push(e)},addStatusHandler:function(e){d.push(e)},removeStatusHandler:function(e){var t=d.indexOf(e);t>=0&&d.splice(t,1)},data:i[e]};return r=void 0,t}var d=[],f="idle";function p(e){f=e;for(var t=0;t<d.length;t++)d[t].call(null,e)}var h,g,m,y=0,v=0,b={},w={},x={};function S(e){return+e+""===e?+e:e}function P(e){if("idle"!==f)throw new Error("check() is only allowed in idle status");return n=e,p("check"),(t=a,t=t||1e4,new Promise((function(e,r){if("undefined"==typeof XMLHttpRequest)return r(new Error("No browser support"));try{var n=new XMLHttpRequest,a=E.p+""+o+".hot-update.json";n.open("GET",a,!0),n.timeout=t,n.send(null)}catch(e){return r(e)}n.onreadystatechange=function(){if(4===n.readyState)if(0===n.status)r(new Error("Manifest request to "+a+" timed out."));else if(404===n.status)e();else if(200!==n.status&&304!==n.status)r(new Error("Manifest request to "+a+" failed."));else{try{var t=JSON.parse(n.responseText)}catch(e){return void r(e)}e(t)}}}))).then((function(e){if(!e)return p("idle"),null;w={},b={},x=e.c,m=e.h,p("prepare");var t=new Promise((function(e,t){h={resolve:e,reject:t}}));g={};return k(0),"prepare"===f&&0===v&&0===y&&T(),t}));var t}function k(e){x[e]?(w[e]=!0,y++,function(e){var t=document.createElement("script");t.charset="utf-8",t.src=E.p+""+e+"."+o+".hot-update.js",document.head.appendChild(t)}(e)):b[e]=!0}function T(){p("ready");var e=h;if(h=null,e)if(n)Promise.resolve().then((function(){return V(n)})).then((function(t){e.resolve(t)}),(function(t){e.reject(t)}));else{var t=[];for(var r in g)Object.prototype.hasOwnProperty.call(g,r)&&t.push(S(r));e.resolve(t)}}function V(t){if("ready"!==f)throw new Error("apply() is only allowed in ready status");var r,n,a,l,c;function u(e){for(var t=[e],r={},n=t.map((function(e){return{chain:[e],id:e}}));n.length>0;){var o=n.pop(),a=o.id,i=o.chain;if((l=C[a])&&!l.hot._selfAccepted){if(l.hot._selfDeclined)return{type:"self-declined",chain:i,moduleId:a};if(l.hot._main)return{type:"unaccepted",chain:i,moduleId:a};for(var s=0;s<l.parents.length;s++){var c=l.parents[s],u=C[c];if(u){if(u.hot._declinedDependencies[a])return{type:"declined",chain:i.concat([c]),moduleId:a,parentId:c};-1===t.indexOf(c)&&(u.hot._acceptedDependencies[a]?(r[c]||(r[c]=[]),d(r[c],[a])):(delete r[c],t.push(c),n.push({chain:i.concat([c]),id:c})))}}}}return{type:"accepted",moduleId:e,outdatedModules:t,outdatedDependencies:r}}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];-1===e.indexOf(n)&&e.push(n)}}t=t||{};var h={},y=[],v={},b=function(){console.warn("[HMR] unexpected require("+P.moduleId+") to disposed module")};for(var w in g)if(Object.prototype.hasOwnProperty.call(g,w)){var P;c=S(w);var k=!1,T=!1,V=!1,O="";switch((P=g[w]?u(c):{type:"disposed",moduleId:w}).chain&&(O="\nUpdate propagation: "+P.chain.join(" -> ")),P.type){case"self-declined":t.onDeclined&&t.onDeclined(P),t.ignoreDeclined||(k=new Error("Aborted because of self decline: "+P.moduleId+O));break;case"declined":t.onDeclined&&t.onDeclined(P),t.ignoreDeclined||(k=new Error("Aborted because of declined dependency: "+P.moduleId+" in "+P.parentId+O));break;case"unaccepted":t.onUnaccepted&&t.onUnaccepted(P),t.ignoreUnaccepted||(k=new Error("Aborted because "+c+" is not accepted"+O));break;case"accepted":t.onAccepted&&t.onAccepted(P),T=!0;break;case"disposed":t.onDisposed&&t.onDisposed(P),V=!0;break;default:throw new Error("Unexception type "+P.type)}if(k)return p("abort"),Promise.reject(k);if(T)for(c in v[c]=g[c],d(y,P.outdatedModules),P.outdatedDependencies)Object.prototype.hasOwnProperty.call(P.outdatedDependencies,c)&&(h[c]||(h[c]=[]),d(h[c],P.outdatedDependencies[c]));V&&(d(y,[P.moduleId]),v[c]=b)}var M,A=[];for(n=0;n<y.length;n++)c=y[n],C[c]&&C[c].hot._selfAccepted&&v[c]!==b&&A.push({module:c,errorHandler:C[c].hot._selfAccepted});p("dispose"),Object.keys(x).forEach((function(e){!1===x[e]&&function(e){delete installedChunks[e]}(e)}));for(var j,L,H=y.slice();H.length>0;)if(c=H.pop(),l=C[c]){var F={},R=l.hot._disposeHandlers;for(a=0;a<R.length;a++)(r=R[a])(F);for(i[c]=F,l.hot.active=!1,delete C[c],delete h[c],a=0;a<l.children.length;a++){var N=C[l.children[a]];N&&((M=N.parents.indexOf(c))>=0&&N.parents.splice(M,1))}}for(c in h)if(Object.prototype.hasOwnProperty.call(h,c)&&(l=C[c]))for(L=h[c],a=0;a<L.length;a++)j=L[a],(M=l.children.indexOf(j))>=0&&l.children.splice(M,1);for(c in p("apply"),o=m,v)Object.prototype.hasOwnProperty.call(v,c)&&(e[c]=v[c]);var I=null;for(c in h)if(Object.prototype.hasOwnProperty.call(h,c)&&(l=C[c])){L=h[c];var B=[];for(n=0;n<L.length;n++)if(j=L[n],r=l.hot._acceptedDependencies[j]){if(-1!==B.indexOf(r))continue;B.push(r)}for(n=0;n<B.length;n++){r=B[n];try{r(L)}catch(e){t.onErrored&&t.onErrored({type:"accept-errored",moduleId:c,dependencyId:L[n],error:e}),t.ignoreErrored||I||(I=e)}}}for(n=0;n<A.length;n++){var q=A[n];c=q.module,s=[c];try{E(c)}catch(e){if("function"==typeof q.errorHandler)try{q.errorHandler(e)}catch(r){t.onErrored&&t.onErrored({type:"self-accept-error-handler-errored",moduleId:c,error:r,originalError:e}),t.ignoreErrored||I||(I=r),I||(I=e)}else t.onErrored&&t.onErrored({type:"self-accept-errored",moduleId:c,error:e}),t.ignoreErrored||I||(I=e)}}return I?(p("fail"),Promise.reject(I)):(p("idle"),new Promise((function(e){e(y)})))}var C={};function E(t){if(C[t])return C[t].exports;var r=C[t]={i:t,l:!1,exports:{},hot:u(t),parents:(l=s,s=[],l),children:[]};return e[t].call(r.exports,r,r.exports,c(t)),r.l=!0,r.exports}E.m=e,E.c=C,E.d=function(e,t,r){E.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},E.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},E.t=function(e,t){if(1&t&&(e=E(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(E.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)E.d(r,n,function(t){return e[t]}.bind(null,n));return r},E.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return E.d(t,"a",t),t},E.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},E.p="",E.h=function(){return o},c(8)(E.s=8)}([function(e,t,r){var n,o;/*! VelocityJS.org (1.5.2). (C) 2014 Julian Shapiro. MIT @license: en.wikipedia.org/wiki/MIT_License */
/*! VelocityJS.org jQuery Shim (1.0.1). (C) 2014 The jQuery Foundation. MIT @license: en.wikipedia.org/wiki/MIT_License. */!function(e){"use strict";if(!e.jQuery){var t=function(e,r){return new t.fn.init(e,r)};t.isWindow=function(e){return e&&e===e.window},t.type=function(e){return e?"object"==typeof e||"function"==typeof e?n[a.call(e)]||"object":typeof e:e+""},t.isArray=Array.isArray||function(e){return"array"===t.type(e)},t.isPlainObject=function(e){var r;if(!e||"object"!==t.type(e)||e.nodeType||t.isWindow(e))return!1;try{if(e.constructor&&!o.call(e,"constructor")&&!o.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}for(r in e);return void 0===r||o.call(e,r)},t.each=function(e,t,r){var n=0,o=e.length,a=l(e);if(r){if(a)for(;n<o&&!1!==t.apply(e[n],r);n++);else for(n in e)if(e.hasOwnProperty(n)&&!1===t.apply(e[n],r))break}else if(a)for(;n<o&&!1!==t.call(e[n],n,e[n]);n++);else for(n in e)if(e.hasOwnProperty(n)&&!1===t.call(e[n],n,e[n]))break;return e},t.data=function(e,n,o){if(void 0===o){var a=e[t.expando],i=a&&r[a];if(void 0===n)return i;if(i&&n in i)return i[n]}else if(void 0!==n){var s=e[t.expando]||(e[t.expando]=++t.uuid);return r[s]=r[s]||{},r[s][n]=o,o}},t.removeData=function(e,n){var o=e[t.expando],a=o&&r[o];a&&(n?t.each(n,(function(e,t){delete a[t]})):delete r[o])},t.extend=function(){var e,r,n,o,a,i,s=arguments[0]||{},l=1,c=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[l]||{},l++),"object"!=typeof s&&"function"!==t.type(s)&&(s={}),l===c&&(s=this,l--);l<c;l++)if(a=arguments[l])for(o in a)a.hasOwnProperty(o)&&(e=s[o],s!==(n=a[o])&&(u&&n&&(t.isPlainObject(n)||(r=t.isArray(n)))?(r?(r=!1,i=e&&t.isArray(e)?e:[]):i=e&&t.isPlainObject(e)?e:{},s[o]=t.extend(u,i,n)):void 0!==n&&(s[o]=n)));return s},t.queue=function(e,r,n){if(e){r=(r||"fx")+"queue";var o,a,i,s=t.data(e,r);return n?(!s||t.isArray(n)?s=t.data(e,r,(i=a||[],(o=n)&&(l(Object(o))?function(e,t){for(var r=+t.length,n=0,o=e.length;n<r;)e[o++]=t[n++];if(r!=r)for(;void 0!==t[n];)e[o++]=t[n++];e.length=o}(i,"string"==typeof o?[o]:o):[].push.call(i,o)),i)):s.push(n),s):s||[]}},t.dequeue=function(e,r){t.each(e.nodeType?[e]:e,(function(e,n){r=r||"fx";var o=t.queue(n,r),a=o.shift();"inprogress"===a&&(a=o.shift()),a&&("fx"===r&&o.unshift("inprogress"),a.call(n,(function(){t.dequeue(n,r)})))}))},t.fn=t.prototype={init:function(e){if(e.nodeType)return this[0]=e,this;throw new Error("Not a DOM node.")},offset:function(){var t=this[0].getBoundingClientRect?this[0].getBoundingClientRect():{top:0,left:0};return{top:t.top+(e.pageYOffset||document.scrollTop||0)-(document.clientTop||0),left:t.left+(e.pageXOffset||document.scrollLeft||0)-(document.clientLeft||0)}},position:function(){var e=this[0],r=function(e){for(var t=e.offsetParent;t&&"html"!==t.nodeName.toLowerCase()&&t.style&&"static"===t.style.position.toLowerCase();)t=t.offsetParent;return t||document}(e),n=this.offset(),o=/^(?:body|html)$/i.test(r.nodeName)?{top:0,left:0}:t(r).offset();return n.top-=parseFloat(e.style.marginTop)||0,n.left-=parseFloat(e.style.marginLeft)||0,r.style&&(o.top+=parseFloat(r.style.borderTopWidth)||0,o.left+=parseFloat(r.style.borderLeftWidth)||0),{top:n.top-o.top,left:n.left-o.left}}};var r={};t.expando="velocity"+(new Date).getTime(),t.uuid=0;for(var n={},o=n.hasOwnProperty,a=n.toString,i="Boolean Number String Function Array Date RegExp Object Error".split(" "),s=0;s<i.length;s++)n["[object "+i[s]+"]"]=i[s].toLowerCase();t.fn.init.prototype=t.fn,e.Velocity={Utilities:t}}function l(e){var r=e.length,n=t.type(e);return"function"!==n&&!t.isWindow(e)&&(!(1!==e.nodeType||!r)||("array"===n||0===r||"number"==typeof r&&r>0&&r-1 in e))}}(window),function(a){"use strict";"object"==typeof e.exports?e.exports=a():void 0===(o="function"==typeof(n=a)?n.call(t,r,t,e):n)||(e.exports=o)}((function(){"use strict";return function(e,t,r,n){var o,a=function(){if(r.documentMode)return r.documentMode;for(var e=7;e>4;e--){var t=r.createElement("div");if(t.innerHTML="\x3c!--[if IE "+e+"]><span></span><![endif]--\x3e",t.getElementsByTagName("span").length)return t=null,e}return n}(),i=(o=0,t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||function(e){var t,r=(new Date).getTime();return t=Math.max(0,16-(r-o)),o=r+t,setTimeout((function(){e(r+t)}),t)}),s=function(){var e=t.performance||{};if("function"!=typeof e.now){var r=e.timing&&e.timing.navigationStart?e.timing.navigationStart:(new Date).getTime();e.now=function(){return(new Date).getTime()-r}}return e}();var l=function(){var e=Array.prototype.slice;try{return e.call(r.documentElement),e}catch(t){return function(t,r){var n=this.length;if("number"!=typeof t&&(t=0),"number"!=typeof r&&(r=n),this.slice)return e.call(this,t,r);var o,a=[],i=t>=0?t:Math.max(0,n+t),s=(r<0?n+r:Math.min(r,n))-i;if(s>0)if(a=new Array(s),this.charAt)for(o=0;o<s;o++)a[o]=this.charAt(i+o);else for(o=0;o<s;o++)a[o]=this[i+o];return a}}}(),c=function(){return Array.prototype.includes?function(e,t){return e.includes(t)}:Array.prototype.indexOf?function(e,t){return e.indexOf(t)>=0}:function(e,t){for(var r=0;r<e.length;r++)if(e[r]===t)return!0;return!1}};function u(e){return f.isWrapped(e)?e=l.call(e):f.isNode(e)&&(e=[e]),e}var d,f={isNumber:function(e){return"number"==typeof e},isString:function(e){return"string"==typeof e},isArray:Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},isFunction:function(e){return"[object Function]"===Object.prototype.toString.call(e)},isNode:function(e){return e&&e.nodeType},isWrapped:function(e){return e&&e!==t&&f.isNumber(e.length)&&!f.isString(e)&&!f.isFunction(e)&&!f.isNode(e)&&(0===e.length||f.isNode(e[0]))},isSVG:function(e){return t.SVGElement&&e instanceof t.SVGElement},isEmptyObject:function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}},p=!1;if(e.fn&&e.fn.jquery?(d=e,p=!0):d=t.Velocity.Utilities,a<=8&&!p)throw new Error("Velocity: IE8 and below require jQuery to be loaded before Velocity.");if(!(a<=7)){var h=400,g="swing",m={State:{isMobile:/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t.navigator.userAgent),isAndroid:/Android/i.test(t.navigator.userAgent),isGingerbread:/Android 2\.3\.[3-7]/i.test(t.navigator.userAgent),isChrome:t.chrome,isFirefox:/Firefox/i.test(t.navigator.userAgent),prefixElement:r.createElement("div"),prefixMatches:{},scrollAnchor:null,scrollPropertyLeft:null,scrollPropertyTop:null,isTicking:!1,calls:[],delayedElements:{count:0}},CSS:{},Utilities:d,Redirects:{},Easings:{},Promise:t.Promise,defaults:{queue:"",duration:h,easing:g,begin:n,complete:n,progress:n,display:n,visibility:n,loop:!1,delay:!1,mobileHA:!0,_cacheValues:!0,promiseRejectEmpty:!0},init:function(e){d.data(e,"velocity",{isSVG:f.isSVG(e),isAnimating:!1,computedStyle:null,tweensContainer:null,rootPropertyValueCache:{},transformCache:{}})},hook:null,mock:!1,version:{major:1,minor:5,patch:2},debug:!1,timestamp:!0,pauseAll:function(e){var t=(new Date).getTime();d.each(m.State.calls,(function(t,r){if(r){if(e!==n&&(r[2].queue!==e||!1===r[2].queue))return!0;r[5]={resume:!1}}})),d.each(m.State.delayedElements,(function(e,r){r&&P(r,t)}))},resumeAll:function(e){var t=(new Date).getTime();d.each(m.State.calls,(function(t,r){if(r){if(e!==n&&(r[2].queue!==e||!1===r[2].queue))return!0;r[5]&&(r[5].resume=!0)}})),d.each(m.State.delayedElements,(function(e,r){r&&k(r,t)}))}};t.pageYOffset!==n?(m.State.scrollAnchor=t,m.State.scrollPropertyLeft="pageXOffset",m.State.scrollPropertyTop="pageYOffset"):(m.State.scrollAnchor=r.documentElement||r.body.parentNode||r.body,m.State.scrollPropertyLeft="scrollLeft",m.State.scrollPropertyTop="scrollTop");var y=function(){function e(e){return-e.tension*e.x-e.friction*e.v}function t(t,r,n){var o={x:t.x+n.dx*r,v:t.v+n.dv*r,tension:t.tension,friction:t.friction};return{dx:o.v,dv:e(o)}}function r(r,n){var o={dx:r.v,dv:e(r)},a=t(r,.5*n,o),i=t(r,.5*n,a),s=t(r,n,i),l=1/6*(o.dx+2*(a.dx+i.dx)+s.dx),c=1/6*(o.dv+2*(a.dv+i.dv)+s.dv);return r.x=r.x+l*n,r.v=r.v+c*n,r}return function e(t,n,o){var a,i,s,l={x:-1,v:0,tension:null,friction:null},c=[0],u=0;for(t=parseFloat(t)||500,n=parseFloat(n)||20,o=o||null,l.tension=t,l.friction=n,i=(a=null!==o)?(u=e(t,n))/o*.016:.016;s=r(s||l,i),c.push(1+s.x),u+=16,Math.abs(s.x)>1e-4&&Math.abs(s.v)>1e-4;);return a?function(e){return c[e*(c.length-1)|0]}:u}}();m.Easings={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},spring:function(e){return 1-Math.cos(4.5*e*Math.PI)*Math.exp(6*-e)}},d.each([["ease",[.25,.1,.25,1]],["ease-in",[.42,0,1,1]],["ease-out",[0,0,.58,1]],["ease-in-out",[.42,0,.58,1]],["easeInSine",[.47,0,.745,.715]],["easeOutSine",[.39,.575,.565,1]],["easeInOutSine",[.445,.05,.55,.95]],["easeInQuad",[.55,.085,.68,.53]],["easeOutQuad",[.25,.46,.45,.94]],["easeInOutQuad",[.455,.03,.515,.955]],["easeInCubic",[.55,.055,.675,.19]],["easeOutCubic",[.215,.61,.355,1]],["easeInOutCubic",[.645,.045,.355,1]],["easeInQuart",[.895,.03,.685,.22]],["easeOutQuart",[.165,.84,.44,1]],["easeInOutQuart",[.77,0,.175,1]],["easeInQuint",[.755,.05,.855,.06]],["easeOutQuint",[.23,1,.32,1]],["easeInOutQuint",[.86,0,.07,1]],["easeInExpo",[.95,.05,.795,.035]],["easeOutExpo",[.19,1,.22,1]],["easeInOutExpo",[1,0,0,1]],["easeInCirc",[.6,.04,.98,.335]],["easeOutCirc",[.075,.82,.165,1]],["easeInOutCirc",[.785,.135,.15,.86]]],(function(e,t){m.Easings[t[0]]=V.apply(null,t[1])}));var v=m.CSS={RegEx:{isHex:/^#([A-f\d]{3}){1,2}$/i,valueUnwrap:/^[A-z]+\((.*)\)$/i,wrappedValueAlreadyExtracted:/[0-9.]+ [0-9.]+ [0-9.]+( [0-9.]+)?/,valueSplit:/([A-z]+\(.+\))|(([A-z0-9#-.]+?)(?=\s|$))/gi},Lists:{colors:["fill","stroke","stopColor","color","backgroundColor","borderColor","borderTopColor","borderRightColor","borderBottomColor","borderLeftColor","outlineColor"],transformsBase:["translateX","translateY","scale","scaleX","scaleY","skewX","skewY","rotateZ"],transforms3D:["transformPerspective","translateZ","scaleZ","rotateX","rotateY"],units:["%","em","ex","ch","rem","vw","vh","vmin","vmax","cm","mm","Q","in","pc","pt","px","deg","grad","rad","turn","s","ms"],colorNames:{aliceblue:"240,248,255",antiquewhite:"250,235,215",aquamarine:"127,255,212",aqua:"0,255,255",azure:"240,255,255",beige:"245,245,220",bisque:"255,228,196",black:"0,0,0",blanchedalmond:"255,235,205",blueviolet:"138,43,226",blue:"0,0,255",brown:"165,42,42",burlywood:"222,184,135",cadetblue:"95,158,160",chartreuse:"127,255,0",chocolate:"210,105,30",coral:"255,127,80",cornflowerblue:"100,149,237",cornsilk:"255,248,220",crimson:"220,20,60",cyan:"0,255,255",darkblue:"0,0,139",darkcyan:"0,139,139",darkgoldenrod:"184,134,11",darkgray:"169,169,169",darkgrey:"169,169,169",darkgreen:"0,100,0",darkkhaki:"189,183,107",darkmagenta:"139,0,139",darkolivegreen:"85,107,47",darkorange:"255,140,0",darkorchid:"153,50,204",darkred:"139,0,0",darksalmon:"233,150,122",darkseagreen:"143,188,143",darkslateblue:"72,61,139",darkslategray:"47,79,79",darkturquoise:"0,206,209",darkviolet:"148,0,211",deeppink:"255,20,147",deepskyblue:"0,191,255",dimgray:"105,105,105",dimgrey:"105,105,105",dodgerblue:"30,144,255",firebrick:"178,34,34",floralwhite:"255,250,240",forestgreen:"34,139,34",fuchsia:"255,0,255",gainsboro:"220,220,220",ghostwhite:"248,248,255",gold:"255,215,0",goldenrod:"218,165,32",gray:"128,128,128",grey:"128,128,128",greenyellow:"173,255,47",green:"0,128,0",honeydew:"240,255,240",hotpink:"255,105,180",indianred:"205,92,92",indigo:"75,0,130",ivory:"255,255,240",khaki:"240,230,140",lavenderblush:"255,240,245",lavender:"230,230,250",lawngreen:"124,252,0",lemonchiffon:"255,250,205",lightblue:"173,216,230",lightcoral:"240,128,128",lightcyan:"224,255,255",lightgoldenrodyellow:"250,250,210",lightgray:"211,211,211",lightgrey:"211,211,211",lightgreen:"144,238,144",lightpink:"255,182,193",lightsalmon:"255,160,122",lightseagreen:"32,178,170",lightskyblue:"135,206,250",lightslategray:"119,136,153",lightsteelblue:"176,196,222",lightyellow:"255,255,224",limegreen:"50,205,50",lime:"0,255,0",linen:"250,240,230",magenta:"255,0,255",maroon:"128,0,0",mediumaquamarine:"102,205,170",mediumblue:"0,0,205",mediumorchid:"186,85,211",mediumpurple:"147,112,219",mediumseagreen:"60,179,113",mediumslateblue:"123,104,238",mediumspringgreen:"0,250,154",mediumturquoise:"72,209,204",mediumvioletred:"199,21,133",midnightblue:"25,25,112",mintcream:"245,255,250",mistyrose:"255,228,225",moccasin:"255,228,181",navajowhite:"255,222,173",navy:"0,0,128",oldlace:"253,245,230",olivedrab:"107,142,35",olive:"128,128,0",orangered:"255,69,0",orange:"255,165,0",orchid:"218,112,214",palegoldenrod:"238,232,170",palegreen:"152,251,152",paleturquoise:"175,238,238",palevioletred:"219,112,147",papayawhip:"255,239,213",peachpuff:"255,218,185",peru:"205,133,63",pink:"255,192,203",plum:"221,160,221",powderblue:"176,224,230",purple:"128,0,128",red:"255,0,0",rosybrown:"188,143,143",royalblue:"65,105,225",saddlebrown:"139,69,19",salmon:"250,128,114",sandybrown:"244,164,96",seagreen:"46,139,87",seashell:"255,245,238",sienna:"160,82,45",silver:"192,192,192",skyblue:"135,206,235",slateblue:"106,90,205",slategray:"112,128,144",snow:"255,250,250",springgreen:"0,255,127",steelblue:"70,130,180",tan:"210,180,140",teal:"0,128,128",thistle:"216,191,216",tomato:"255,99,71",turquoise:"64,224,208",violet:"238,130,238",wheat:"245,222,179",whitesmoke:"245,245,245",white:"255,255,255",yellowgreen:"154,205,50",yellow:"255,255,0"}},Hooks:{templates:{textShadow:["Color X Y Blur","black 0px 0px 0px"],boxShadow:["Color X Y Blur Spread","black 0px 0px 0px 0px"],clip:["Top Right Bottom Left","0px 0px 0px 0px"],backgroundPosition:["X Y","0% 0%"],transformOrigin:["X Y Z","50% 50% 0px"],perspectiveOrigin:["X Y","50% 50%"]},registered:{},register:function(){for(var e=0;e<v.Lists.colors.length;e++){var t="color"===v.Lists.colors[e]?"0 0 0 1":"255 255 255 1";v.Hooks.templates[v.Lists.colors[e]]=["Red Green Blue Alpha",t]}var r,n,o;if(a)for(r in v.Hooks.templates)if(v.Hooks.templates.hasOwnProperty(r)){o=(n=v.Hooks.templates[r])[0].split(" ");var i=n[1].match(v.RegEx.valueSplit);"Color"===o[0]&&(o.push(o.shift()),i.push(i.shift()),v.Hooks.templates[r]=[o.join(" "),i.join(" ")])}for(r in v.Hooks.templates)if(v.Hooks.templates.hasOwnProperty(r))for(var s in o=(n=v.Hooks.templates[r])[0].split(" "))if(o.hasOwnProperty(s)){var l=r+o[s],c=s;v.Hooks.registered[l]=[r,c]}},getRoot:function(e){var t=v.Hooks.registered[e];return t?t[0]:e},getUnit:function(e,t){var r=(e.substr(t||0,5).match(/^[a-z%]+/)||[])[0]||"";return r&&c(v.Lists.units,r)?r:""},fixColors:function(e){return e.replace(/(rgba?\(\s*)?(\b[a-z]+\b)/g,(function(e,t,r){return v.Lists.colorNames.hasOwnProperty(r)?(t||"rgba(")+v.Lists.colorNames[r]+(t?"":",1)"):t+r}))},cleanRootPropertyValue:function(e,t){return v.RegEx.valueUnwrap.test(t)&&(t=t.match(v.RegEx.valueUnwrap)[1]),v.Values.isCSSNullValue(t)&&(t=v.Hooks.templates[e][1]),t},extractValue:function(e,t){var r=v.Hooks.registered[e];if(r){var n=r[0],o=r[1];return(t=v.Hooks.cleanRootPropertyValue(n,t)).toString().match(v.RegEx.valueSplit)[o]}return t},injectValue:function(e,t,r){var n=v.Hooks.registered[e];if(n){var o,a=n[0],i=n[1];return(o=(r=v.Hooks.cleanRootPropertyValue(a,r)).toString().match(v.RegEx.valueSplit))[i]=t,o.join(" ")}return r}},Normalizations:{registered:{clip:function(e,t,r){switch(e){case"name":return"clip";case"extract":var n;return n=v.RegEx.wrappedValueAlreadyExtracted.test(r)?r:(n=r.toString().match(v.RegEx.valueUnwrap))?n[1].replace(/,(\s+)?/g," "):r;case"inject":return"rect("+r+")"}},blur:function(e,t,r){switch(e){case"name":return m.State.isFirefox?"filter":"-webkit-filter";case"extract":var n=parseFloat(r);if(!n&&0!==n){var o=r.toString().match(/blur\(([0-9]+[A-z]+)\)/i);n=o?o[1]:0}return n;case"inject":return parseFloat(r)?"blur("+r+")":"none"}},opacity:function(e,t,r){if(a<=8)switch(e){case"name":return"filter";case"extract":var n=r.toString().match(/alpha\(opacity=(.*)\)/i);return r=n?n[1]/100:1;case"inject":return t.style.zoom=1,parseFloat(r)>=1?"":"alpha(opacity="+parseInt(100*parseFloat(r),10)+")"}else switch(e){case"name":return"opacity";case"extract":case"inject":return r}}},register:function(){a&&!(a>9)||m.State.isGingerbread||(v.Lists.transformsBase=v.Lists.transformsBase.concat(v.Lists.transforms3D));for(var e=0;e<v.Lists.transformsBase.length;e++)!function(){var t=v.Lists.transformsBase[e];v.Normalizations.registered[t]=function(e,r,o){switch(e){case"name":return"transform";case"extract":return S(r)===n||S(r).transformCache[t]===n?/^scale/i.test(t)?1:0:S(r).transformCache[t].replace(/[()]/g,"");case"inject":var a=!1;switch(t.substr(0,t.length-1)){case"translate":a=!/(%|px|em|rem|vw|vh|\d)$/i.test(o);break;case"scal":case"scale":m.State.isAndroid&&S(r).transformCache[t]===n&&o<1&&(o=1),a=!/(\d)$/i.test(o);break;case"skew":case"rotate":a=!/(deg|\d)$/i.test(o)}return a||(S(r).transformCache[t]="("+o+")"),S(r).transformCache[t]}}}();for(var t=0;t<v.Lists.colors.length;t++)!function(){var e=v.Lists.colors[t];v.Normalizations.registered[e]=function(t,r,o){switch(t){case"name":return e;case"extract":var i;if(v.RegEx.wrappedValueAlreadyExtracted.test(o))i=o;else{var s,l={black:"rgb(0, 0, 0)",blue:"rgb(0, 0, 255)",gray:"rgb(128, 128, 128)",green:"rgb(0, 128, 0)",red:"rgb(255, 0, 0)",white:"rgb(255, 255, 255)"};/^[A-z]+$/i.test(o)?s=l[o]!==n?l[o]:l.black:v.RegEx.isHex.test(o)?s="rgb("+v.Values.hexToRgb(o).join(" ")+")":/^rgba?\(/i.test(o)||(s=l.black),i=(s||o).toString().match(v.RegEx.valueUnwrap)[1].replace(/,(\s+)?/g," ")}return(!a||a>8)&&3===i.split(" ").length&&(i+=" 1"),i;case"inject":return/^rgb/.test(o)?o:(a<=8?4===o.split(" ").length&&(o=o.split(/\s+/).slice(0,3).join(" ")):3===o.split(" ").length&&(o+=" 1"),(a<=8?"rgb":"rgba")+"("+o.replace(/\s+/g,",").replace(/\.(\d)+(?=,)/g,"")+")")}}}();function r(e,t,r){if("border-box"===v.getPropertyValue(t,"boxSizing").toString().toLowerCase()===(r||!1)){var n,o,a=0,i="width"===e?["Left","Right"]:["Top","Bottom"],s=["padding"+i[0],"padding"+i[1],"border"+i[0]+"Width","border"+i[1]+"Width"];for(n=0;n<s.length;n++)o=parseFloat(v.getPropertyValue(t,s[n])),isNaN(o)||(a+=o);return r?-a:a}return 0}function o(e,t){return function(n,o,a){switch(n){case"name":return e;case"extract":return parseFloat(a)+r(e,o,t);case"inject":return parseFloat(a)-r(e,o,t)+"px"}}}v.Normalizations.registered.innerWidth=o("width",!0),v.Normalizations.registered.innerHeight=o("height",!0),v.Normalizations.registered.outerWidth=o("width"),v.Normalizations.registered.outerHeight=o("height")}},Names:{camelCase:function(e){return e.replace(/-(\w)/g,(function(e,t){return t.toUpperCase()}))},SVGAttribute:function(e){var t="width|height|x|y|cx|cy|r|rx|ry|x1|x2|y1|y2";return(a||m.State.isAndroid&&!m.State.isChrome)&&(t+="|transform"),new RegExp("^("+t+")$","i").test(e)},prefixCheck:function(e){if(m.State.prefixMatches[e])return[m.State.prefixMatches[e],!0];for(var t=["","Webkit","Moz","ms","O"],r=0,n=t.length;r<n;r++){var o;if(o=0===r?e:t[r]+e.replace(/^\w/,(function(e){return e.toUpperCase()})),f.isString(m.State.prefixElement.style[o]))return m.State.prefixMatches[e]=o,[o,!0]}return[e,!1]}},Values:{hexToRgb:function(e){var t;return e=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(e,t,r,n){return t+t+r+r+n+n})),(t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e))?[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]:[0,0,0]},isCSSNullValue:function(e){return!e||/^(none|auto|transparent|(rgba\(0, ?0, ?0, ?0\)))$/i.test(e)},getUnitType:function(e){return/^(rotate|skew)/i.test(e)?"deg":/(^(scale|scaleX|scaleY|scaleZ|alpha|flexGrow|flexHeight|zIndex|fontWeight)$)|((opacity|red|green|blue|alpha)$)/i.test(e)?"":"px"},getDisplayType:function(e){var t=e&&e.tagName.toString().toLowerCase();return/^(b|big|i|small|tt|abbr|acronym|cite|code|dfn|em|kbd|strong|samp|var|a|bdo|br|img|map|object|q|script|span|sub|sup|button|input|label|select|textarea)$/i.test(t)?"inline":/^(li)$/i.test(t)?"list-item":/^(tr)$/i.test(t)?"table-row":/^(table)$/i.test(t)?"table":/^(tbody)$/i.test(t)?"table-row-group":"block"},addClass:function(e,t){if(e)if(e.classList)e.classList.add(t);else if(f.isString(e.className))e.className+=(e.className.length?" ":"")+t;else{var r=e.getAttribute(a<=7?"className":"class")||"";e.setAttribute("class",r+(r?" ":"")+t)}},removeClass:function(e,t){if(e)if(e.classList)e.classList.remove(t);else if(f.isString(e.className))e.className=e.className.toString().replace(new RegExp("(^|\\s)"+t.split(" ").join("|")+"(\\s|$)","gi")," ");else{var r=e.getAttribute(a<=7?"className":"class")||"";e.setAttribute("class",r.replace(new RegExp("(^|s)"+t.split(" ").join("|")+"(s|$)","gi")," "))}}},getPropertyValue:function(e,r,o,i){function s(e,r){var o=0;if(a<=8)o=d.css(e,r);else{var l=!1;/^(width|height)$/.test(r)&&0===v.getPropertyValue(e,"display")&&(l=!0,v.setPropertyValue(e,"display",v.Values.getDisplayType(e)));var c,u=function(){l&&v.setPropertyValue(e,"display","none")};if(!i){if("height"===r&&"border-box"!==v.getPropertyValue(e,"boxSizing").toString().toLowerCase()){var f=e.offsetHeight-(parseFloat(v.getPropertyValue(e,"borderTopWidth"))||0)-(parseFloat(v.getPropertyValue(e,"borderBottomWidth"))||0)-(parseFloat(v.getPropertyValue(e,"paddingTop"))||0)-(parseFloat(v.getPropertyValue(e,"paddingBottom"))||0);return u(),f}if("width"===r&&"border-box"!==v.getPropertyValue(e,"boxSizing").toString().toLowerCase()){var p=e.offsetWidth-(parseFloat(v.getPropertyValue(e,"borderLeftWidth"))||0)-(parseFloat(v.getPropertyValue(e,"borderRightWidth"))||0)-(parseFloat(v.getPropertyValue(e,"paddingLeft"))||0)-(parseFloat(v.getPropertyValue(e,"paddingRight"))||0);return u(),p}}c=S(e)===n?t.getComputedStyle(e,null):S(e).computedStyle?S(e).computedStyle:S(e).computedStyle=t.getComputedStyle(e,null),"borderColor"===r&&(r="borderTopColor"),""!==(o=9===a&&"filter"===r?c.getPropertyValue(r):c[r])&&null!==o||(o=e.style[r]),u()}if("auto"===o&&/^(top|right|bottom|left)$/i.test(r)){var h=s(e,"position");("fixed"===h||"absolute"===h&&/top|left/i.test(r))&&(o=d(e).position()[r]+"px")}return o}var l;if(v.Hooks.registered[r]){var c=r,u=v.Hooks.getRoot(c);o===n&&(o=v.getPropertyValue(e,v.Names.prefixCheck(u)[0])),v.Normalizations.registered[u]&&(o=v.Normalizations.registered[u]("extract",e,o)),l=v.Hooks.extractValue(c,o)}else if(v.Normalizations.registered[r]){var f,p;"transform"!==(f=v.Normalizations.registered[r]("name",e))&&(p=s(e,v.Names.prefixCheck(f)[0]),v.Values.isCSSNullValue(p)&&v.Hooks.templates[r]&&(p=v.Hooks.templates[r][1])),l=v.Normalizations.registered[r]("extract",e,p)}if(!/^[\d-]/.test(l)){var h=S(e);if(h&&h.isSVG&&v.Names.SVGAttribute(r))if(/^(height|width)$/i.test(r))try{l=e.getBBox()[r]}catch(e){l=0}else l=e.getAttribute(r);else l=s(e,v.Names.prefixCheck(r)[0])}return v.Values.isCSSNullValue(l)&&(l=0),m.debug>=2&&console.log("Get "+r+": "+l),l},setPropertyValue:function(e,r,n,o,i){var s=r;if("scroll"===r)i.container?i.container["scroll"+i.direction]=n:"Left"===i.direction?t.scrollTo(n,i.alternateValue):t.scrollTo(i.alternateValue,n);else if(v.Normalizations.registered[r]&&"transform"===v.Normalizations.registered[r]("name",e))v.Normalizations.registered[r]("inject",e,n),s="transform",n=S(e).transformCache[r];else{if(v.Hooks.registered[r]){var l=r,c=v.Hooks.getRoot(r);o=o||v.getPropertyValue(e,c),n=v.Hooks.injectValue(l,n,o),r=c}if(v.Normalizations.registered[r]&&(n=v.Normalizations.registered[r]("inject",e,n),r=v.Normalizations.registered[r]("name",e)),s=v.Names.prefixCheck(r)[0],a<=8)try{e.style[s]=n}catch(e){m.debug&&console.log("Browser does not support ["+n+"] for ["+s+"]")}else{var u=S(e);u&&u.isSVG&&v.Names.SVGAttribute(r)?e.setAttribute(r,n):e.style[s]=n}m.debug>=2&&console.log("Set "+r+" ("+s+"): "+n)}return[s,n]},flushTransformCache:function(e){var t="",r=S(e);if((a||m.State.isAndroid&&!m.State.isChrome)&&r&&r.isSVG){var n=function(t){return parseFloat(v.getPropertyValue(e,t))},o={translate:[n("translateX"),n("translateY")],skewX:[n("skewX")],skewY:[n("skewY")],scale:1!==n("scale")?[n("scale"),n("scale")]:[n("scaleX"),n("scaleY")],rotate:[n("rotateZ"),0,0]};d.each(S(e).transformCache,(function(e){/^translate/i.test(e)?e="translate":/^scale/i.test(e)?e="scale":/^rotate/i.test(e)&&(e="rotate"),o[e]&&(t+=e+"("+o[e].join(" ")+") ",delete o[e])}))}else{var i,s;d.each(S(e).transformCache,(function(r){if(i=S(e).transformCache[r],"transformPerspective"===r)return s=i,!0;9===a&&"rotateZ"===r&&(r="rotate"),t+=r+i+" "})),s&&(t="perspective"+s+" "+t)}v.setPropertyValue(e,"transform",t)}};v.Hooks.register(),v.Normalizations.register(),m.hook=function(e,t,r){var o;return e=u(e),d.each(e,(function(e,a){if(S(a)===n&&m.init(a),r===n)o===n&&(o=v.getPropertyValue(a,t));else{var i=v.setPropertyValue(a,t,r);"transform"===i[0]&&m.CSS.flushTransformCache(a),o=i}})),o};var b=function(){var e;function o(){return a?w.promise||null:i}var a,i,s,l,p,g,y=arguments[0]&&(arguments[0].p||d.isPlainObject(arguments[0].properties)&&!arguments[0].properties.names||f.isString(arguments[0].properties));f.isWrapped(this)?(a=!1,s=0,l=this,i=this):(a=!0,s=1,l=y?arguments[0].elements||arguments[0].e:arguments[0]);var w={promise:null,resolver:null,rejecter:null};if(a&&m.Promise&&(w.promise=new m.Promise((function(e,t){w.resolver=e,w.rejecter=t}))),y?(p=arguments[0].properties||arguments[0].p,g=arguments[0].options||arguments[0].o):(p=arguments[s],g=arguments[s+1]),l=u(l)){var x,T=l.length,V=0;if(!/^(stop|finish|finishAll|pause|resume)$/i.test(p)&&!d.isPlainObject(g)){var M=s+1;g={};for(var A=M;A<arguments.length;A++)f.isArray(arguments[A])||!/^(fast|normal|slow)$/i.test(arguments[A])&&!/^\d/.test(arguments[A])?f.isString(arguments[A])||f.isArray(arguments[A])?g.easing=arguments[A]:f.isFunction(arguments[A])&&(g.complete=arguments[A]):g.duration=arguments[A]}switch(p){case"scroll":x="scroll";break;case"reverse":x="reverse";break;case"pause":var j=(new Date).getTime();return d.each(l,(function(e,t){P(t,j)})),d.each(m.State.calls,(function(e,t){var r=!1;t&&d.each(t[1],(function(e,o){var a=g===n?"":g;return!0!==a&&t[2].queue!==a&&(g!==n||!1!==t[2].queue)||(d.each(l,(function(e,n){if(n===o)return t[5]={resume:!1},r=!0,!1})),!r&&void 0)}))})),o();case"resume":return d.each(l,(function(e,t){k(t)})),d.each(m.State.calls,(function(e,t){var r=!1;t&&d.each(t[1],(function(e,o){var a=g===n?"":g;return!0!==a&&t[2].queue!==a&&(g!==n||!1!==t[2].queue)||(!t[5]||(d.each(l,(function(e,n){if(n===o)return t[5].resume=!0,r=!0,!1})),!r&&void 0))}))})),o();case"finish":case"finishAll":case"stop":d.each(l,(function(e,t){S(t)&&S(t).delayTimer&&(clearTimeout(S(t).delayTimer.setTimeout),S(t).delayTimer.next&&S(t).delayTimer.next(),delete S(t).delayTimer),"finishAll"!==p||!0!==g&&!f.isString(g)||(d.each(d.queue(t,f.isString(g)?g:""),(function(e,t){f.isFunction(t)&&t()})),d.queue(t,f.isString(g)?g:"",[]))}));var L=[];return d.each(m.State.calls,(function(e,t){t&&d.each(t[1],(function(r,o){var a=g===n?"":g;if(!0!==a&&t[2].queue!==a&&(g!==n||!1!==t[2].queue))return!0;d.each(l,(function(r,n){if(n===o)if((!0===g||f.isString(g))&&(d.each(d.queue(n,f.isString(g)?g:""),(function(e,t){f.isFunction(t)&&t(null,!0)})),d.queue(n,f.isString(g)?g:"",[])),"stop"===p){var i=S(n);i&&i.tweensContainer&&(!0===a||""===a)&&d.each(i.tweensContainer,(function(e,t){t.endValue=t.currentValue})),L.push(e)}else"finish"!==p&&"finishAll"!==p||(t[2].duration=1)}))}))})),"stop"===p&&(d.each(L,(function(e,t){O(t,!0)})),w.promise&&w.resolver(l)),o();default:if(!d.isPlainObject(p)||f.isEmptyObject(p)){if(f.isString(p)&&m.Redirects[p]){var H=(e=d.extend({},g)).duration,F=e.delay||0;return!0===e.backwards&&(l=d.extend(!0,[],l).reverse()),d.each(l,(function(t,r){parseFloat(e.stagger)?e.delay=F+parseFloat(e.stagger)*t:f.isFunction(e.stagger)&&(e.delay=F+e.stagger.call(r,t,T)),e.drag&&(e.duration=parseFloat(H)||(/^(callout|transition)/.test(p)?1e3:h),e.duration=Math.max(e.duration*(e.backwards?1-t/T:(t+1)/T),.75*e.duration,200)),m.Redirects[p].call(r,r,e||{},t,T,l,w.promise?w:n)})),o()}var R="Velocity: First argument ("+p+") was not a property map, a known action, or a registered redirect. Aborting.";return w.promise?w.rejecter(new Error(R)):t.console&&console.log(R),o()}x="start"}var N={lastParent:null,lastPosition:null,lastFontSize:null,lastPercentToPxWidth:null,lastPercentToPxHeight:null,lastEmToPx:null,remToPx:null,vwToPx:null,vhToPx:null},I=[];d.each(l,(function(e,t){f.isNode(t)&&z(t,e)})),(e=d.extend({},m.defaults,g)).loop=parseInt(e.loop,10);var B=2*e.loop-1;if(e.loop)for(var q=0;q<B;q++){var D={delay:e.delay,progress:e.progress};q===B-1&&(D.display=e.display,D.visibility=e.visibility,D.complete=e.complete),b(l,"reverse",D)}return o()}function z(e,o){var a,i,s=d.extend({},m.defaults,g),u={};switch(S(e)===n&&m.init(e),parseFloat(s.delay)&&!1!==s.queue&&d.queue(e,s.queue,(function(t,r){if(!0===r)return!0;m.velocityQueueEntryFlag=!0;var n=m.State.delayedElements.count++;m.State.delayedElements[n]=e;var o,a=(o=n,function(){m.State.delayedElements[o]=!1,t()});S(e).delayBegin=(new Date).getTime(),S(e).delay=parseFloat(s.delay),S(e).delayTimer={setTimeout:setTimeout(t,parseFloat(s.delay)),next:a}})),s.duration.toString().toLowerCase()){case"fast":s.duration=200;break;case"normal":s.duration=h;break;case"slow":s.duration=600;break;default:s.duration=parseFloat(s.duration)||1}function y(i){var h,y;if(s.begin&&0===V)try{s.begin.call(l,l)}catch(e){setTimeout((function(){throw e}),1)}if("scroll"===x){var b,P,k,O=/^x$/i.test(s.axis)?"Left":"Top",M=parseFloat(s.offset)||0;s.container?f.isWrapped(s.container)||f.isNode(s.container)?(s.container=s.container[0]||s.container,k=(b=s.container["scroll"+O])+d(e).position()[O.toLowerCase()]+M):s.container=null:(b=m.State.scrollAnchor[m.State["scrollProperty"+O]],P=m.State.scrollAnchor[m.State["scrollProperty"+("Left"===O?"Top":"Left")]],k=d(e).offset()[O.toLowerCase()]+M),u={scroll:{rootPropertyValue:!1,startValue:b,currentValue:b,endValue:k,unitType:"",easing:s.easing,scrollData:{container:s.container,direction:O,alternateValue:P}},element:e},m.debug&&console.log("tweensContainer (scroll): ",u.scroll,e)}else if("reverse"===x){if(!(h=S(e)))return;if(!h.tweensContainer)return void d.dequeue(e,s.queue);for(var A in"none"===h.opts.display&&(h.opts.display="auto"),"hidden"===h.opts.visibility&&(h.opts.visibility="visible"),h.opts.loop=!1,h.opts.begin=null,h.opts.complete=null,g.easing||delete s.easing,g.duration||delete s.duration,s=d.extend({},h.opts,s),y=d.extend(!0,{},h?h.tweensContainer:null))if(y.hasOwnProperty(A)&&"element"!==A){var j=y[A].startValue;y[A].startValue=y[A].currentValue=y[A].endValue,y[A].endValue=j,f.isEmptyObject(g)||(y[A].easing=s.easing),m.debug&&console.log("reverse tweensContainer ("+A+"): "+JSON.stringify(y[A]),e)}u=y}else if("start"===x){(h=S(e))&&h.tweensContainer&&!0===h.isAnimating&&(y=h.tweensContainer);var L=function(t,r){var n,a,i;return f.isFunction(t)&&(t=t.call(e,o,T)),f.isArray(t)?(n=t[0],!f.isArray(t[1])&&/^[\d-]/.test(t[1])||f.isFunction(t[1])||v.RegEx.isHex.test(t[1])?i=t[1]:f.isString(t[1])&&!v.RegEx.isHex.test(t[1])&&m.Easings[t[1]]||f.isArray(t[1])?(a=r?t[1]:C(t[1],s.duration),i=t[2]):i=t[1]||t[2]):n=t,r||(a=a||s.easing),f.isFunction(n)&&(n=n.call(e,o,T)),f.isFunction(i)&&(i=i.call(e,o,T)),[n||0,a,i]},H=function(o,i){var l,c=v.Hooks.getRoot(o),p=!1,g=i[0],b=i[1],w=i[2];if(h&&h.isSVG||"tween"===c||!1!==v.Names.prefixCheck(c)[1]||v.Normalizations.registered[c]!==n){(s.display!==n&&null!==s.display&&"none"!==s.display||s.visibility!==n&&"hidden"!==s.visibility)&&/opacity|filter/.test(o)&&!w&&0!==g&&(w=0),s._cacheValues&&y&&y[o]?(w===n&&(w=y[o].endValue+y[o].unitType),p=h.rootPropertyValueCache[c]):v.Hooks.registered[o]?w===n?(p=v.getPropertyValue(e,c),w=v.getPropertyValue(e,o,p)):p=v.Hooks.templates[c][1]:w===n&&(w=v.getPropertyValue(e,o));var x,S,P,k=!1,T=function(e,t){var r,n;return n=(t||"0").toString().toLowerCase().replace(/[%A-z]+$/,(function(e){return r=e,""})),r||(r=v.Values.getUnitType(e)),[n,r]};if(w!==g&&f.isString(w)&&f.isString(g)){l="";var V=0,C=0,E=[],O=[],M=0,A=0,j=0;for(w=v.Hooks.fixColors(w),g=v.Hooks.fixColors(g);V<w.length&&C<g.length;){var L=w[V],H=g[C];if(/[\d\.-]/.test(L)&&/[\d\.-]/.test(H)){for(var F=L,R=H,I=".",B=".";++V<w.length;){if((L=w[V])===I)I="..";else if(!/\d/.test(L))break;F+=L}for(;++C<g.length;){if((H=g[C])===B)B="..";else if(!/\d/.test(H))break;R+=H}var q=v.Hooks.getUnit(w,V),D=v.Hooks.getUnit(g,C);if(V+=q.length,C+=D.length,q===D)F===R?l+=F+q:(l+="{"+E.length+(A?"!":"")+"}"+q,E.push(parseFloat(F)),O.push(parseFloat(R)));else{var z=parseFloat(F),_=parseFloat(R);l+=(M<5?"calc":"")+"("+(z?"{"+E.length+(A?"!":"")+"}":"0")+q+" + "+(_?"{"+(E.length+(z?1:0))+(A?"!":"")+"}":"0")+D+")",z&&(E.push(z),O.push(0)),_&&(E.push(0),O.push(_))}}else{if(L!==H){M=0;break}l+=L,V++,C++,0===M&&"c"===L||1===M&&"a"===L||2===M&&"l"===L||3===M&&"c"===L||M>=4&&"("===L?M++:(M&&M<5||M>=4&&")"===L&&--M<5)&&(M=0),0===A&&"r"===L||1===A&&"g"===L||2===A&&"b"===L||3===A&&"a"===L||A>=3&&"("===L?(3===A&&"a"===L&&(j=1),A++):j&&","===L?++j>3&&(A=j=0):(j&&A<(j?5:4)||A>=(j?4:3)&&")"===L&&--A<(j?5:4))&&(A=j=0)}}V===w.length&&C===g.length||(m.debug&&console.error('Trying to pattern match mis-matched strings ["'+g+'", "'+w+'"]'),l=n),l&&(E.length?(m.debug&&console.log('Pattern found "'+l+'" -> ',E,O,"["+w+","+g+"]"),w=E,g=O,S=P=""):l=n)}l||(w=(x=T(o,w))[0],P=x[1],g=(x=T(o,g))[0].replace(/^([+-\/*])=/,(function(e,t){return k=t,""})),S=x[1],w=parseFloat(w)||0,g=parseFloat(g)||0,"%"===S&&(/^(fontSize|lineHeight)$/.test(o)?(g/=100,S="em"):/^scale/.test(o)?(g/=100,S=""):/(Red|Green|Blue)$/i.test(o)&&(g=g/100*255,S="")));if(/[\/*]/.test(k))S=P;else if(P!==S&&0!==w)if(0===g)S=P;else{a=a||function(){var n={myParent:e.parentNode||r.body,position:v.getPropertyValue(e,"position"),fontSize:v.getPropertyValue(e,"fontSize")},o=n.position===N.lastPosition&&n.myParent===N.lastParent,a=n.fontSize===N.lastFontSize;N.lastParent=n.myParent,N.lastPosition=n.position,N.lastFontSize=n.fontSize;var i={};if(a&&o)i.emToPx=N.lastEmToPx,i.percentToPxWidth=N.lastPercentToPxWidth,i.percentToPxHeight=N.lastPercentToPxHeight;else{var s=h&&h.isSVG?r.createElementNS("http://www.w3.org/2000/svg","rect"):r.createElement("div");m.init(s),n.myParent.appendChild(s),d.each(["overflow","overflowX","overflowY"],(function(e,t){m.CSS.setPropertyValue(s,t,"hidden")})),m.CSS.setPropertyValue(s,"position",n.position),m.CSS.setPropertyValue(s,"fontSize",n.fontSize),m.CSS.setPropertyValue(s,"boxSizing","content-box"),d.each(["minWidth","maxWidth","width","minHeight","maxHeight","height"],(function(e,t){m.CSS.setPropertyValue(s,t,"100%")})),m.CSS.setPropertyValue(s,"paddingLeft","100em"),i.percentToPxWidth=N.lastPercentToPxWidth=(parseFloat(v.getPropertyValue(s,"width",null,!0))||1)/100,i.percentToPxHeight=N.lastPercentToPxHeight=(parseFloat(v.getPropertyValue(s,"height",null,!0))||1)/100,i.emToPx=N.lastEmToPx=(parseFloat(v.getPropertyValue(s,"paddingLeft"))||1)/100,n.myParent.removeChild(s)}return null===N.remToPx&&(N.remToPx=parseFloat(v.getPropertyValue(r.body,"fontSize"))||16),null===N.vwToPx&&(N.vwToPx=parseFloat(t.innerWidth)/100,N.vhToPx=parseFloat(t.innerHeight)/100),i.remToPx=N.remToPx,i.vwToPx=N.vwToPx,i.vhToPx=N.vhToPx,m.debug>=1&&console.log("Unit ratios: "+JSON.stringify(i),e),i}();var G=/margin|padding|left|right|width|text|word|letter/i.test(o)||/X$/.test(o)||"x"===o?"x":"y";switch(P){case"%":w*="x"===G?a.percentToPxWidth:a.percentToPxHeight;break;case"px":break;default:w*=a[P+"ToPx"]}switch(S){case"%":w*=1/("x"===G?a.percentToPxWidth:a.percentToPxHeight);break;case"px":break;default:w*=1/a[S+"ToPx"]}}switch(k){case"+":g=w+g;break;case"-":g=w-g;break;case"*":g*=w;break;case"/":g=w/g}u[o]={rootPropertyValue:p,startValue:w,currentValue:w,endValue:g,unitType:S,easing:b},l&&(u[o].pattern=l),m.debug&&console.log("tweensContainer ("+o+"): "+JSON.stringify(u[o]),e)}else m.debug&&console.log("Skipping ["+c+"] due to a lack of browser support.")};for(var F in p)if(p.hasOwnProperty(F)){var R=v.Names.camelCase(F),B=L(p[F]);if(c(v.Lists.colors,R)){var q=B[0],D=B[1],z=B[2];if(v.RegEx.isHex.test(q)){for(var _=["Red","Green","Blue"],G=v.Values.hexToRgb(q),Y=z?v.Values.hexToRgb(z):n,X=0;X<_.length;X++){var W=[G[X]];D&&W.push(D),Y!==n&&W.push(Y[X]),H(R+_[X],W)}continue}}H(R,B)}u.element=e}u.element&&(v.Values.addClass(e,"velocity-animating"),I.push(u),(h=S(e))&&(""===s.queue&&(h.tweensContainer=u,h.opts=s),h.isAnimating=!0),V===T-1?(m.State.calls.push([I,l,s,null,w.resolver,null,0]),!1===m.State.isTicking&&(m.State.isTicking=!0,E())):V++)}if(!1!==m.mock&&(!0===m.mock?s.duration=s.delay=1:(s.duration*=parseFloat(m.mock)||1,s.delay*=parseFloat(m.mock)||1)),s.easing=C(s.easing,s.duration),s.begin&&!f.isFunction(s.begin)&&(s.begin=null),s.progress&&!f.isFunction(s.progress)&&(s.progress=null),s.complete&&!f.isFunction(s.complete)&&(s.complete=null),s.display!==n&&null!==s.display&&(s.display=s.display.toString().toLowerCase(),"auto"===s.display&&(s.display=m.CSS.Values.getDisplayType(e))),s.visibility!==n&&null!==s.visibility&&(s.visibility=s.visibility.toString().toLowerCase()),s.mobileHA=s.mobileHA&&m.State.isMobile&&!m.State.isGingerbread,!1===s.queue)if(s.delay){var b=m.State.delayedElements.count++;m.State.delayedElements[b]=e;var P=(i=b,function(){m.State.delayedElements[i]=!1,y()});S(e).delayBegin=(new Date).getTime(),S(e).delay=parseFloat(s.delay),S(e).delayTimer={setTimeout:setTimeout(y,parseFloat(s.delay)),next:P}}else y();else d.queue(e,s.queue,(function(e,t){if(!0===t)return w.promise&&w.resolver(l),!0;m.velocityQueueEntryFlag=!0,y()}));""!==s.queue&&"fx"!==s.queue||"inprogress"===d.queue(e)[0]||d.dequeue(e)}w.promise&&(p&&g&&!1===g.promiseRejectEmpty?w.resolver():w.rejecter())};(m=d.extend(b,m)).animate=b;var w=t.requestAnimationFrame||i;if(!m.State.isMobile&&r.hidden!==n){var x=function(){r.hidden?(w=function(e){return setTimeout((function(){e(!0)}),16)},E()):w=t.requestAnimationFrame||i};x(),r.addEventListener("visibilitychange",x)}return e.Velocity=m,e!==t&&(e.fn.velocity=b,e.fn.velocity.defaults=m.defaults),d.each(["Down","Up"],(function(e,t){m.Redirects["slide"+t]=function(e,r,o,a,i,s){var l=d.extend({},r),c=l.begin,u=l.complete,f={},p={height:"",marginTop:"",marginBottom:"",paddingTop:"",paddingBottom:""};l.display===n&&(l.display="Down"===t?"inline"===m.CSS.Values.getDisplayType(e)?"inline-block":"block":"none"),l.begin=function(){for(var r in 0===o&&c&&c.call(i,i),p)if(p.hasOwnProperty(r)){f[r]=e.style[r];var n=v.getPropertyValue(e,r);p[r]="Down"===t?[n,0]:[0,n]}f.overflow=e.style.overflow,e.style.overflow="hidden"},l.complete=function(){for(var t in f)f.hasOwnProperty(t)&&(e.style[t]=f[t]);o===a-1&&(u&&u.call(i,i),s&&s.resolver(i))},m(e,p,l)}})),d.each(["In","Out"],(function(e,t){m.Redirects["fade"+t]=function(e,r,o,a,i,s){var l=d.extend({},r),c=l.complete,u={opacity:"In"===t?1:0};0!==o&&(l.begin=null),l.complete=o!==a-1?null:function(){c&&c.call(i,i),s&&s.resolver(i)},l.display===n&&(l.display="In"===t?"auto":"none"),m(this,u,l)}})),m}function S(e){var t=d.data(e,"velocity");return null===t?n:t}function P(e,t){var r=S(e);r&&r.delayTimer&&!r.delayPaused&&(r.delayRemaining=r.delay-t+r.delayBegin,r.delayPaused=!0,clearTimeout(r.delayTimer.setTimeout))}function k(e,t){var r=S(e);r&&r.delayTimer&&r.delayPaused&&(r.delayPaused=!1,r.delayTimer.setTimeout=setTimeout(r.delayTimer.next,r.delayRemaining))}function T(e){return function(t){return Math.round(t*e)*(1/e)}}function V(e,r,n,o){var a=4,i=.001,s=1e-7,l=10,c=11,u=1/(c-1),d="Float32Array"in t;if(4!==arguments.length)return!1;for(var f=0;f<4;++f)if("number"!=typeof arguments[f]||isNaN(arguments[f])||!isFinite(arguments[f]))return!1;e=Math.min(e,1),n=Math.min(n,1),e=Math.max(e,0),n=Math.max(n,0);var p=d?new Float32Array(c):new Array(c);function h(e,t){return 1-3*t+3*e}function g(e,t){return 3*t-6*e}function m(e){return 3*e}function y(e,t,r){return((h(t,r)*e+g(t,r))*e+m(t))*e}function v(e,t,r){return 3*h(t,r)*e*e+2*g(t,r)*e+m(t)}function b(t){for(var r=0,o=1,d=c-1;o!==d&&p[o]<=t;++o)r+=u;--o;var f=r+(t-p[o])/(p[o+1]-p[o])*u,h=v(f,e,n);return h>=i?function(t,r){for(var o=0;o<a;++o){var i=v(r,e,n);if(0===i)return r;r-=(y(r,e,n)-t)/i}return r}(t,f):0===h?f:function(t,r,o){var a,i,c=0;do{(a=y(i=r+(o-r)/2,e,n)-t)>0?o=i:r=i}while(Math.abs(a)>s&&++c<l);return i}(t,r,r+u)}var w=!1;function x(){w=!0,e===r&&n===o||function(){for(var t=0;t<c;++t)p[t]=y(t*u,e,n)}()}var S=function(t){return w||x(),e===r&&n===o?t:0===t?0:1===t?1:y(b(t),r,o)};S.getControlPoints=function(){return[{x:e,y:r},{x:n,y:o}]};var P="generateBezier("+[e,r,n,o]+")";return S.toString=function(){return P},S}function C(e,t){var r=e;return f.isString(e)?m.Easings[e]||(r=!1):r=f.isArray(e)&&1===e.length?T.apply(null,e):f.isArray(e)&&2===e.length?y.apply(null,e.concat([t])):!(!f.isArray(e)||4!==e.length)&&V.apply(null,e),!1===r&&(r=m.Easings[m.defaults.easing]?m.defaults.easing:g),r}function E(e){if(e){var t=m.timestamp&&!0!==e?e:s.now(),r=m.State.calls.length;r>1e4&&(m.State.calls=function(e){for(var t=-1,r=e?e.length:0,n=[];++t<r;){var o=e[t];o&&n.push(o)}return n}(m.State.calls),r=m.State.calls.length);for(var o=0;o<r;o++)if(m.State.calls[o]){var i=m.State.calls[o],l=i[0],c=i[2],u=i[3],p=!u,h=null,g=i[5],y=i[6];if(u||(u=m.State.calls[o][3]=t-16),g){if(!0!==g.resume)continue;u=i[3]=Math.round(t-y-16),i[5]=null}y=i[6]=t-u;for(var b=Math.min(y/c.duration,1),x=0,P=l.length;x<P;x++){var k=l[x],T=k.element;if(S(T)){var V=!1;if(c.display!==n&&null!==c.display&&"none"!==c.display){if("flex"===c.display){d.each(["-webkit-box","-moz-box","-ms-flexbox","-webkit-flex"],(function(e,t){v.setPropertyValue(T,"display",t)}))}v.setPropertyValue(T,"display",c.display)}for(var C in c.visibility!==n&&"hidden"!==c.visibility&&v.setPropertyValue(T,"visibility",c.visibility),k)if(k.hasOwnProperty(C)&&"element"!==C){var M,A=k[C],j=f.isString(A.easing)?m.Easings[A.easing]:A.easing;if(f.isString(A.pattern)){var L=1===b?function(e,t,r){var n=A.endValue[t];return r?Math.round(n):n}:function(e,t,r){var n=A.startValue[t],o=A.endValue[t]-n,a=n+o*j(b,c,o);return r?Math.round(a):a};M=A.pattern.replace(/{(\d+)(!)?}/g,L)}else if(1===b)M=A.endValue;else{var H=A.endValue-A.startValue;M=A.startValue+H*j(b,c,H)}if(!p&&M===A.currentValue)continue;if(A.currentValue=M,"tween"===C)h=M;else{var F;if(v.Hooks.registered[C]){F=v.Hooks.getRoot(C);var R=S(T).rootPropertyValueCache[F];R&&(A.rootPropertyValue=R)}var N=v.setPropertyValue(T,C,A.currentValue+(a<9&&0===parseFloat(M)?"":A.unitType),A.rootPropertyValue,A.scrollData);v.Hooks.registered[C]&&(v.Normalizations.registered[F]?S(T).rootPropertyValueCache[F]=v.Normalizations.registered[F]("extract",null,N[1]):S(T).rootPropertyValueCache[F]=N[1]),"transform"===N[0]&&(V=!0)}}c.mobileHA&&S(T).transformCache.translate3d===n&&(S(T).transformCache.translate3d="(0px, 0px, 0px)",V=!0),V&&v.flushTransformCache(T)}}c.display!==n&&"none"!==c.display&&(m.State.calls[o][2].display=!1),c.visibility!==n&&"hidden"!==c.visibility&&(m.State.calls[o][2].visibility=!1),c.progress&&c.progress.call(i[1],i[1],b,Math.max(0,u+c.duration-t),u,h),1===b&&O(o)}}m.State.isTicking&&w(E)}function O(e,t){if(!m.State.calls[e])return!1;for(var r=m.State.calls[e][0],o=m.State.calls[e][1],a=m.State.calls[e][2],i=m.State.calls[e][4],s=!1,l=0,c=r.length;l<c;l++){var u=r[l].element;t||a.loop||("none"===a.display&&v.setPropertyValue(u,"display",a.display),"hidden"===a.visibility&&v.setPropertyValue(u,"visibility",a.visibility));var f=S(u);if(!0!==a.loop&&(d.queue(u)[1]===n||!/\.velocityQueueEntryFlag/i.test(d.queue(u)[1]))&&f){f.isAnimating=!1,f.rootPropertyValueCache={};var p=!1;d.each(v.Lists.transforms3D,(function(e,t){var r=/^scale/.test(t)?1:0,o=f.transformCache[t];f.transformCache[t]!==n&&new RegExp("^\\("+r+"[^.]").test(o)&&(p=!0,delete f.transformCache[t])})),a.mobileHA&&(p=!0,delete f.transformCache.translate3d),p&&v.flushTransformCache(u),v.Values.removeClass(u,"velocity-animating")}if(!t&&a.complete&&!a.loop&&l===c-1)try{a.complete.call(o,o)}catch(e){setTimeout((function(){throw e}),1)}i&&!0!==a.loop&&i(o),f&&!0===a.loop&&!t&&(d.each(f.tweensContainer,(function(e,t){if(/^rotate/.test(e)&&(parseFloat(t.startValue)-parseFloat(t.endValue))%360==0){var r=t.startValue;t.startValue=t.endValue,t.endValue=r}/^backgroundPosition/.test(e)&&100===parseFloat(t.endValue)&&"%"===t.unitType&&(t.endValue=0,t.startValue=100)})),m(u,"reverse",{loop:!0,delay:a.delay})),!1!==a.queue&&d.dequeue(u,a.queue)}m.State.calls[e]=!1;for(var h=0,g=m.State.calls.length;h<g;h++)if(!1!==m.State.calls[h]){s=!0;break}!1===s&&(m.State.isTicking=!1,delete m.State.calls,m.State.calls=[])}jQuery.fn.velocity=jQuery.fn.animate}(window.jQuery||window.Zepto||window,window,window?window.document:void 0)}))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(4),a=function(e){var t=e>>16,r=e>>8&255,n=255&e;return[o.sRGBToLinear(t),o.sRGBToLinear(r),o.sRGBToLinear(n)]},i=function(e,t){var r=Math.floor(e/361),n=Math.floor(e/19)%19,a=e%19;return[o.signPow((r-9)/9,2)*t,o.signPow((n-9)/9,2)*t,o.signPow((a-9)/9,2)*t]};t.default=function(e,t,r,s){if(s|=1,e.length<6)return console.error("too short blurhash"),null;var l=n.decode83(e[0]),c=Math.floor(l/9)+1,u=l%9+1,d=(n.decode83(e[1])+1)/166;if(e.length!==4+2*u*c)return console.error("blurhash length mismatch",e.length,4+2*u*c),null;for(var f=new Array(u*c),p=0;p<f.length;p++)if(0===p){var h=n.decode83(e.substring(2,6));f[p]=a(h)}else{h=n.decode83(e.substring(4+2*p,6+2*p));f[p]=i(h,d*s)}for(var g=4*t,m=new Uint8ClampedArray(g*r),y=0;y<r;y++)for(var v=0;v<t;v++){for(var b=0,w=0,x=0,S=0;S<c;S++)for(p=0;p<u;p++){var P=Math.cos(Math.PI*v*p/t)*Math.cos(Math.PI*y*S/r),k=f[p+S*u];b+=k[0]*P,w+=k[1]*P,x+=k[2]*P}var T=o.linearTosRGB(b),V=o.linearTosRGB(w),C=o.linearTosRGB(x);m[4*v+0+y*g]=T,m[4*v+1+y*g]=V,m[4*v+2+y*g]=C,m[4*v+3+y*g]=255}return m}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(1);t.decode=n.default;var o=r(7);t.encode=o.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","#","$","%","*","+",",","-",".",":",";","=","?","@","[","]","^","_","{","|","}","~"];t.decode83=function(e){for(var t=0,r=0;r<e.length;r++){var o=e[r];t=83*t+n.indexOf(o)}return t},t.encode83=function(e,t){for(var r="",o=1;o<=t;o++){var a=Math.floor(e)/Math.pow(83,t-o)%83;r+=n[Math.floor(a)]}return r}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sRGBToLinear=function(e){var t=e/255;return t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)},t.linearTosRGB=function(e){var t=Math.max(0,Math.min(1,e));return t<=.0031308?Math.round(12.92*t*255+.5):Math.round(255*(1.055*Math.pow(t,1/2.4)-.055)+.5)},t.sign=function(e){return e<0?-1:1},t.signPow=function(e,r){return t.sign(e)*Math.pow(Math.abs(e),r)}},function(e,t,r){!function(){"use strict";e.exports={polyfill:function(){var e=window,t=document;if(!("scrollBehavior"in t.documentElement.style&&!0!==e.__forceSmoothScrollPolyfill__)){var r,n=e.HTMLElement||e.Element,o=468,a={scroll:e.scroll||e.scrollTo,scrollBy:e.scrollBy,elementScroll:n.prototype.scroll||l,scrollIntoView:n.prototype.scrollIntoView},i=e.performance&&e.performance.now?e.performance.now.bind(e.performance):Date.now,s=(r=e.navigator.userAgent,new RegExp(["MSIE ","Trident/","Edge/"].join("|")).test(r)?1:0);e.scroll=e.scrollTo=function(){void 0!==arguments[0]&&(!0!==c(arguments[0])?g.call(e,t.body,void 0!==arguments[0].left?~~arguments[0].left:e.scrollX||e.pageXOffset,void 0!==arguments[0].top?~~arguments[0].top:e.scrollY||e.pageYOffset):a.scroll.call(e,void 0!==arguments[0].left?arguments[0].left:"object"!=typeof arguments[0]?arguments[0]:e.scrollX||e.pageXOffset,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:e.scrollY||e.pageYOffset))},e.scrollBy=function(){void 0!==arguments[0]&&(c(arguments[0])?a.scrollBy.call(e,void 0!==arguments[0].left?arguments[0].left:"object"!=typeof arguments[0]?arguments[0]:0,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:0):g.call(e,t.body,~~arguments[0].left+(e.scrollX||e.pageXOffset),~~arguments[0].top+(e.scrollY||e.pageYOffset)))},n.prototype.scroll=n.prototype.scrollTo=function(){if(void 0!==arguments[0])if(!0!==c(arguments[0])){var e=arguments[0].left,t=arguments[0].top;g.call(this,this,void 0===e?this.scrollLeft:~~e,void 0===t?this.scrollTop:~~t)}else{if("number"==typeof arguments[0]&&void 0===arguments[1])throw new SyntaxError("Value could not be converted");a.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left:"object"!=typeof arguments[0]?~~arguments[0]:this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top:void 0!==arguments[1]?~~arguments[1]:this.scrollTop)}},n.prototype.scrollBy=function(){void 0!==arguments[0]&&(!0!==c(arguments[0])?this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior}):a.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop))},n.prototype.scrollIntoView=function(){if(!0!==c(arguments[0])){var r=p(this),n=r.getBoundingClientRect(),o=this.getBoundingClientRect();r!==t.body?(g.call(this,r,r.scrollLeft+o.left-n.left,r.scrollTop+o.top-n.top),"fixed"!==e.getComputedStyle(r).position&&e.scrollBy({left:n.left,top:n.top,behavior:"smooth"})):e.scrollBy({left:o.left,top:o.top,behavior:"smooth"})}else a.scrollIntoView.call(this,void 0===arguments[0]||arguments[0])}}function l(e,t){this.scrollLeft=e,this.scrollTop=t}function c(e){if(null===e||"object"!=typeof e||void 0===e.behavior||"auto"===e.behavior||"instant"===e.behavior)return!0;if("object"==typeof e&&"smooth"===e.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+e.behavior+" is not a valid value for enumeration ScrollBehavior.")}function u(e,t){return"Y"===t?e.clientHeight+s<e.scrollHeight:"X"===t?e.clientWidth+s<e.scrollWidth:void 0}function d(t,r){var n=e.getComputedStyle(t,null)["overflow"+r];return"auto"===n||"scroll"===n}function f(e){var t=u(e,"Y")&&d(e,"Y"),r=u(e,"X")&&d(e,"X");return t||r}function p(e){for(;e!==t.body&&!1===f(e);)e=e.parentNode||e.host;return e}function h(t){var r,n,a,s,l=(i()-t.startTime)/o;s=l=l>1?1:l,r=.5*(1-Math.cos(Math.PI*s)),n=t.startX+(t.x-t.startX)*r,a=t.startY+(t.y-t.startY)*r,t.method.call(t.scrollable,n,a),n===t.x&&a===t.y||e.requestAnimationFrame(h.bind(e,t))}function g(r,n,o){var s,c,u,d,f=i();r===t.body?(s=e,c=e.scrollX||e.pageXOffset,u=e.scrollY||e.pageYOffset,d=a.scroll):(s=r,c=r.scrollLeft,u=r.scrollTop,d=l),h({scrollable:s,method:d,startTime:f,startX:c,startY:u,x:n,y:o})}}}}()},function(e,t,r){},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(4);t.default=function(e,t,r,a,i){if(a<1||a>9||i<1||i>9)throw new Error("BlurHash must have between 1 and 9 components");if(t*r*4!==e.length)throw new Error("Width and height must match the pixels array");for(var s=[],l=function(n){for(var i=function(a){var i=0==a&&0==n?1:2,l=function(e,t,r,n){for(var a=0,i=0,s=0,l=4*t,c=0;c<t;c++)for(var u=0;u<r;u++){var d=n(c,u);a+=d*o.sRGBToLinear(e[4*c+0+u*l]),i+=d*o.sRGBToLinear(e[4*c+1+u*l]),s+=d*o.sRGBToLinear(e[4*c+2+u*l])}var f=1/(t*r);return[a*f,i*f,s*f]}(e,t,r,(function(e,o){return i*Math.cos(Math.PI*a*e/t)*Math.cos(Math.PI*n*o/r)}));s.push(l)},l=0;l<a;l++)i(l)},c=0;c<i;c++)l(c);var u,d,f=s[0],p=s.slice(1),h="",g=a-1+9*(i-1);if(h+=n.encode83(g,1),p.length>0){var m=Math.max.apply(Math,p.map((function(e){return Math.max.apply(Math,e)}))),y=Math.floor(Math.max(0,Math.min(82,Math.floor(166*m-.5))));u=(y+1)/166,h+=n.encode83(y,1)}else u=1,h+=n.encode83(0,1);return h+=n.encode83((d=f,(o.linearTosRGB(d[0])<<16)+(o.linearTosRGB(d[1])<<8)+o.linearTosRGB(d[2])),4),p.forEach((function(e){h+=n.encode83(function(e,t){return 19*Math.floor(Math.max(0,Math.min(18,Math.floor(9*o.signPow(e[0]/t,.5)****))))*19+19*Math.floor(Math.max(0,Math.min(18,Math.floor(9*o.signPow(e[1]/t,.5)****))))+Math.floor(Math.max(0,Math.min(18,Math.floor(9*o.signPow(e[2]/t,.5)****))))}(e,u),2)})),h}},function(e,t,r){"use strict";r.r(t);r(6);var n=["LEHV6njZ2ykUpyoKadR*.8kCMdnj","LHF5]+c[^6#M@-5b,1J5@[or[kA{","L6Pj0^xZ.A.S_Nt7t7R+*0o}DgQ-","LKO2?U%2Tw=_]~VeVZRi};RPxuwH","LPPGdFog?wt7?HofM|R+OGRjr;xu"],o=r(1),a=r.n(o),i=r(0),s=r.n(i),l=r(5);r.n(l).a.polyfill();var c=function(){var e=document.getElementsByClassName("image-bg"),t=document.getElementsByClassName("imagesContainer"),r=document.getElementsByClassName("content");function o(e,t){if(t){var r=a()(t,32,32);if(r){var n=e.getContext("2d"),o=new ImageData(r,32,32);n.putImageData(o,0,0)}}}!function(){if("complete"===!document.readyState)return;s()({elements:t,properties:{translateY:"25%"},options:{duration:0}}),s()({elements:r,properties:{opacity:0},options:{duration:0}});var a=document.getElementsByClassName("image-canvas-bg");if(a&&a.length)for(var i=0;i<a.length;i++)o(a[i],n[i]);!function(){for(var n=0;n<e.length;n++)e[n].classList.add("animateImages");s()({elements:t,properties:{translateY:"0%"},options:{duration:750,delay:500,easing:"easeInOutCubic"}}),s()({elements:r,properties:{opacity:1},options:{duration:250,delay:1e3,complete:l}})}()}();var i=function(e){return"translate3d(0%, ".concat(e,"px, 0)")};function l(){window.addEventListener("scroll",c)}function c(e){var r=.5*window.pageYOffset;t[0].style.transform=i(r),t[0].style.webkitTransform=i(r)}document.getElementById("get-started").addEventListener("click",(function(){document.querySelector(".why-blurhash").scrollIntoView({behavior:"smooth"})}))},u=r(2),d=document.getElementById("demo-blurhash"),f=document.getElementById("demo-canvas"),p=document.getElementById("original-canvas"),h=document.getElementById("file-upload"),g=document.getElementById("component-x"),m=document.getElementById("component-y"),y=document.querySelector(".predefined");function v(){var e=d.textContent;if(e){var t=Object(u.decode)(e,32,32);if(t){d.classList.remove("error");var r=f.getContext("2d"),n=new ImageData(t,32,32);r.putImageData(n,0,0)}else d.classList.add("error")}}function b(e){return isNaN(e)?1:Math.min(9,Math.max(1,e))}function w(e){p.getContext("2d").drawImage(e,0,0,p.width,p.height),URL.revokeObjectURL(e.src),setTimeout(x,0)}function x(){var e=p.getContext("2d"),t=b(+g.value),r=b(+m.value),n=e.getImageData(0,0,p.width,p.height),o=Object(u.encode)(n.data,n.width,n.height,t,r);d.textContent=o,v()}function S(){console.log("renderSelectedImage");var e=document.querySelector(".predefined input:checked + img");p.classList.remove("visible"),h.value="",w(e)}d.addEventListener("change",v),d.addEventListener("keyup",v),h.addEventListener("change",(function(){if(h.files[0]){var e=new Image;p.classList.add("visible"),e.onload=function(){w(e)},e.src=URL.createObjectURL(h.files[0])}})),g.addEventListener("keyup",x),m.addEventListener("keyup",x),y.addEventListener("change",S),p.addEventListener("click",S);c(),S()}]);
//# sourceMappingURL=blurhash.01ae00eea611ada5e9c7.js.map