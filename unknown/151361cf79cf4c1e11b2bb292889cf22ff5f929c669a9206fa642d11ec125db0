#!/usr/bin/env python3
"""
Project Cache Clearing Script

This script clears ALL cached data from Redis for the Habib project to ensure 
fresh data is fetched on the next requests. It identifies and removes all cache 
keys related to the project including consultant data, statistics, and any other 
cached information.

⚠️  WARNING: This will delete ALL cached data for the project!

Usage:
    python clear_all_caches.py [--dry-run] [--verbose] [--confirm]

Options:
    --dry-run    Show what would be deleted without actually deleting
    --verbose    Show detailed information about each operation
    --confirm    Skip confirmation prompt (use with caution)
"""

import asyncio
import argparse
import sys
import os
from typing import List, Dict

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.redis_client import redis_client
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProjectCacheCleaner:
    """
    A utility class to clear all project-related caches from Redis.
    This includes all cached data used by the Habib application.
    """
    
    def __init__(self, dry_run: bool = False, verbose: bool = False):
        self.dry_run = dry_run
        self.verbose = verbose
        self.cache_patterns = [
            # Consultant-related caches
            "consultants:*",           # Consultant list caches
            "consultant:*",            # Individual consultant caches
            
            # Statistics and analytics caches
            "stats_info_consultant_*", # Consultant-specific stats caches
            "stats_info_all_*",        # General stats caches
            "period_stats_*",          # Period-based statistics caches
            
            # Catch-all pattern for any other project caches
            # This will clear ALL keys in the Redis database
            # Use with caution in shared Redis environments
            "*",                       # All remaining cache keys
        ]
    
    async def get_matching_keys(self, pattern: str) -> List[str]:
        """
        Get all keys matching a specific pattern.
        
        Args:
            pattern: Redis key pattern
            
        Returns:
            List of matching keys
        """
        try:
            keys = await redis_client.keys(pattern)
            if self.verbose:
                logger.info(f"Pattern '{pattern}' matched {len(keys)} keys")
                for key in keys[:10]:  # Show first 10 keys as examples
                    logger.info(f"  - {key}")
                if len(keys) > 10:
                    logger.info(f"  ... and {len(keys) - 10} more keys")
            return keys
        except Exception as e:
            logger.error(f"Error getting keys for pattern '{pattern}': {e}")
            return []
    
    async def analyze_cache_usage(self) -> Dict[str, List[str]]:
        """
        Analyze current cache usage by pattern.
        
        Returns:
            Dictionary mapping patterns to lists of matching keys
        """
        logger.info("Analyzing current cache usage...")
        cache_analysis = {}
        
        for pattern in self.cache_patterns:
            keys = await self.get_matching_keys(pattern)
            cache_analysis[pattern] = keys
            logger.info(f"Pattern '{pattern}': {len(keys)} keys")
        
        return cache_analysis
    
    async def clear_pattern(self, pattern: str) -> int:
        """
        Clear all keys matching a pattern.
        
        Args:
            pattern: Redis key pattern
            
        Returns:
            Number of keys deleted
        """
        if self.dry_run:
            keys = await self.get_matching_keys(pattern)
            logger.info(f"[DRY RUN] Would delete {len(keys)} keys matching '{pattern}'")
            return len(keys)
        else:
            deleted_count = await redis_client.delete_pattern(pattern)
            logger.info(f"Deleted {deleted_count} keys matching '{pattern}'")
            return deleted_count
    
    async def clear_all_project_caches(self) -> Dict[str, int]:
        """
        Clear all project-related caches.
        
        Returns:
            Dictionary mapping patterns to number of keys deleted
        """
        logger.info("Starting project cache clearing process...")
        
        if self.dry_run:
            logger.info("DRY RUN MODE: No actual deletions will be performed")
        
        # First analyze what we have
        cache_analysis = await self.analyze_cache_usage()
        
        # Calculate total keys to be affected
        total_keys = sum(len(keys) for keys in cache_analysis.values())
        logger.info(f"Total keys to be {'analyzed' if self.dry_run else 'deleted'}: {total_keys}")
        
        if total_keys == 0:
            logger.info("No project cache keys found. Nothing to clear.")
            return {}
        
        # Confirm before proceeding (unless dry run)
        if not self.dry_run:
            logger.warning(f"About to delete {total_keys} cache keys. This action cannot be undone.")
            logger.warning("This will clear ALL cached data for the project!")
            
        # Clear caches by pattern (excluding the catch-all "*" pattern for now)
        deletion_results = {}
        specific_patterns = [p for p in self.cache_patterns if p != "*"]
        
        # First clear specific patterns
        for pattern in specific_patterns:
            deleted_count = await self.clear_pattern(pattern)
            deletion_results[pattern] = deleted_count
        
        # Then clear any remaining keys with the catch-all pattern
        if "*" in self.cache_patterns:
            remaining_keys = await self.get_matching_keys("*")
            if remaining_keys:
                logger.info(f"Found {len(remaining_keys)} additional keys to clear with catch-all pattern")
                deleted_count = await self.clear_pattern("*")
                deletion_results["*"] = deleted_count
            else:
                deletion_results["*"] = 0
        
        return deletion_results
    
    async def verify_cache_clearing(self) -> bool:
        """
        Verify that all project caches have been cleared.
        
        Returns:
            True if all caches are cleared, False otherwise
        """
        logger.info("Verifying cache clearing...")
        
        remaining_keys = 0
        # Check specific patterns (excluding catch-all)
        specific_patterns = [p for p in self.cache_patterns if p != "*"]
        
        for pattern in specific_patterns:
            keys = await self.get_matching_keys(pattern)
            remaining_keys += len(keys)
            if keys and self.verbose:
                logger.warning(f"Pattern '{pattern}' still has {len(keys)} keys")
        
        # Check if any keys remain at all
        all_keys = await self.get_matching_keys("*")
        total_remaining = len(all_keys)
        
        if total_remaining == 0:
            logger.info("✓ All project caches have been successfully cleared")
            return True
        else:
            logger.warning(f"⚠ {total_remaining} cache keys still remain")
            if self.verbose and all_keys:
                logger.info("Remaining keys:")
                for key in all_keys[:10]:  # Show first 10 remaining keys
                    logger.info(f"  - {key}")
                if len(all_keys) > 10:
                    logger.info(f"  ... and {len(all_keys) - 10} more keys")
            return False


async def main():
    """Main function to run the cache clearing script."""
    parser = argparse.ArgumentParser(
        description="Clear all project-related caches from Redis"
    )
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="Show what would be deleted without actually deleting"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Show detailed information about each operation"
    )
    parser.add_argument(
        "--confirm", 
        action="store_true", 
        help="Skip confirmation prompt (use with caution)"
    )
    
    args = parser.parse_args()
    
    # Initialize the cache cleaner
    cleaner = ProjectCacheCleaner(dry_run=args.dry_run, verbose=args.verbose)
    
    try:
        # Test Redis connection
        logger.info("Testing Redis connection...")
        if not await redis_client.ping():
            logger.error("Failed to connect to Redis. Please check your Redis configuration.")
            return 1
        logger.info("✓ Redis connection successful")
        
        # Warning for production use
        if not args.dry_run and not args.confirm:
            logger.warning("⚠️  WARNING: This will delete ALL cached data for the project!")
            logger.warning("⚠️  This includes consultant data, statistics, and any other cached information.")
            logger.warning("⚠️  Use --dry-run first to see what would be deleted.")
            logger.warning("⚠️  Add --confirm flag to skip this warning.")
            
            response = input("\nAre you sure you want to proceed? (yes/no): ")
            if response.lower() not in ['yes', 'y']:
                logger.info("Operation cancelled by user.")
                return 0
        
        # Clear project caches
        results = await cleaner.clear_all_project_caches()
        
        # Show summary
        total_deleted = sum(results.values())
        logger.info(f"\n{'=== DRY RUN SUMMARY ===' if args.dry_run else '=== DELETION SUMMARY ==='}")
        for pattern, count in results.items():
            logger.info(f"  {pattern}: {count} keys")
        logger.info(f"  Total: {total_deleted} keys {'would be deleted' if args.dry_run else 'deleted'}")
        
        # Verify clearing (only if not dry run)
        if not args.dry_run and total_deleted > 0:
            await cleaner.verify_cache_clearing()
        
        logger.info("Cache clearing process completed successfully")
        return 0
        
    except Exception as e:
        logger.error(f"Error during cache clearing: {e}")
        return 1
    finally:
        # Close Redis connection
        await redis_client.close()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
