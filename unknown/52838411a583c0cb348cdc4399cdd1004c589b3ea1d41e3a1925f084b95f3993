"""support request and consultat experise_field

Revision ID: d0c58530b65c
Revises: 99c2b02a4e96
Create Date: 2024-04-20 13:43:56.512826

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd0c58530b65c'
down_revision: Union[str, None] = '99c2b02a4e96'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('support_request',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('subject', sa.String(), nullable=True),
    sa.Column('wa_number', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('at_time', sa.DateTime(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_support_request_id'), 'support_request', ['id'], unique=False)
    op.add_column('consultants', sa.Column('expertise_field', sa.String(), nullable=True))
    op.add_column('consultants', sa.Column('expertise', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('consultants', 'expertise')
    op.drop_column('consultants', 'expertise_field')
    op.drop_index(op.f('ix_support_request_id'), table_name='support_request')
    op.drop_table('support_request')
    # ### end Alembic commands ###
