"""Add relationships to Call model

Revision ID: 6b3e7d9900c2
Revises: 723178515398
Create Date: 2024-06-07 17:18:36.738592

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '6b3e7d9900c2'
down_revision: Union[str, None] = '723178515398'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('calls')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('calls',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('consultant_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('client_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('start_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('end_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('call_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('cost', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['client_id'], ['users.id'], name='calls_client_id_fkey'),
    sa.ForeignKeyConstraint(['consultant_id'], ['consultants.id'], name='calls_consultant_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='calls_pkey')
    )
    # ### end Alembic commands ###
