"""Add CALL

Revision ID: c5bcf7e4c825
Revises: 6b3e7d9900c2
Create Date: 2024-06-07 17:21:48.919932

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c5bcf7e4c825'
down_revision: Union[str, None] = '6b3e7d9900c2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
