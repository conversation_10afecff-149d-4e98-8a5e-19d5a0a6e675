"""room update

Revision ID: a13ebd74b3c4
Revises: f782e84dd1f6
Create Date: 2024-01-22 16:21:35.302365

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a13ebd74b3c4'
down_revision: Union[str, None] = 'f782e84dd1f6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rooms', sa.Column('closed_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rooms', 'closed_at')
    # ### end Alembic commands ###
