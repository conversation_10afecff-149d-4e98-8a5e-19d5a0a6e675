"""Add Call model

Revision ID: 942a232bc792
Revises: d437a63d748a
Create Date: 2024-06-07 17:02:10.234892

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '942a232bc792'
down_revision: Union[str, None] = 'd437a63d748a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.create_table(
        'calls',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('consultant_id', sa.Integer(), nullable=False),
        sa.Column('client_id', sa.Integer(), nullable=False),
        sa.Column('start_time', sa.DateTime(), nullable=False),
        sa.Column('end_time', sa.DateTime(), nullable=True),
        sa.Column('call_type', sa.String(), nullable=False),
        sa.Column('cost', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['consultant_id'], ['consultants.id'], ),
        sa.ForeignKeyConstraint(['client_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    op.drop_table('calls')