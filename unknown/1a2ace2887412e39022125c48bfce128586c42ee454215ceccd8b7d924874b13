"""Add session_duration, video_call_cost, and voice_call_cost to consultants

Revision ID: f1e1fc85ec38
Revises: 942a232bc792
Create Date: 2024-06-07 17:10:51.283898

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f1e1fc85ec38'
down_revision: Union[str, None] = '942a232bc792'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
