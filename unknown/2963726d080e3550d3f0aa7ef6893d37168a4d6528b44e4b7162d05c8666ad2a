from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import aliased, joinedload

from models import *

subq = db.query(Room.id, func.max(Message.id).label('max_id')).join(Message).group_by(Room.id).subquery()
q = db.query(
    Room,
    func.count(func.distinct(Message.id)).label('messages_count'),
    func.count(func.distinct(Message.id)).filter(
        Message.has_read == False, Message.by_user_type == 'Consultant'
    ).label('unread_count')
).filter(Room.messages.any()).join(Message).filter(
    and_(Room.id == subq.c.id, Message.id == subq.c.max_id)
).order_by(Message.id.desc()).group_by(Room.id, Message.id)

# q = db.query(Room).filter(
#     Room.messages.any()
# ).join(Message).group_by(Room.id, Message.at_time).distinct(Room.id).order_by(Message.id.desc())

print(q.first()[0].id)
#
# y = db.query(
#     Room,
#     func.count(func.distinct(Message.id)).label('messages_count'),
#     func.count(func.distinct(Message.id)).filter(Message.has_read == False).label('unread_count')
# ).join(Message, Message.room_id == Room.id).filter(Room.messages.any()).group_by(Room.id, Message.id).distinct(
#     Room.id).order_by(Message.id, Room.id)
#
# print(q.count(), q.first().id)
# print(y.count(), y.first()[0].id)
#
# # for room, c, uc in y.all():
# #     print(room.id, c, uc)
# #     print([msg.has_read for msg in room.messages])
# #     print()
