import os
import time

from dotenv import load_dotenv, dotenv_values

# بارگذاری فایل‌های env
load_dotenv()
load_dotenv('.env.prod')  # بارگذاری تنظیمات Redis

CONFIG = {
    **dotenv_values('db.env'),  # load config variables
    **os.environ,  # override loaded values with environment variables
}

os.environ['TZ'] = 'Asia/Tehran'
time.tzset()


def config(key, default=None):
    return CONFIG.get(key) or default


DB = {
    "mongo": {
        "user": config('MONGO_USER'),
        "password": config('MONGO_PASSWORD'),
        "name": config('MONGO_DB', 'najm-chat'),
        "port": int(config('MONGO_PORT', 27017)),
        "host": config('MONGO_HOST', 'localhost'),
    },
    "redis": {
        "host": config('REDIS_HOST', 'najm_redis'),
        "port": int(config('REDIS_PORT', 6379)),
        "db": int(config('REDIS_DB', 2)),
        "password": config('REDIS_PASSWORD', None),
        "username": config('REDIS_USERNAME', None),
    },
}
