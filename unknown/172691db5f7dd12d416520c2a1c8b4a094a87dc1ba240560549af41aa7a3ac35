import asyncio
import datetime
import json
import logging
from math import ceil

from datetime import datetime, timezone
from pydantic import ValidationError
from sqlalchemy import Date, func
from sqlalchemy.sql.elements import and_

from gpt import gpt_ask
from models import Consultant, Topic, Admin, Room, Message, User
from schemas import request_types
from schemas.models import MessageModel
from schemas.response_types import *
from schemas.response_types import ConsultantsListResponse, RoomListResponse
from utils.fcm_notification import send_notification
from utils.paginate import paginate
from utils.welcome_msg import welcome_msg_bot
from collections import defaultdict
from utils.hlog import (
    log_message, format_telegram_message, format_telegram_chat_history,
    send_telegram_async, HISTORY_CHANNEL_ID
)


class ActionHandler:
    actions: dict = {}
    ai_bots = {
        '<EMAIL>': {
            'type': 'ahkam',
            'welcome_message': "Habib AI assistant bot provides  answers about Islamic law and can assist with matters related to various aspects of life. The information is sourced from authentic sources and scholarly opinions"
        },
        '<EMAIL>': {
            'type': 'beliefs',
            'welcome_message': 'Welcome to Habib AI assistant bot for beliefs Questions! Our AI bot is designed to help you gain a deeper understanding of Islamic beliefs and practices. With authentic sources and scholarly opinions as our basis, we provide accurate and trustworthy answers to your questions. So feel free to ask us anything, and let us help you strengthen your faith and knowledge.'
        },
    }
    errors: dict = {}
    from_user = None
    user_connections = []
    room_connections = []
    connection = None
    data = None
    action = None
    db = None
    # waiting_tasks: dict = {}


    def __init__(self):
        self.actions = {
            "getConsultants": self.get_consultants,
            "getRooms": self.get_rooms,
            "startRoom": self.start_room,
            "sendMessage": self.send_message,
            "getHistory": self.get_history,
            "closeRoom": self.close_room,
            "openRoom": self.open_room,
            "readMsg": self.read_message,
            "getStats": self.get_stats,
            "sendAudioMessage": self.send_audio_message,
        }

    async def send_audio_message(self, data):
        room = self.db.session.query(Room).filter(
            Room.id == data['room_id']
        ).first()

        if not room:
            return {
                'act': data['action'],
                'error': 'Room not found',
            }

        try:
            msg = Message(
                content='',
                mime_type='audio',
                audio_url=data['audio_url'],
                chat_type='VoiceChat',
                by_user_id=self.from_user.id,
                by_user_type=self.from_user.__class__.__name__,
                room_id=room.id,
            )
            self.db.session.add(msg)
            self.db.session.commit()
            self.db.session.refresh(msg)
        except Exception as e:
            return {
                'act': data['action'],
                'error': 'Failed to send message',
                'room_id': data['room_id']
            }

        msg_data = MessageModel(
            **msg.to_dict(),
        ).dict()
        msg_data['date'] = str(msg_data['date'])
        msg_data['type'] = "VoiceChat"

        await self.broadcast_to_users([room.client, room.consultant], msg_data)

        return {
            'act': data['action'],
            'room_id': room.id,
            'message_id': msg.id,
            'status': True,
            'msg_data': msg_data,
        }

    async def close_room(self, data):
        if not data.get('room_id'):
            return {
                'act': data['action'],
                'error': 'room id is empty'
            }

        if room := self.db.session.query(Room).filter(Room.id == data['room_id']).first():
            if room.consultant_id == self.from_user.id or room.client.id == self.from_user.id:
                room.status = 'c'
                room.reason = data['reason']
                self.db.session.commit()
                resp_data = {
                    'act': 'closeRoom',
                    'room_id': data['room_id'],
                    'reason': data['reason'],
                    'success': True,
                }
                await self.broadcast_to_users([room.client, room.consultant], data=json.dumps(resp_data))
                return

        return {
            'success': False,
            'room_id': data['room_id'],
            'act': 'closeRoom',
            'reason': '',
            'error': 'operation no permitted',
        }

    async def open_room(self, data):
        if not data.get('room_id'):
            return {
                'act': data['action'],
                'error': 'room id is empty'
            }

        if room := self.db.session.query(Room).filter(Room.id == data['room_id']).first():
            if room.consultant.id == self.from_user.id or room.client.id == self.from_user.id:
                room.status = 'o'
                self.db.session.commit()
                resp_data = {
                    'act': 'openRoom',
                    'room_id': data['room_id'],
                    'success': True,
                }
                await self.broadcast_to_users([room.client, room.consultant], data=json.dumps(resp_data))
                return

        return {
            'success': False,
            'room_id': data['room_id'],
            'act': 'openRoom',
            'error': 'operation no permitted',
        }

    async def broadcast_to_users(self, users, data, send_notification_if_offline: bool = True):
        success = False
        if type(data) is not str:
            data = json.dumps(data)
        try:
            for user in users:
                if connections := self.user_connections.get(user.username):

                    for conn_index, connection in enumerate(connections):
                            if connection.client_state.value == 1 and connection.application_state.value == 1:
                                await connection.send_text(data)
                                success = True
                            else:
                                connections.pop(conn_index)
                                print(f"{user.username} connection has been closed = removed from active list")

                else:
                    if send_notification_if_offline:
                        # Use user's device_os for platform-specific notifications
                        await self.send_notif(user, data)
                        success = True
                    logging.info(f"No active connections for user {user.username}, skipping notification due to flag.")
                    continue
            return success
        except Exception as e:
            print(f'---error-broadcast_to_users-> {e}')
            return False

    async def send_notif(self, user, data, platform=None):
        if not getattr(user, 'fcm', None):
            pass
        if isinstance(data, str):
            data = json.loads(data)

        # Use user's device_os if platform not provided
        if platform is None:
            platform = getattr(user, 'device_os', 'android')

        notif_map = {
            'message': {
                'title': 'New Message',
                'body': data.get('content', '')[:130]
            },
            # 'call_request': {
            #     'title': 'Call Request',
            #     'body': 'there is a client eagerly waiting for your expertise on a Video/Voice Message'
            # }
        }

        if _map := notif_map.get(data['act']):
            try:
                # Create the new notification data structure for message notifications
                notification_data = None
                if data['act'] == 'message':
                    # Get sender information from the message data
                    sender_info = data.get('by_user', {})
                    room_id = data.get('room_id', '')

                    notification_data = {
                        "model": "talk",
                        "navigation": "/TalkChatPage",
                        "data": {
                            "call_type": "chat",
                            "from_user_fullname": sender_info.get('fullname', ''),
                            "from_user_username": sender_info.get('username', ''),
                            "call_id": str(room_id)
                        }
                    }

                # Use the new notification data structure if available, otherwise fall back to the old structure
                final_data = notification_data if notification_data else _map
                await send_notification([user.fcm], _map['title'], _map['body'], final_data, platform=platform)
            except Exception as exp:
                print(f'--error-send_notification-> {exp}')


    async def send_message(self, data: request_types.SendMessage, by_user=None, as_bot=False):
        # print('send_message ->>>> ', data)
        room: Room = data.room
        if type(room) is ValueError:
            return {
                'error': 'not a valid room',
            }

        if type(self.from_user) is Admin:
            # print(f"{self.from_user} texting as consultant")
            by_user = room.consultant
        else:
            by_user = self.from_user

        # Check if user can send message to AI consultant
        if type(self.from_user) is User and room.consultant.is_ai:
            from services.subscription_service import SubscriptionService

            # Check if user can send message to this consultant
            can_send, reason = SubscriptionService.can_message_consultant(room.client_id, room.consultant_id)

            if not can_send:
                return {
                    "status": False,
                    "message": "You have reached your message limit for this AI consultant",
                    "code": "ai_message_limit_reached",
                    # "reason": reason
                }

            # Record that a message was sent from user to AI consultant
            SubscriptionService.record_message_sent(room.client_id, room.consultant_id)



        try:
            # Store message timestamp in UTC
            utc_now = datetime.now(timezone.utc)  # Ensure UTC timestamp

            # print("saving message 2/ ", data)
            msg = Message(
                mime_type='txt',
                content=data.text,
                by_user_id=by_user.id,
                by_user_type=by_user.__class__.__name__,
                room_id=room.id,
                at_time=utc_now  # Use UTC timestamp

            )
            self.db.session.add(msg)
            self.db.session.commit()
            self.db.session.refresh(msg)
        except Exception as e:
            print('ERROR save message: ', e, data)

        # print('send_message:message saved 3->>', msg)

        msg_data = MessageModel(
            **msg.to_dict(),
        ).dict()
        msg_data['tmp_id'] = data.tmp_id
        msg_data['date'] = str(msg_data['date'])
        msg_data['payload'] = data.payload

        print(f'---> {self.from_user.username} sent message to telegram')
        try:
            # If message is from a user, send just this message to Telegram
            if type(self.from_user) is User:
                formatted_msg = format_telegram_message(
                    user_fullname=msg_data['by_user']['fullname'],
                    language=msg.by_user.language_code,
                    consultant_fullname=msg.room.consultant.fullname,
                    message_text=msg_data['content'],
                    created_at=msg_data['date'],
                    room_id=msg.room.id
                )
                asyncio.create_task(
                    send_telegram_async(formatted_msg)
                )
            # If message is from a consultant, send the entire chat history to Telegram
            elif type(self.from_user) is Consultant or type(self.from_user) is Admin:
                # Get all messages from this room
                messages = []
                for room_msg in self.db.session.query(Message).filter(
                    Message.room_id == room.id
                ).order_by(Message.id.asc()).all():
                    messages.append(
                        MessageModel(
                            **room_msg.to_dict(),
                        ).dict()
                    )

                # Format the entire chat history for Telegram
                formatted_history = format_telegram_chat_history(
                    messages=messages,
                    room_id=room.id,
                    client_fullname=room.client.fullname,
                    consultant_fullname=room.consultant.fullname,
                    language=room.client.language_code
                )
                # Send the formatted history to the dedicated history channel
                asyncio.create_task(
                    send_telegram_async(formatted_history, history=True)
                )
        except Exception as exp:
            print(f'--error-send-tel--> {exp}')
            pass

        if type(self.from_user) is Admin:
            await self.broadcast_to_users([room.client, room.consultant, self.from_user], msg_data)
        await self.broadcast_to_users([room.client, room.consultant], msg_data)

        # Auto-respond if message is from user to AI consultant
        if type(self.from_user) is User and room.consultant.is_ai:
            await self._auto_respond_from_ai_consultant(room, data.text)

    async def get_history(self, data: dict):

        messages = []

        # اعتبارسنجی مدل GetHistory
        try:
            if isinstance(data, dict):
                validated_data = request_types.GetHistory(**data)
            else:
                validated_data = data
        except ValidationError as e:
            raise ValueError(e.errors())

        # دریافت پیام‌ها از دیتابیس
        for msg in self.db.session.query(Message).filter(Message.room_id == validated_data.room.id).order_by(
                Message.id.asc()).all():
            messages.append(
                MessageModel(
                    **msg.to_dict(),
                ))

        # ساخت و بازگشت پاسخ تاریخچه
        return HistoryResponse(
            results=messages, count=len(messages), room_id=str(validated_data.room.id),
            status='open' if validated_data.room.status == 'o' else 'closed', reason=validated_data.room.reason,
            room=RoomsResponse(**validated_data.room.to_dict())
        ).json()

    async def get_consultants(self, data):
        _filters = {
            'is_banned': False,
            'visible': True,
        }
        if not data.language:
            data.language = getattr(self.from_user, 'language_code', 'en')

        def get_language_name(code):
            langs = {
                'en': 'English',
                'fa': 'Persian',
                'ar': 'Arabic',
                'fr': 'French',
                'in': 'Indonesian',
                'tr': 'Turkish',
                'az': 'Azerbaijani',
                'ru': 'Russian',
                'sw': 'Swahili',
                'es': 'Spanish',
                'ur': 'Urdu',
                'de': 'German',
            }
            return langs.get(code) or 'English'

        _user = "User" if self.from_user.user_type == "Consultant" else "Consultant"

        objects = []
        query = self.db.session.query(Consultant).filter(
            # Consultant.status == True,
            Consultant.visible == True,
            Consultant.is_banned == False,
        )
        data.language = get_language_name(data.language)

        if self.from_user.user_type == "Admin" or self.from_user.username == "<EMAIL>":
            query = query.all()
        else:
            query = query.filter(
                Consultant.languages.contains(data.language)
            )

        for obj in query.all():
            obj_dict = obj.to_dict(data.language)

            obj_dict['estimate_time'] = self.calculate_estimate_time_to_answer()
            obj_dict['availability_status'] = self.calculate_estimate_time_to_answer()
            obj_dict['unread_count'] = 0
            objects.append(obj_dict)

        return ConsultantsListResponse(results=objects).json()

    async def get_rooms(self, data):
        subq = self.db.session.query(Room.id, func.max(Message.id).label('max_id')).join(Message).group_by(
            Room.id).subquery()
        unread_user_type_filter = 'Consultant' if self.from_user.user_type == 'User' else 'User'
        base_query = self.db.session.query(
            Room,
            func.count(func.distinct(Message.id)).label('messages_count'),
            func.count(func.distinct(Message.id)).filter(
                Message.has_read == False,
                Message.by_user_type == unread_user_type_filter,
            ).label('unread_count')
        ).filter(Room.messages.any()).join(Message).filter(
            and_(Room.id == subq.c.id, Message.id == subq.c.max_id)
        ).order_by(Message.id.desc()).group_by(Room.id, Message.id)

        def to_response(rows):
            data = []
            for row, messages_count, unread_count in rows:
                if messages_count == 0:
                    continue

                data.append(
                    RoomsResponse(**row.to_dict(), unread_count=unread_count, messages_count=messages_count)
                )
            return data

        if type(self.from_user) is Consultant:
            objects = base_query.filter(
                Room.consultant_id == self.from_user.id,
            )

            count = objects.count()
            per_page = 10
            paginated_objects = paginate(objects, page=data.page, per_page=per_page)
            return RoomListResponse(
                current_page=data.page,
                total_pages=ceil(count / per_page),
                count=count,
                per_page=per_page,
                results=to_response(paginated_objects)
            ).json()

        elif type(self.from_user) is Admin:
            if self.from_user.username == "<EMAIL>":
                base_query = base_query.filter(Room.consultant_id.in_([9, 12, 17, 16, 15, 3]))

            objects = base_query
            count = objects.count()
            per_page = 10
            paginated_objects = paginate(objects, page=data.page, per_page=per_page)
            return RoomListResponse(
                current_page=data.page,
                total_pages=ceil(count / per_page),
                count=count,
                per_page=per_page,
                results=to_response(paginated_objects)).json()
        else:
            if not hasattr(self.from_user, 'id'):
                return {'error': 'کاربر شناسایی نشد', 'act': data.type, 'status': False}

            objects = base_query.filter(
                Room.client_id == self.from_user.id, Room.consultant == data.consultant
            )
            return RoomListResponse(
                results=to_response(objects)
            ).json(exclude={'count', 'per_page', 'total_pages', 'current_page'})

    async def start_room(self, data):
        if type(self.from_user) is Consultant:
            return {
                'act': data.type,
                'status': False,
                'error': 'only regular users can start room'
            }

        today_room_count = self.db.session.query(
            Room, func.count(Message.id)
        ).filter(
            func.cast(Room.created_at, Date) > datetime.now().date(),
            Room.client == self.from_user,
        ).join(Message).group_by(Room.id).having(func.count(Message.id) >= 2).count()

        if data.message:
            messages = [
                Message(
                    mime_type='txt',
                    content=data.message,
                    by_user=self.from_user,
                    by_user_type=self.from_user.__class__.__name__,
                )
            ]
        else:
            messages = []

        obj = Room(
            client=self.from_user,
            consultant=data.consultant,
            messages=messages,
            status='c' if today_room_count > 2 else 'o',
            reason="reached today's limit" if today_room_count > 2 else None,
        )
        self.db.session.add(obj)
        self.db.session.commit()
        self.db.session.refresh(obj)

        # print("room created", obj)
        json_data = RoomsResponse(**obj.to_dict()).json()
        await self.broadcast_to_users([obj.client, obj.consultant], json_data, send_notification_if_offline=False)

        if data.consultant.is_ai:
            client_lang = obj.client.language_code or 'en'
            msg = request_types.SendMessage(
                text=welcome_msg_bot.get(client_lang, welcome_msg_bot['en']),
                room=str(obj.id),
            )
            await asyncio.sleep(0.5)
            await self.send_message(msg, by_user=data.consultant, as_bot=True)

    async def read_message(self, data):
        messages = []
        by_user_type = "Consultant" if self.from_user is User else "User"
        try:
            # آغاز تراکنش
            self.db.session.begin()

            # به‌روزرسانی وضعیت پیام‌ها
            self.db.session.query(Message).filter(
                Message.room_id == data['room_id'],
                Message.by_user_id != self.from_user.id,
            ).update({
                Message.has_read: True,
            })

            # اجرای commit برای اعمال تغییرات
            self.db.session.commit()

            # گرفتن پیام‌های به‌روز شده
            updated_messages = self.db.session.query(Message).filter(
                Message.room_id == data['room_id'],
                Message.by_user_type == by_user_type,
                Message.has_read == True,
            ).all()

            # افزودن پیام‌های به‌روز شده به لیست پیام‌ها
            messages = [msg.to_dict() for msg in updated_messages]

            room = self.db.session.query(Room).filter(Room.id == data['room_id']).first()

            data = {
                'act': 'readMsg',
                'room': str(room.id),
                'user_type': self.from_user.user_type,
                'username': self.from_user.username.split(':')[0] if ':' in str(self.from_user.username) else self.from_user.username,
                'messages': messages
            }
            await self.broadcast_to_users([room.client, room.consultant], data)

        except Exception as e:
            # در صورت بروز خطا، انجام rollback
            self.db.session.rollback()
            raise e
        finally:
            # پایان تراکنش
            self.db.session.close()

    async def get_stats(self, data):
        return {
            "act": "getStats",
            "experts": self.db.session.query(Consultant).count(),
            "questions": self.db.session.query(Room).count(),
            "languages": 10,
            "subjects": self.db.session.query(Topic).count(),
        }

    def calculate_estimate_time_to_answer(self):
        return "10 minutes"

    async def _auto_respond_from_ai_consultant(self, room, user_message):
        """
        Automatically generate and send a response from an AI consultant

        Args:
            room: The room object
            user_message: The message sent by the user
        """
        import asyncio
        from datetime import datetime, timezone
        from models.message import Message
        from schemas.response_types import MessageModel

        try:
            # Get AI response
            ai_response = "Hello"

            # Create a new message from the consultant
            utc_now = datetime.now(timezone.utc)

            # Create and save the AI response message
            ai_msg = Message(
                mime_type='txt',
                content=ai_response,
                by_user_id=room.consultant.id,
                by_user_type='Consultant',
                room_id=room.id,
                at_time=utc_now
            )

            self.db.session.add(ai_msg)
            self.db.session.commit()
            self.db.session.refresh(ai_msg)

            # Prepare message data for broadcasting
            msg_data = MessageModel(
                **ai_msg.to_dict(),
            ).dict()
            msg_data['date'] = str(msg_data['date'])
            msg_data['payload'] = {}

            # Broadcast the AI response to users in the room
            await self.broadcast_to_users([room.client, room.consultant], msg_data)

            # Log the AI response
            print(f"AI consultant ({room.consultant.username}) responded to message: {user_message}")

        except Exception as e:
            print(f"Error in auto-responding from AI consultant: {e}")


    # async def create_notification(self, room: 'Room', message: Type['BaseMessage'],
    #                               online_users_to_exclude: List['User']):
    #     """
    #         this method should implement with celery
    #     """
    #     # if not isinstance(message, ):
    #     #     return
    #
    #     for user in room.members:
    #         if user not in online_users_to_exclude:
    #             Notification(
    #                 chat=chat,
    #                 message=message,
    #                 user=user,
    #             ).save()

    async def __call__(self, db_session, data, connection, room_connections, user_connections, waiting_tasks):
        self.action = data.get('action')
        self.data = data
        self.connection = connection
        self.room_connections = room_connections
        self.user_connections = user_connections
        self.from_user = connection.user
        self.db = db_session
        self.waiting_tasks = waiting_tasks

        try:
            request_model = getattr(request_types, self.action[0].upper() + self.action[1:], None)

            if not request_model:
                req_model = self.data
            else:
                req_model = request_model(**self.data)

            if self.action in self.actions:
                log_message(f"--[{self.__class__.__name__}]--event--({self.action})", 'O', self.data)

                resp = await self.actions[self.action](req_model)
                if resp and type(resp) is not str:
                    return json.dumps(resp)

                if self.errors:
                    return json.dumps({
                        'error': self.errors
                    })

                return resp

            return None
        except ValidationError as e:
            logging.exception(e)
            return json.dumps({
                'action': self.action,
                'error': e.errors()
            })
        except Exception as e:
            logging.exception(e)
            return json.dumps({
                'action': self.action,
                'error': "something went wrong, please try again",
            })

def process_username(v):
    if isinstance(v, str) and ':' in v:
        return v.split(':')[0]
    return v
