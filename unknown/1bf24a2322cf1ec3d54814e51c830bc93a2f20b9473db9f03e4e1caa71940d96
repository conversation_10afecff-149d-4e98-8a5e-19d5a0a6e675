import datetime

from sqlalchemy import <PERSON>um<PERSON>, Integer, String, JSON, DateTime

from models import Base


class Topic(Base):
    __tablename__ = "topics"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String)
    title_translations = Column(JSON, nullable=True, default=lambda: {}) # [{"language_code": "fa", "text": "title fa"}, {"language_code": "en", "text": "title en"}]
    created_at = Column(DateTime, default=datetime.datetime.now)
    
    def get_title_for_language(self, lang: str) -> str:
        if self.title_translations and isinstance(self.title_translations, list):
            # First, try to find the translation for the specified language
            for translation in self.title_translations:
                if isinstance(translation, dict) and translation.get("language_code") == lang:
                    return translation.get("text", self.title)
            # If not found, try to find the English translation
            for translation in self.title_translations:
                if isinstance(translation, dict) and translation.get("language_code") == "en":
                    return translation.get("text", self.title)
        return self.title or ""