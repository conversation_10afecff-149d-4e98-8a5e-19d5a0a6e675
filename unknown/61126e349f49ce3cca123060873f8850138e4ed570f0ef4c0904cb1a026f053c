<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Audio Conference</title>
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
    body {
        margin-top: 100px; /* فاصله دادن محتوای دیگر از بالای صفحه */
    }
    background-color: hwb(0 18% 81%);
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }
    h3 {
      margin-bottom: 20px;
    }
    .btn {
        color: #000000;
        background-color: rgb(255, 255, 255);
    }
    .btn-group button {
      width: 150px;
    }
    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        margin-bottom: 5px;
        font-weight: bold;
        display: block;
    }
    .form-container {
        position: fixed;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        padding: 20px;
        width: 550px;
        background-color: hwb(0 18% 81%);
        box-shadow: 2px 2px 10px rgba(255, 255, 255, 0.1);
        border-radius: 0 5px 5px 0;
    }
    .form-control {
        width: 100%;
        padding: 10px;
        border: 4px solid #ff0000;
        border-radius: 10px;
        background-color: #6d6d6d;
        color: red;
    }
    #token {
        position: fixed;
        bottom: 0;
        left: 50%;

        /* top: 0; */
        /* right: 0; */
        /* left: 0; */
        /* left: 100; */
        width: 490px;
        height: 40px;
        background-color: #333;
        color: white;
        padding: 10px;
        overflow-y: scroll;
        border-top-right-radius: 10px;
        box-shadow: 2px 2px 10px rgba(255, 255, 255, 0.5);
        font-family: monospace;
        font-size: 14px;
    }
    #log {
        position: fixed;
        /* bottom: 0; */
        top: 0;
        right: 0;
        /* left: 0; */
        /* left: 0; */
        width: 200px;
        height: 500px;
        background-color: #333;
        color: white;
        border-radius: 5px;
        padding: 10px;
        overflow-y: scroll;
        border-top-right-radius: 10px;
        box-shadow: 2px 2px 10px rgba(255, 255, 255, 0.5);
        font-family: monospace;
        font-size: 14px;
    }
    #volume-display {
        position: fixed;
        bottom: 0;
        right: 0;
        width: 200px;
        height: 145px;
        overflow-y: scroll;
        background-color: hwb(0 18% 81%);
        color: white;
        padding: 10px;
        font-size: 12px;
        border-top-right-radius: 10px;
        box-shadow: 2px 2px 10px rgba(255, 255, 255, 0.5);

    }
    .line {
        color: white;
        /* font-size: 30px; */
        border-radius: 5px;

    }
    #vertical-line {
        position: fixed;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 5px;
        height: 100%;
        background-color: rgb(255, 255, 255);
        z-index: 1000; /* خط همیشه بالای بقیه عناصر باشد */
    }
    .control-panel {
        /* position: fixed; */
        /* top: 10; */
        bottom: 50px;
        left: 20px;
        background-color: #6d6d6d;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    }
    .control-panel button {
        padding: 10px 20px;
        margin: 5px;
        font-size: 14px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .control-panel button:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }
    .control-panel #toggle-mic.active {
        background-color: red;
        color: white;
    }
    #participants-list {
        position: fixed;
        /* bottom: 100; */
        /* top: 0; */
        /* right: 0; */
        width: 30%;
        height: 100%;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        grid-auto-rows: 150px;
        gap: 10px;
        padding: 10px;
        background-color: #6d6d6d;
        overflow-y: auto;
    }
    .participant {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #ffffff;
        border: 2px solid #ddd;
        border-radius: 10px;
        padding: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    .participant strong {
        font-size: 1.2em;
        margin-bottom: 10px;
    }

    .participant .bi {
        font-size: 2em;
        margin-top: 10px;
    }
    .form-group {
        margin-bottom: 15px;
    }

    .form-control {
        color: rgb(255, 255, 255);
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 4px;
    }
    .form-control::placeholder {
        color: rgb(255, 255, 255); /* رنگ دلخواه */
    }
    .participants-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        grid-auto-rows: 150px;
        gap: 10px;
        padding: 10px;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 8px;
        max-height: 500px;
        overflow-y: auto;
    }
  .participant-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
  }
  .participant-card strong {
    font-size: 1.2em;
    margin-bottom: 5px;
  }

  .participant-card .bi {
    font-size: 2em;
    margin-top: 10px;
  }
  .participants-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px; /* فاصله بین آیتم‌ها */
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    max-height: 500px;
    overflow-y: auto;
  }

  .participants-grid li {
    list-style-type: none;
    width: 150px;
    height: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
  }

  .participants-grid li strong {
    font-size: 1.2em;
    margin-bottom: 5px;
  }

  .participants-grid li .bi {
    font-size: 2em;
    margin-top: 10px;
  }
.main {
    position: relative; /* Maintains natural position in the document flow */
    z-index: 9999; /* Ensures it stays above all other elements */
    color: white; /* White color for the text */
    font-size: 3em; /* Large font size */
    font-weight: bold; /* Bold font weight */
    text-align: center; /* Center the text */
    margin: 0; /* Remove any default margins */
    margin-bottom: 40px;
    padding: 20px; /* Add some padding */
    background-color: rgba(105, 105, 105, 0.7); /* Semi-transparent background for better readability */
}
  </style>
</head>
<body>
    <div class="content">
        
        <div class="container mt-12">
            <div class="row">
                
                <!-- Left Side: User Controls -->
                <div class="col-md-6">
                    
                    <div class="form-container">
                        <h1 class="main line">Talk LiveKit</h1>
                        <!-- <h3 class="line">Join a Room </h3> -->
                        <div class="form-group">
                            <label for="usertoken" class="line">User Token</label>
                            <input type="text" id="usertoken" class="form-control" placeholder="Enter your user token">
                        </div>
    
    
                <div class="form-group">
                <label for="call-id" class="line">Call ID</label>
                <input type="text" id="call-id" class="form-control" placeholder="Enter Call ID">
                </div>
    
            <div class="form-group">
                
                <label for="user-role" class="line"><span>User Role Handle Front</span></label>
                <select id="user-role" class="form-control">
                    <option value="1">Can Speak and Listen</option>
                    <option value="0">Listen Only</option>
                </select>
            </div>
    
            <div class="form-group">
            <label for="type" class="line">Speaker/Listener Handle Server Token</label>
            <input type="text" id="type" class="form-control" value="true" placeholder="Enter Type ture/false">
            </div>
            <button id="connect-room" class="btn btn-secondary  mb-3">Connect</button>
            <button id="disConnect-room" class="btn btn-secondary  mb-3">disConnect</button>
    
            <button id="join-room" class="btn btn-secondary  mb-3">Join Room</button>
            <div class="control-panel">
            <h5 class="line">Room Controls</h5>
    
            <div class="form-group">
            <button id="toggle-mic" class="btn " disabled>Mute</button>
            <button id="toggle-hand-btn">hand ✋</button>

            <!-- <button id="btn-unmute" class="btn btn-success">Unmute</button> -->
            </div>
            </div>
            
        </div>
            <pre id="log"><h4>Logs</h4></pre>
        </div>
        <div id="vertical-line"></div>
    
            <div id="volume-display"> <h4>volume audio</h4></div>
                    <input type="text" id="token" value="Token" disabled >
    
        <!-- Right Side: Participants List -->
        <div class="col-md-6">
            <h3 class="line">Participants</h3>
            
            <ul id="participants-list" class="participants-grid">
                <!-- List of participants will be appended here -->
                </ul>
        </div>
        </div>
    </div>
    </div>

  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
  <!-- <script src="https://cdn.jsdelivr.net/npm/livekit-client@2.5.0/dist/livekit-client.umd.min.js"></script> -->
  <script src="https://cdn.jsdelivr.net/npm/livekit-client/dist/livekit-client.umd.min.js"></script>
    <script>
        let Token = null;
        let currentRoom = null
        const volumeDisplay = document.getElementById('volume-display');    
        const connectBtn = document.getElementById('connect-room') 
        const disConnectBtn = document.getElementById('disConnect-room') 
        const participantList = document.getElementById('participants-list') 
     <!---   const baseUrl = `wss://livekitmeet.duckdns.org` -->
        const baseUrl = `wss://livekit.habibmeet.nwhco.ir` 
        const getTokenUrl = `https://qa.habibapp.com/livekit/token/`
        const inputToken = document.getElementById('token');
        const toggleMicButton = document.getElementById('toggle-mic');
        const userRoleInput = document.getElementById('user-role');  // نقش کاربر
        const toggleHandBtn = document.getElementById('toggle-hand-btn');
        let username = null
        let roomName = null
        let isHandRaised = false; // وضعیت اولیه دست (پایین)

        // console.log(LivekitClient)
        disConnectBtn.disabled = false
        connectBtn.addEventListener('click', async () => {
            const usertoken1 = document.getElementById('usertoken');
            const callId1 = document.getElementById('call-id');
            username = usertoken1.value
            roomName = callId1.value
            logMessage('Connect ...');
            const type = document.getElementById('type').value;

            if (!username || !roomName || !type) {
                logMessage('Please fill out all fields.');
                return;
            }
            try {
                logMessage('get token ...');
                const dataToken = await requestToken(getTokenUrl, roomName, username, type) 
                if (dataToken){
                    inputToken.disabled = false
                    inputToken.value= dataToken
                    token = dataToken
                }

                logMessage('token > '+ dataToken);
                
            } catch (error) {
                logMessage('Failed to get token: ' + error.message);
            }

        }) 

        document.getElementById('join-room').addEventListener('click', async () => {

            try {

                LivekitClient.setLogLevel(LivekitClient.LogLevel.debug)

                const room = new LivekitClient.Room({
                    audioCaptureDefaults: {
                        autoGainControl: true,
                        echoCancellation: true,
                        noiseSuppression: true,
                    },
                })
                
                logMessage('Connect Room ... ' + baseUrl);
                const startTime = Date.now();
                
                await room.prepareConnection(baseUrl, token);

                const prewarmTime = Date.now() - startTime;
                logMessage('Connectd Room' + prewarmTime);

                currentRoom = room;
                window.currentRoom = room;

                currentRoom
                    .on(LivekitClient.RoomEvent.ParticipantConnected, participantConnected)
                    .on(LivekitClient.RoomEvent.ParticipantDisconnected, participantDisconnected)
                    .on(LivekitClient.RoomEvent.Disconnected, handleRoomDisconnect)
                    .on(LivekitClient.RoomEvent.LocalTrackPublished, (pub) => {
                        if (pub.track.kind === 'audio') {
                            const { calculateVolume } = LivekitClient.createAudioAnalyser(pub.track);
                            setInterval(() => {
                                const volume = calculateVolume();
                                appendVolumeLog(`Volume: ${volume.toFixed(2)}`);
                            }, 1000);
                        }
                        renderParticipant(currentRoom.localParticipant);
                        updateMicButtonState();

                    })
                    .on(LivekitClient.RoomEvent.TrackSubscribed, handleTrackSubscribed)
                    .on(LivekitClient.RoomEvent.TrackUnsubscribed, handleTrackUnsubscribed)
                    .on(LivekitClient.RoomEvent.ParticipantIsSpeaking, (participant) => {
                        updateParticipantSpeakingStatus(participant, true);
                    })
                    .on(LivekitClient.RoomEvent.ParticipantStoppedSpeaking, (participant) => {
                        updateParticipantSpeakingStatus(participant, false);
                    })
                    .on(LivekitClient.RoomEvent.TrackMuted, handleTrackMuted)
                    .on(LivekitClient.RoomEvent.TrackUnmuted, handleTrackUnmuted)
                    .on(LivekitClient.RoomEvent.ParticipantIsSpeaking, handleParticipantSpeaking)
                    .on(LivekitClient.RoomEvent.ParticipantStoppedSpeaking, handleParticipantStoppedSpeaking)
                    .on(LivekitClient.RoomEvent.LocalTrackPublished, setHighAudioQuality); // اضافه کردن رویداد برای اطمینان از انتشار ترک‌ها
                    



                await currentRoom.connect(baseUrl, token);

                currentRoom.on(LivekitClient.RoomEvent.DataReceived,(msg, participant) => {
                        const metadata = JSON.parse(participant.metadata || '{}');
                        console.logs('>>>>>>> OK >>>>>>>>')
                        updateHandStatus(participant.identity, metadata.handRaised);
                    });
                logMessage('connected room')
                connectBtn.disabled = true;
                disConnectBtn.disabled = false;
                // toggleMicButton.disabled = false;
                const canSpeak = userRoleInput.value === "1";
                
                await currentRoom.localParticipant.setMicrophoneEnabled(canSpeak);
                toggleMicButton.disabled = !canSpeak;
                
                setHighAudioQuality();
                renderParticipant(currentRoom.localParticipant);
                
                currentRoom.remoteParticipants.forEach(participantConnected);

            } catch (error) {
                logMessage('Failed to connect: ' + error.message);
            }
        })
        // تابع برای ارسال پیام داده به اتاق
        async function sendDataMessage(message) {
            try {
                console.log('>>>> Sending Data Message:', JSON.stringify(message));
                let m = JSON.stringify(message)
                currentRoom.localParticipant.publishData(JSON.stringify(message), { reliable: false });

                // await currentRoom.localParticipant.publishData(
                    // JSON.stringify(message),
                    // { reliable: true }
                // );
            } catch (error) {
                console.error('Failed to send data message:', error);
            }
        }

        // رویداد برای تغییر وضعیت دست
        toggleHandBtn.addEventListener('click', () => {
            console.log('>>>> Toggle Hand Status');
            isHandRaised = !isHandRaised; // تغییر وضعیت دست
            const message = {
                action: isHandRaised ? 'raise_hand' : 'lower_hand',
                userId: username // شناسه کاربر فعلی
            };
            sendDataMessage(message); // ارسال پیام داده
            // toggleHandBtn.textContent = isHandRaised ? '👋' : '✋'; // تغییر متن دکمه
            toggleHandBtn.className = isHandRaised ? 'bi bi-arrow-up-circle-fill' : 'bi bi-arrow-down-square-fill'
        });

        // تابع برای دریافت و پردازش پیام‌های داده
        function handleData(payload, participant) {
            console.log('Received Data:', payload, participant);
            const data = JSON.parse(new TextDecoder().decode(payload));

            // if (data.action === 'raise_hand') {
                // updateHandStatus(data.userId, true);
            // } else if (data.action === 'lower_hand') {
                // updateHandStatus(data.userId, false);
            // }
        }

        function updateHandStatus(userId, isHandRaised) {
            const participantElement = document.getElementById(`participant-${userId}`);
            if (!participantElement) {
                const newParticipant = document.createElement('div');
                newParticipant.id = `participant-${userId}`;
                newParticipant.textContent = `${userId}: ${isHandRaised ? '✋ دست بالا' : '👋 دست پایین'}`;
                participantsContainer.appendChild(newParticipant);
            } else {
                participantElement.textContent = `${userId}: ${isHandRaised ? '✋ دست بالا' : '👋 دست پایین'}`;
            }
        }


        function setHighAudioQuality() {
            const bitrate = 64 * 1024; // 64 kbps برای کیفیت بالا
            
            // بررسی ترک‌های صوتی و به‌روزرسانی نرخ بیت
            if (currentRoom.localParticipant && currentRoom.localParticipant.audioTracks) {
                currentRoom.localParticipant.audioTracks.forEach(trackPublication => {
                if (trackPublication.track) {
                    trackPublication.track.setMaxBitrate(bitrate);
                }
                });
                logMessage(`Audio quality set to high (${bitrate / 1024} kbps)`);
            } else {
                logMessage('No audio tracks available to set quality.');
            }
        }
        function handleTrackUnmuted(track, participant) {
            if (track.kind === 'audio') {
                const micStatusElement = document.getElementById(`mic-status-${participant.sid}`);
                if (micStatusElement) {
                micStatusElement.className = 'bi bi-mic-fill';
                }
            }
        }
        function handleTrackMuted(track, participant) {
            if (track.kind === 'audio') {
                const micStatusElement = document.getElementById(`mic-status-${participant.sid}`);
                if (micStatusElement) {
                micStatusElement.className = 'bi bi-mic-mute-fill';
                }
            }
        }
        function handleParticipantStoppedSpeaking(participant) {
            const speakingStatusElement = document.getElementById(`speaking-status-${participant.sid}`);
            if (speakingStatusElement) {
                speakingStatusElement.style.display = 'none';
            }
        }
        
        function handleParticipantSpeaking(participant) {
          const speakingStatusElement = document.getElementById(`speaking-status-${participant.sid}`);
          if (speakingStatusElement) {
            speakingStatusElement.style.display = 'inline';
          }
        }

        function handleTrackSubscribed(track, publication, participant) {
            if (track.kind === 'audio') {
                const audioElement = document.createElement('audio');
                audioElement.id = `audio-${track.sid}`;
                audioElement.autoplay = true;
                audioElement.srcObject = new MediaStream([track.mediaStreamTrack]);
                participantList.appendChild(audioElement);
                logMessage(`Audio track subscribed: ${participant.identity}`);
            }
        }
        
        function handleTrackUnsubscribed(track) {
            if (track.kind === 'audio') {
                const audioElement = document.getElementById(`audio-${track.sid}`);
                if (audioElement) {
                    audioElement.remove();
                }
                logMessage(`Audio track unsubscribed: ${track.sid}`);
            }
        }






        function participantConnected(participant) {
            console.log('>>>>>>>>>>>>>>>>>>>>>>>>>.')
            console.log(participant)
            logMessage(`Participant connected: ${participant.identity}`);
            renderParticipant(participant);
        }

        function renderParticipant(participant) {
            const participantsList = participantList;
            let participantDiv = document.getElementById(`participant-${participant.sid}`);
            if (!participantDiv) {
                participantDiv = document.createElement('div');

                participantDiv.id = `participant-${participant.sid}`;
                participantDiv.className = 'participant-card';
                participantDiv.innerHTML = `
                <strong>${participant.identity}</strong>
                <span id="mic-status-${participant.sid}" class="bi ${participant.isMicrophoneEnabled ? 'bi-mic-fill' : 'bi-mic-mute-fill'}"></span>
                <span id="speaking-status-${participant.sid}" class="bi bi-volume-up-fill" style="display: ${participant.isSpeaking ? 'inline' : 'none'}; color: green;"></span>
                <span id="hand-status-${username.value}" class="bi ${isHandRaised ? 'bi-hand-index-fill' : null}"></span>
                `;
                // participantDiv.appendChild(participantLi)
                participantList.appendChild(participantDiv);
            } else {
                // به‌روزرسانی وضعیت آیکون‌ها
                document.getElementById(`mic-status-${participant.sid}`).className = `bi ${participant.isMicrophoneEnabled ? 'bi-mic-fill' : 'bi-mic-mute-fill'}`;
            }
        }
        function updateParticipantSpeakingStatus(participant, isSpeaking) {
            const speakingStatusElement = document.getElementById(`speaking-status-${participant.sid}`);
            if (speakingStatusElement) {
                speakingStatusElement.style.display = isSpeaking ? 'inline' : 'none';
            }
        }

        function participantDisconnected(participant) {
            logMessage(`Participant disconnected: ${participant.identity}`);
            removeParticipant(participant);
        }
        function removeParticipant(participant) {
            const participantDiv = document.getElementById(`participant-${participant.sid}`);
            if (participantDiv) {
                participantList.removeChild(participantDiv);
            }
        }



        function handleRoomDisconnect() {
            logMessage('Disconnected from room');
            connectBtn.disabled = false;
            disConnectBtn.disabled = true;
            clearParticipants();
        }
        


        disConnectBtn.addEventListener('click', () => {
            logMessage('Disconnected from room');
            if (currentRoom) {
                currentRoom.disconnect();
                clearParticipants()
                
            }
        });
        
        
        function clearParticipants() {
            connectBtn.disabled = false;
            disConnectBtn.disabled = true;
            toggleMicButton.disabled = true;
            participantList.innerHTML = '';
        }
        toggleMicButton.addEventListener('click', async () => {
            
            if (!currentRoom) return;
        
            const micEnabled = currentRoom.localParticipant.isMicrophoneEnabled;
            await currentRoom.localParticipant.setMicrophoneEnabled(!micEnabled);
            updateMicButtonState();
        });

        function updateMicButtonState() {
            const micEnabled = currentRoom.localParticipant.isMicrophoneEnabled;
            toggleMicButton.textContent = micEnabled ? 'Mute' : 'Unmute';
            toggleMicButton.classList.toggle('active', !micEnabled);
        }
        


        async function requestToken(getTokenUrl, roomName, username, type) {
	    console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>',getTokenUrl)
            const response = await fetch(getTokenUrl ,{
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event: 'Talk',
                    call_id: roomName,
                    user_token: username,
                    can_publish: type,
                    can_subscribe: true
                }),
            });
        
            if (!response.ok) {
                throw new Error('Failed to fetch token');
            }
        
            const data = await response.json();
            return data.token;
        }


        function logMessage(message) {
            const log = document.getElementById('log');
            log.textContent += `${message}\n`;
            log.scrollTop = log.scrollHeight; // اسکرول خودکار به انتهای لاگ‌ها

        }
        function appendVolumeLog(message) {
            const logItem = document.createElement('div');
            logItem.textContent = message;
            volumeDisplay.appendChild(logItem);
            volumeDisplay.scrollTop = volumeDisplay.scrollHeight; // اسکرول به انتهای کادر
        }
    </script>
</body>
</html>
