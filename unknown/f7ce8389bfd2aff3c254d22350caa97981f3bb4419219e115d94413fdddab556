import os
import json
from typing import Any, Optional, Dict
import redis.asyncio as aioredis
import logging
from settings import config

# تنظیم لاگر
logger = logging.getLogger(__name__)

class RedisClient:
    """
    A singleton client for interacting with Redis asynchronously.
    Supports common operations like get, set, delete, exists, etc.
    """
    _instance = None
    _redis = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisClient, cls).__new__(cls)
        return cls._instance
    
    async def initialize(self):
        """
        Initialize the Redis connection using settings from config.
        """
        if self._redis is None:
            redis_host = config('REDIS_HOST', 'najm_redis')
            redis_port = int(config('REDIS_PORT', 6379))
            redis_db = int(config('REDIS_DB', 2))
            redis_password = config('REDIS_PASSWORD', None)
            
            try:
                self._redis = await aioredis.from_url(
                    f"redis://{redis_host}:{redis_port}/{redis_db}",
                    password=redis_password,
                    encoding="utf-8",
                    decode_responses=True
                )
                logger.info(f"Connected to Redis at {redis_host}:{redis_port}, DB: {redis_db}")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                raise
    
    async def get_connection(self):
        """
        Get the Redis connection, initializing it if necessary.
        
        Returns:
            Redis connection object
        """
        if self._redis is None:
            await self.initialize()
        return self._redis
    
    async def ping(self) -> bool:
        """
        Check if the Redis connection is alive.
        
        Returns:
            bool: True if connection is alive, False otherwise
        """
        try:
            redis = await self.get_connection()
            return await redis.ping()
        except Exception as e:
            logger.error(f"Redis ping error: {e}")
            return False
    
    async def close(self) -> None:
        """
        Close the Redis connection.
        """
        if self._redis is not None:
            try:
                await self._redis.close()
                self._redis = None
                logger.info("Redis connection closed")
            except Exception as e:
                logger.error(f"Error closing Redis connection: {e}")
    
    async def set_json(self, key: str, value: Any, expiry: int = None) -> bool:
        """
        Set a JSON value in Redis with optional expiry time.
        
        Args:
            key: Redis key
            value: Value to store (will be JSON serialized)
            expiry: Optional expiry time in seconds
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            redis = await self.get_connection()
            serialized = json.dumps(value)
            if expiry:
                await redis.setex(key, expiry, serialized)
            else:
                await redis.set(key, serialized)
            return True
        except Exception as e:
            logger.error(f"Redis set_json error: {e}")
            return False
    
    async def get_json(self, key: str) -> Optional[Any]:
        """
        Get a JSON value from Redis.
        
        Args:
            key: Redis key
            
        Returns:
            The deserialized JSON value or None if not found
        """
        try:
            redis = await self.get_connection()
            value = await redis.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Redis get_json error: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """
        Delete a key from Redis.
        
        Args:
            key: Redis key
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            redis = await self.get_connection()
            await redis.delete(key)
            return True
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in Redis.

        Args:
            key: Redis key

        Returns:
            bool: True if key exists, False otherwise
        """
        try:
            redis = await self.get_connection()
            return await redis.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis exists error: {e}")
            return False

    async def keys(self, pattern: str) -> list:
        """
        Get all keys matching a pattern.

        Args:
            pattern: Redis key pattern (e.g., "consultants:*")

        Returns:
            list: List of matching keys
        """
        try:
            redis = await self.get_connection()
            keys = await redis.keys(pattern)
            return keys if keys else []
        except Exception as e:
            logger.error(f"Redis keys error: {e}")
            return []

    async def delete_pattern(self, pattern: str) -> int:
        """
        Delete all keys matching a pattern.

        Args:
            pattern: Redis key pattern (e.g., "consultants:*")

        Returns:
            int: Number of keys deleted
        """
        try:
            redis = await self.get_connection()
            keys = await redis.keys(pattern)
            if keys:
                deleted_count = await redis.delete(*keys)
                logger.info(f"Deleted {deleted_count} keys matching pattern: {pattern}")
                return deleted_count
            else:
                logger.info(f"No keys found matching pattern: {pattern}")
                return 0
        except Exception as e:
            logger.error(f"Redis delete_pattern error: {e}")
            return 0

# ایجاد یک نمونه سینگلتون از کلاس RedisClient
redis_client = RedisClient()

async def get_redis_client() -> RedisClient:
    """
    Helper function to get the Redis client instance.
    
    Returns:
        RedisClient: The Redis client instance
    """
    return redis_client