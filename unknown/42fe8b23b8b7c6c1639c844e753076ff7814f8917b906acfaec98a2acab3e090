#! python

import click
from passlib.context import CryptContext
import db.mongo  # noqa
from models import Consultant, Admin

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
from models import SessionLocal, Admin


@click.option('--username', help='username')
@click.option('--password', help='password')
@click.command()
def create_admin(username, password):
    with SessionLocal() as session:
        if session.query(Admin).filter(Admin.username == username).first():
            print(f"Admin with username '{username}' already exists")
            return

        o = Admin(
            username=username,
            password=pwd_context.hash(password)
        )
        session.add(o)
        session.commit()
        session.refresh(o)
        print('Token:', o.token)


if __name__ == '__main__':
    create_admin()
