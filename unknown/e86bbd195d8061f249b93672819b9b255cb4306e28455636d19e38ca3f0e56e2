#!/usr/bin/env python
import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("telegram_bot.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import the function from the bot module
try:
    from bot import start_polling
    
    # Start the bot
    start_polling()
    
except Exception as e:
    logger.error(f"Error running the Telegram bot: {e}")
    sys.exit(1)