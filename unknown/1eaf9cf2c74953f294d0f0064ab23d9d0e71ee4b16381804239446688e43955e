from fastapi.exceptions import HTTPException


class APIException(HTTPException):
    detail = None

    def __init__(
            self,
            status_code=400,
            detail='',
            headers=None,
    ):
        super().__init__(status_code, detail, headers)


class APIPermissionException(HTTPException):
    detail = 'Token missmatch or missing in header'

    def __init__(
            self,
            status_code=401,
            detail='',
            headers=None,
    ):
        super().__init__(status_code, detail, headers)


def process_username(username):
	if isinstance(username, str) and ':' in username:
		return username.split(':')[0]
	return username