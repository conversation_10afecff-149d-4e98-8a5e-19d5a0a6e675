import datetime
import logging
from math import ceil
import calendar

from sqlalchemy import func, and_, or_
from sqlalchemy.orm import aliased
from sqlalchemy.sql import select

from apps.actions import ActionHandler
from models import Consultant, Rate, Room, Message, Admin, Category, Topic, Subscription, UserSubscription, User
from models.calls import Call
from schemas.models import MessageModel
from schemas.request_types_v2 import GetConsultants, GetConsultant, StartRoom, ConsultantsChangeStatus, GetReservationSlots, ReserveCallSlot, GetUserReservations, GetConsultantReservations, AddPreferredLanguage
from schemas.response_types import RoomListResponse, RoomsResponse
from schemas.response_types_v2 import ConsultantsListResponse, ConsultantsResponse, ConsultantResponse, ChangeStatusResponse, DynamicConsultantsListResponse
from utils.paginate import paginate
from utils.hlog import log_message, format_telegram_message, send_telegram_async
from utils.redis_client import redis_client
from utils.timezone_converter import convert_scheduling_timezone, should_consultant_be_online_by_schedule

# تنظیم لاگر
logger = logging.getLogger(__name__)


def get_language_name(codes):
    langs = {
        'en': 'English',
        'fa': 'Persian',
        'ar': 'Arabic',
        'fr': 'French',
        'in': 'Indonesian',
        'tr': 'Turkish',
        'az': 'Azerbaijani',
        'ru': 'Russian',
        'sw': 'Swahili',
        'es': 'Spanish',
        'ur': 'Urdu',
        'de': 'German',
    }
    
    # اگر ورودی یک رشته باشد، آن را به لیست تبدیل می‌کنیم
    if isinstance(codes, str):
        codes = [codes]

    # برگرداندن لیستی از نام زبان‌ها
    return [langs.get(code, 'English') for code in codes]


class ActionHandlerV2(ActionHandler):
    def __init__(self):
        super().__init__()
        self.actions['getCategories'] = self.get_categories
        self.actions['changeStatus'] = self.change_status
        self.actions['updateMessage'] = self.update_message
        self.actions['deleteMessage'] = self.delete_message
        self.actions['getConsultant'] = self.get_consultant
        self.actions['getConsultants'] = self.get_consultants
        self.actions['getRooms'] = self.get_rooms
        self.actions['subscriptionInfo'] = self.subscription_info
        self.actions['purchaseSubscription'] = self.purchase_subscription
        self.actions['getReservationSlots'] = self.get_reservation_slots
        self.actions['reserveCallSlot'] = self.reserve_call_slot
        self.actions['getUserReservations'] = self.get_user_reservations
        self.actions['getConsultantReservations'] = self.get_consultant_reservations
        self.actions['getDynamicConsultants'] = self.get_dynamic_consultants
        self.actions['addPreferredLanguage'] = self.add_preferred_language

    async def change_status(self, data, internal: str = None):
        try:
            # استفاده از پارامتر داده شده به تابع به جای self.data
            data = ConsultantsChangeStatus(**data)

            # اعمال تغییرات در پایگاه داده
            consultant_username = self.from_user.username if not internal else internal
            self.db.session.query(Consultant).filter(
                Consultant.username == consultant_username,
            ).update({
                Consultant.status: data.status,

                Consultant.status_from_schedule: False,
            })

            # commit تغییرات
            self.db.session.commit()

            # ساخت پاسخ
            resp = ChangeStatusResponse(consultant=consultant_username, status=data.status)

            # ارسال پاسخ به کلاینت‌ها
            for u, cc in self.user_connections.items():
                for c in cc:
                    if c.client_state.value == 1 and c.application_state.value == 1:
                        await c.send_text(resp.json())
        except Exception as e:
            logger.error(f"An error occurred in change_status: {e}")

    async def get_consultants(self, data):
        """
        Retrieve a list of consultants based on specified filters.
        
        This method implements a caching strategy to improve performance:
        1. First checks Redis cache for consultant data
        2. If found, only updates dynamic data (status, unread messages)
        3. If not found, fetches complete data from database
        
        The results are sorted by:
        - Consultants with unread messages first
        - Then by consultant order (higher values first)
        - Then by online status
        
        Args:
            data: GetConsultants object containing filter parameters
            
        Returns:
            JSON response with list of consultants matching criteria
        """
        data = GetConsultants(**self.data)
        
        # Create cache key based on request parameters
        cache_key = f"consultants:{self.from_user.id}:{data.language_code}:{data.only_interacted}:{data.search}"
        
        # Try to get data from cache
        cached_data = await redis_client.get_json(cache_key)
        
        # If data exists in cache, only update dynamic information
        if cached_data:
            logger.info(f"Using cached consultants data for user {self.from_user.id} - Found {len(cached_data)} consultants in cache")
            
            # Log some details about cached data before update
            unread_before = sum(item.get('unread_count', 0) for item in cached_data)
            online_before = sum(1 for item in cached_data if item.get('availability_status') == 'online')
            
            # Update status and unread message count for each consultant
            start_time = datetime.datetime.now()
            # await self._update_dynamic_consultant_data(cached_data)
            update_time = (datetime.datetime.now() - start_time).total_seconds()
            
            # Log some details about cached data after update
            unread_after = sum(item.get('unread_count', 0) for item in cached_data)
            online_after = sum(1 for item in cached_data if item.get('availability_status') == 'online')
            
            # Sort results
            cached_data.sort(
                key=lambda x: (
                    x['unread_count'] == 0,  # Priority 1: Consultants with unread messages
                    -x['order'], 
                    x['availability_status'] != "online",  # Priority 2: Online consultants
                )
            )
            
            return ConsultantsListResponse(results=cached_data).json()
        

        accept_language = self.connection.headers.get("accept-language", "en").split(",")[0][:2]
        _user = "Consultant" if self.from_user.user_type == "Consultant" else "User"
        objects = []

        # Build base query with visibility and ban filters
        query = (
            self.db.session.query(Consultant)
            .filter(
                Consultant.visible == True,
                Consultant.is_banned == False
            )
            .distinct(Consultant.username)
        )

        # Filter out tester consultants for non-tester users
        # This handles both is_tester boolean field and consultant_type string field
        if not self.from_user.is_tester:
            query = query.filter(
                Consultant.is_tester == False,
                Consultant.consultant_type != 'tester'
            )

        # Define unread_message_alias for use throughout the method
        unread_message_alias = aliased(Message)

        # First identify consultants with unread messages
        consultants_with_unread_messages = []

        if self.from_user.user_type != "Admin" and self.from_user.username != "<EMAIL>":
            # Find rooms related to current user
            user_rooms = self.db.session.query(Room).filter(
                (Room.client_id == self.from_user.id) | (Room.consultant_id == self.from_user.id)
            ).all()

            # Check for unread messages in each room
            for room in user_rooms:
                consultant_id = room.consultant_id
                unread_count = self.db.session.query(func.count(unread_message_alias.id)) \
                    .filter(
                        unread_message_alias.room_id == room.id,
                        unread_message_alias.has_read == False,
                        unread_message_alias.by_user_id != self.from_user.id,
                        (unread_message_alias.room.has(client_id=self.from_user.id) |
                         unread_message_alias.room.has(consultant_id=self.from_user.id))
                    ).scalar()

                if unread_count > 0 and consultant_id not in consultants_with_unread_messages:
                    consultants_with_unread_messages.append(consultant_id)

        language_codes = data.language_code.split(",")
        languages = get_language_name(language_codes)

        if not data.only_interacted:
            if self.from_user.user_type == "Admin" or self.from_user.username == "<EMAIL>":
                query = query.all()
            else:
                # Exclude consultants with unread messages from language filter
                if consultants_with_unread_messages:
                    query = query.filter(
                        or_(
                            Consultant.id.in_(consultants_with_unread_messages),
                            or_(*[Consultant.languages.contains(language) for language in languages])
                        )
                    )
                else:
                    query = query.filter(
                        or_(*[Consultant.languages.contains(language) for language in languages])
                    )
        else:
            interacted_consultant_ids = self.db.session.query(
                Consultant.id
            ).join(Call, Call.consultant_id == Consultant.id).filter(
                Call.client_id == self.from_user.id
            ).union(
                self.db.session.query(Consultant.id).join(Message, Message.room_id == Consultant.id).filter(
                    Message.by_user_id == self.from_user.id,
                    Message.by_user_type == "User"
                )
            ).all()

            interacted_consultant_ids = [c[0] for c in interacted_consultant_ids]
            query = query.filter(Consultant.id.in_(interacted_consultant_ids))

        if data.search:
            search_term = f"%{data.search}%"
            query = query.filter(
                or_(
                    Consultant.fullname.ilike(search_term),
                    Consultant.username.ilike(search_term)
                )
            )
            
        rate_data = (
            self.db.session.query(
                Rate.consultant,
                func.avg(Rate.rate).label('avg_rate')
            )
            .group_by(Rate.consultant)
            .all()
        )

        rate_dict = {consultant_id: avg_rate for consultant_id, avg_rate in rate_data}

        results = query.distinct(Consultant.username).all()
        user_language = data.language_code
        # if _user == "User":
            # self.from_user.add_preferred_language(self.db.session, user_language)     

        # Collect all consultant IDs for efficient batch processing
        consultant_ids = [obj.id for obj in results]
        
        # Start timing the optimized unread count calculation
        unread_start_time = datetime.datetime.now()
        
        # Get all rooms for these consultants in a single query
        all_rooms = self.db.session.query(Room).filter(
            Room.consultant_id.in_(consultant_ids),
            or_(Room.client_id == self.from_user.id, Room.consultant_id == self.from_user.id)
        ).all()
        
        # Group rooms by consultant ID
        rooms_by_consultant = {}
        all_room_ids = []
        for room in all_rooms:
            if room.consultant_id not in rooms_by_consultant:
                rooms_by_consultant[room.consultant_id] = []
            rooms_by_consultant[room.consultant_id].append(room.id)
            all_room_ids.append(room.id)
        
        logger.info(f"Found {len(all_rooms)} rooms for {len(rooms_by_consultant)} consultants")
        
        # Get unread message counts for all rooms in a single query
        unread_messages_by_room = {}
        if all_room_ids:
            unread_counts_query = (
                self.db.session.query(
                    Message.room_id,
                    func.count(Message.id).label('count')
                )
                .filter(
                    Message.room_id.in_(all_room_ids),
                    Message.has_read == False,
                    Message.by_user_id != self.from_user.id
                )
                .group_by(Message.room_id)
                .all()
            )
            
            unread_messages_by_room = {room_id: count for room_id, count in unread_counts_query}
            
        
        # Get call status for all consultants in one query
        consultants_on_call = set(
            call.consultant_id for call in self.db.session.query(Call.consultant_id).filter(
                Call.consultant_id.in_(consultant_ids),
                Call.status == "confirmed",
                Call.end_time.is_(None)
            ).all()
        )
        
        
        # Now process each consultant with the pre-fetched data
        for obj in results:
            # Get basic consultant info (without status and unread count)
            obj_dict = obj.to_dict(user_language)
            obj_dict['estimate_time'] = ''
            obj_dict['categories'] = [i.title for i in obj.categories]
            obj_dict['avg_rate'] = ceil(rate_dict.get(obj.id, 5.0))
            obj_dict['order'] = obj.order
            
            # Dynamic data that must always be fetched from database
            obj_dict['availability_status'] = obj.status
            obj_dict['is_on_another_call'] = obj.id in consultants_on_call
            
            # Calculate unread message count from pre-fetched data
            room_ids = rooms_by_consultant.get(obj.id, [])
            unread_count = sum(unread_messages_by_room.get(room_id, 0) for room_id in room_ids)
            
            obj_dict['unread_count'] = unread_count
            objects.append(obj_dict)

        # Log summary of database results
        total_unread = sum(obj.get('unread_count', 0) for obj in objects)
        online_count = sum(1 for obj in objects if obj.get('availability_status') == 'online')
        
        await redis_client.set_json(cache_key, objects, expiry=180)
        
        # Sort results
        objects.sort(
            key=lambda x: (
                x['unread_count'] == 0,  # Priority 1: Consultants with unread messages
                -x['order'], 
                x['availability_status'] != "online",  # Priority 2: Online consultants
            )
        )
        
        return ConsultantsListResponse(results=objects).json()
        
    async def get_dynamic_consultants(self, data):
        """
        Retrieve a simplified list of consultants with only dynamic data (username, status, unread_count).
        
        This method is an optimized version of get_consultants that returns only the essential
        dynamic information about consultants, making it more efficient for status updates.
        
        Args:
            data: GetConsultants object containing filter parameters
            
        Returns:
            JSON response with simplified list of consultants containing only username, status, and unread_count
        """
        # Use the data parameter instead of self.data
        if not isinstance(data, GetConsultants):
            data = GetConsultants(**data)
        logger.info(f"Fetching dynamic consultants data from database for user {self.from_user.id}")

        # Build base query with visibility and ban filters
        query = (
            self.db.session.query(Consultant)
            .filter(
                Consultant.visible == True,
                Consultant.is_banned == False
            )
            .distinct(Consultant.username)
        )

        # Filter out tester consultants for non-tester users
        # This handles both is_tester boolean field and consultant_type string field
        if not self.from_user.is_tester:
            query = query.filter(
                Consultant.is_tester == False,
                Consultant.consultant_type != 'tester'
            )

        # Define unread_message_alias for use throughout the method
        unread_message_alias = aliased(Message)

        # First identify consultants with unread messages
        consultants_with_unread_messages = []

        if self.from_user.user_type != "Admin" and self.from_user.username != "<EMAIL>":
            # Find rooms related to current user
            user_rooms = self.db.session.query(Room).filter(
                (Room.client_id == self.from_user.id) | (Room.consultant_id == self.from_user.id)
            ).all()

            # Check for unread messages in each room
            for room in user_rooms:
                consultant_id = room.consultant_id
                unread_count = self.db.session.query(func.count(unread_message_alias.id)) \
                    .filter(
                        unread_message_alias.room_id == room.id,
                        unread_message_alias.has_read == False,
                        unread_message_alias.by_user_id != self.from_user.id,
                        (unread_message_alias.room.has(client_id=self.from_user.id) |
                         unread_message_alias.room.has(consultant_id=self.from_user.id))
                    ).scalar()

                if unread_count > 0 and consultant_id not in consultants_with_unread_messages:
                    consultants_with_unread_messages.append(consultant_id)

        language_codes = data.language_code.split(",")
        languages = get_language_name(language_codes)

        if not data.only_interacted:
            if self.from_user.user_type == "Admin":
                query = query.all()
            else:
                # Exclude consultants with unread messages from language filter
                if consultants_with_unread_messages:
                    query = query.filter(
                        or_(
                            Consultant.id.in_(consultants_with_unread_messages),
                            or_(*[Consultant.languages.contains(language) for language in languages])
                        )
                    )
                else:
                    query = query.filter(
                        or_(*[Consultant.languages.contains(language) for language in languages])
                    )
        else:
            interacted_consultant_ids = self.db.session.query(
                Consultant.id
            ).join(Call, Call.consultant_id == Consultant.id).filter(
                Call.client_id == self.from_user.id
            ).union(
                self.db.session.query(Consultant.id).join(Message, Message.room_id == Consultant.id).filter(
                    Message.by_user_id == self.from_user.id,
                    Message.by_user_type == "User"
                )
            ).all()

            interacted_consultant_ids = [c[0] for c in interacted_consultant_ids]
            query = query.filter(Consultant.id.in_(interacted_consultant_ids))

        if data.search:
            search_term = f"%{data.search}%"
            query = query.filter(
                or_(
                    Consultant.fullname.ilike(search_term),
                    Consultant.username.ilike(search_term)
                )
            )
            
        results = query.distinct(Consultant.username).all()

        # Debug: Log consultant IDs and usernames
        consultant_info = [(obj.id, obj.username, obj.visible, obj.is_banned, obj.is_tester, obj.languages) for obj in results]

        user_language = data.language_code
        # if _user == "User":
            # self.from_user.add_preferred_language(self.db.session, user_language)     

        # Collect all consultant IDs for efficient batch processing
        consultant_ids = [obj.id for obj in results]
        
        # Start timing the optimized unread count calculation
        unread_start_time = datetime.datetime.now()
        
        # Get all rooms for these consultants in a single query
        all_rooms = self.db.session.query(Room).filter(
            Room.consultant_id.in_(consultant_ids),
            or_(Room.client_id == self.from_user.id, Room.consultant_id == self.from_user.id)
        ).all()
        
        # Group rooms by consultant ID
        rooms_by_consultant = {}
        all_room_ids = []
        for room in all_rooms:
            if room.consultant_id not in rooms_by_consultant:
                rooms_by_consultant[room.consultant_id] = []
            rooms_by_consultant[room.consultant_id].append(room.id)
            all_room_ids.append(room.id)
        
        
        # Get unread message counts for all rooms in a single query
        unread_messages_by_room = {}
        if all_room_ids:
            unread_counts_query = (
                self.db.session.query(
                    Message.room_id,
                    func.count(Message.id).label('count')
                )
                .filter(
                    Message.room_id.in_(all_room_ids),
                    Message.has_read == False,
                    Message.by_user_id != self.from_user.id
                )
                .group_by(Message.room_id)
                .all()
            )
            
            unread_messages_by_room = {room_id: count for room_id, count in unread_counts_query}
        
        # Get call status for all consultants in one query
        consultants_on_call = set(
            call.consultant_id for call in self.db.session.query(Call.consultant_id).filter(
                Call.consultant_id.in_(consultant_ids),
                Call.status == "confirmed",
                Call.end_time.is_(None)
            ).all()
        )
        
        # Calculate total time for optimized unread count calculation
        unread_time = (datetime.datetime.now() - unread_start_time).total_seconds()
        
        # Now process each consultant with the pre-fetched data
        objects = []
        simplified_objects = []
        
        for obj in results:
            # Get basic consultant info (without status and unread count)
            obj_dict = obj.to_dict(user_language)
            obj_dict['estimate_time'] = ''
            obj_dict['categories'] = [i.title for i in obj.categories]
            obj_dict['order'] = obj.order
            
            # Dynamic data that must always be fetched from database
            obj_dict['availability_status'] = obj.status
            obj_dict['is_on_another_call'] = obj.id in consultants_on_call
            
            # Calculate unread message count from pre-fetched data
            room_ids = rooms_by_consultant.get(obj.id, [])
            unread_count = sum(unread_messages_by_room.get(room_id, 0) for room_id in room_ids)
            
            obj_dict['unread_count'] = unread_count
            objects.append(obj_dict)
            
            # Create simplified version with only username, status, and unread_count
            # Check if consultant should be online based on scheduling
            consultant_status = obj.status

            # Only update status if status_from_schedule is enabled
            if obj.status_from_schedule:
                should_be_online = should_consultant_be_online_by_schedule(obj, data.timezone)
                logger.info(f"Consultant {obj.id} ({obj.username}) - status_from_schedule=True, should_be_online={should_be_online}, current_status={obj.status}")

                if should_be_online:
                    consultant_status = 'online'
                    # Update the consultant's status in database if it's not already online
                    if obj.status != 'online':
                        logger.info(f"Updating consultant {obj.id} status from {obj.status} to online")
                        self.db.session.query(Consultant).filter(
                            Consultant.id == obj.id
                        ).update({
                            Consultant.status: 'online'
                        })
                        # Also update the object for consistency
                        obj.status = 'online'
                else:
                    consultant_status = 'offline'
                    # Update the consultant's status in database if it's not already offline
                    if obj.status != 'offline':
                        logger.info(f"Updating consultant {obj.id} status from {obj.status} to offline")
                        self.db.session.query(Consultant).filter(
                            Consultant.id == obj.id
                        ).update({
                            Consultant.status: 'offline'
                        })
                        # Also update the object for consistency
                        obj.status = 'offline'

            simplified_obj = {
                'username': obj.username,
                'status': consultant_status,
                'unread_count': unread_count
            }
            simplified_objects.append(simplified_obj)

        # Commit any status changes to database
        self.db.session.commit()

        # Log summary of database results
        total_unread = sum(obj.get('unread_count', 0) for obj in objects)
        online_count = sum(1 for obj in objects if obj.get('availability_status') == 'online')
        # Sort results
        simplified_objects.sort(
            key=lambda x: (
                x['unread_count'] == 0,  # Priority 1: Consultants with unread messages
                x['status'] != "online",  # Priority 2: Online consultants
            )
        )
        
        return DynamicConsultantsListResponse(results=simplified_objects).json()
        
    async def _update_dynamic_consultant_data(self, consultants_data):
        """
        Update dynamic consultant information (status and unread message count).
        
        This method efficiently updates dynamic data for multiple consultants in a single operation:
        1. Fetches all consultants in one query
        2. Gets all relevant rooms in one query
        3. Calculates unread message counts with optimized queries
        4. Updates call status for all consultants at once
        
        Args:
            consultants_data: List of dictionaries containing consultant information
        """
        logger.info(f"Starting dynamic data update for {len(consultants_data)} consultants")
        start_time = datetime.datetime.now()
        
        # Get all consultant IDs in a list
        consultant_ids = [data.get('id') for data in consultants_data if data.get('id')]
        logger.info(f"Found {len(consultant_ids)} valid consultant IDs for update")
        
        if not consultant_ids:
            logger.warning("No valid consultant IDs found, skipping dynamic update")
            return
        
        # Fetch all consultants in one query
        consultants = {
            c.id: c for c in self.db.session.query(Consultant).filter(
                Consultant.id.in_(consultant_ids)
            ).all()
        }
        logger.info(f"Fetched {len(consultants)} consultants from database")
        
        # Get all rooms related to these consultants and current user in one query
        rooms = self.db.session.query(Room).filter(
            Room.consultant_id.in_(consultant_ids),
            or_(Room.client_id == self.from_user.id, Room.consultant_id == self.from_user.id)
        ).all()
        
        # Get all room IDs in a list for a single unread messages query
        all_room_ids = [room.id for room in rooms]
        
        # Group rooms by consultant ID for reference
        rooms_by_consultant = {}
        for room in rooms:
            if room.consultant_id not in rooms_by_consultant:
                rooms_by_consultant[room.consultant_id] = []
            rooms_by_consultant[room.consultant_id].append(room.id)
        
        logger.info(f"Found {len(rooms)} rooms for {len(rooms_by_consultant)} consultants")
        
        # Optimize: Get all unread messages in a single query with room_id and count
        unread_messages_by_room = {}
        if all_room_ids:
            # Use a more efficient query with group by to get counts per room
            unread_counts_query = (
                self.db.session.query(
                    Message.room_id,
                    func.count(Message.id).label('count')
                )
                .filter(
                    Message.room_id.in_(all_room_ids),
                    Message.has_read == False,
                    Message.by_user_id != self.from_user.id
                )
                .group_by(Message.room_id)
                .all()
            )
            
            # Create a dictionary of room_id -> unread_count
            unread_messages_by_room = {room_id: count for room_id, count in unread_counts_query}
            
            logger.info(f"Found unread messages in {len(unread_messages_by_room)} rooms")
        
        # Calculate unread counts per consultant by summing room counts
        unread_counts = {}
        for consultant_id, room_ids in rooms_by_consultant.items():
            consultant_unread = sum(unread_messages_by_room.get(room_id, 0) for room_id in room_ids)
            unread_counts[consultant_id] = consultant_unread
            if consultant_unread > 0:
                logger.info(f"Consultant ID {consultant_id}: Found {consultant_unread} unread messages across {len(room_ids)} rooms")
        
        # Get call status for all consultants in one query
        consultants_on_call = set(
            call.consultant_id for call in self.db.session.query(Call.consultant_id).filter(
                Call.consultant_id.in_(consultant_ids),
                Call.status == "confirmed",
                Call.end_time.is_(None)
            ).all()
        )
        
        logger.info(f"Found {len(consultants_on_call)} consultants currently on calls")
        
        # Update consultant information
        updated_count = 0
        for consultant_data in consultants_data:
            consultant_id = consultant_data.get('id')
            if not consultant_id or consultant_id not in consultants:
                continue
            
            consultant = consultants[consultant_id]
            old_status = consultant_data.get('availability_status')
            old_call_status = consultant_data.get('is_on_another_call')
            old_unread = consultant_data.get('unread_count', 0)
            
            consultant_data['availability_status'] = consultant.status
            consultant_data['is_on_another_call'] = consultant_id in consultants_on_call
            consultant_data['unread_count'] = unread_counts.get(consultant_id, 0)
            
            # Log changes
            if (old_status != consultant_data['availability_status'] or 
                old_call_status != consultant_data['is_on_another_call'] or 
                old_unread != consultant_data['unread_count']):
                logger.info(f"Updated consultant {consultant.username} (ID: {consultant_id}): "
                           f"Status: {old_status} -> {consultant_data['availability_status']}, "
                           f"On call: {old_call_status} -> {consultant_data['is_on_another_call']}, "
                           f"Unread: {old_unread} -> {consultant_data['unread_count']}")
                updated_count += 1
        
        execution_time = (datetime.datetime.now() - start_time).total_seconds()
        logger.info(f"Dynamic update completed in {execution_time:.3f}s: {updated_count} consultants had changes")


        
    async def get_consultant(self, data):
        """
        Retrieve detailed information about a specific consultant.
        
        This method implements a caching strategy to improve performance:
        1. First checks Redis cache for consultant data
        2. If found, only updates dynamic data (status, unread messages)
        3. If not found, fetches complete data from database
        
        Args:
            data: GetConsultant object containing username and language parameters
            
        Returns:
            JSON response with consultant details or error message
        """
        data = GetConsultant(**self.data)
        
        # Create cache key based on request parameters
        cache_key = f"consultant:{data.username}:{self.from_user.id}:{data.language_code}"
        
        # Try to get data from cache
        cached_data = await redis_client.get_json(cache_key)
        
        # If data exists in cache, only update dynamic information
        if cached_data:
            logger.info(f"Using cached consultant data for {data.username}")
            
            # Get consultant from database to update dynamic information
            consultant = self.db.session.query(Consultant).filter(
                Consultant.username == data.username,
                # Consultant.visible == True,
                Consultant.is_banned == False
            ).first()
            
            if not consultant:
                # If consultant is no longer available, remove from cache
                await redis_client.delete(cache_key)
                return {"error": f"Consultant with username {data.username} not found or is not available"}
            
            # Update status and call availability
            cached_data['availability_status'] = consultant.status
            cached_data['is_on_another_call'] = self.is_on_another_call(consultant)
            
            # Calculate unread message count with optimized query
            unread_count = 0
            if self.from_user.user_type != "Admin":
                # Start timing the optimized unread count calculation
                unread_start_time = datetime.datetime.now()
                
                # Get all rooms for this consultant in a single query
                consultant_rooms = self.db.session.query(Room).filter(
                    (Room.consultant_id == consultant.id) &
                    ((Room.client_id == self.from_user.id) | (Room.consultant_id == self.from_user.id))
                ).all()
                
                # Get all room IDs
                room_ids = [room.id for room in consultant_rooms]
                
                if room_ids:
                    # Use a single optimized query to get all unread messages
                    unread_count = self.db.session.query(func.count(Message.id)).filter(
                        Message.room_id.in_(room_ids),
                        Message.has_read == False,
                        Message.by_user_id != self.from_user.id
                    ).scalar()
                    
                    # Log performance
                    unread_time = (datetime.datetime.now() - unread_start_time).total_seconds()
                    logger.info(f"Optimized unread count for consultant {consultant.username}: {unread_count} messages in {len(room_ids)} rooms ({unread_time:.3f}s)")
            
            cached_data['unread_count'] = unread_count
            # Create ConsultantResponse object
            response = ConsultantResponse(**cached_data)
            
            # Add subscription information if consultant is AI-enabled
            if consultant.is_ai:
                subscription_info = ConsultantResponse.get_subscription_info(self.from_user.id, consultant.id)
                response.subscription_info = subscription_info
                
            # Return response
            return response.json()


        # If data is not in cache, fetch it from database
        logger.info(f"Fetching consultant data from database for {data.username}")
        
        # Define unread_message_alias for use in the method
        unread_message_alias = aliased(Message)
        
        # Get consultant with specified username
        consultant = self.db.session.query(Consultant).filter(
            Consultant.username == data.username,
            # Consultant.visible == True,
            Consultant.is_banned == False
        ).first()
        
        if not consultant:
            return {"error": f"Consultant with username {data.username} not found or is not available"}
        
        # Get consultant's average rating
        rate_data = (
            self.db.session.query(
                Rate.consultant,
                func.avg(Rate.rate).label('avg_rate')
            )
            .filter(Rate.consultant == consultant.id)
            .group_by(Rate.consultant)
            .first()
        )
        
        avg_rate = rate_data[1] if rate_data else 5.0
        
        # Check for unread messages
        # Calculate unread message count with optimized query
        unread_count = 0
        if self.from_user.user_type != "Admin":
            # Start timing the optimized unread count calculation
            unread_start_time = datetime.datetime.now()
            
            # Get all rooms for this consultant in a single query
            consultant_rooms = self.db.session.query(Room).filter(
                (Room.consultant_id == consultant.id) &
                ((Room.client_id == self.from_user.id) | (Room.consultant_id == self.from_user.id))
            ).all()
            
            # Get all room IDs
            room_ids = [room.id for room in consultant_rooms]
            
            if room_ids:
                # Use a single optimized query to get all unread messages
                unread_count = self.db.session.query(func.count(Message.id)).filter(
                    Message.room_id.in_(room_ids),
                    Message.has_read == False,
                    Message.by_user_id != self.from_user.id
                ).scalar()
                
                # Log performance
                unread_time = (datetime.datetime.now() - unread_start_time).total_seconds()
                logger.info(f"Optimized unread count for consultant {consultant.username}: {unread_count} messages in {len(room_ids)} rooms ({unread_time:.3f}s)")
        
        # Convert consultant information to dictionary
        user_language = data.language_code
        consultant_dict = consultant.to_dict(user_language)
        consultant_dict['estimate_time'] = ''
        consultant_dict['availability_status'] = consultant.status
        consultant_dict['unread_count'] = unread_count
        consultant_dict['is_on_another_call'] = self.is_on_another_call(consultant)
        consultant_dict['categories'] = [i.title for i in consultant.categories]
        consultant_dict['avg_rate'] = ceil(avg_rate)
        consultant_dict['order'] = consultant.order
        
        # Add preferred language for user if user type is User
        # _user = "Consultant" if self.from_user.user_type == "Consultant" else "User"
        # if _user == "User":
            # self.from_user.add_preferred_language(self.db.session, user_language)
        
        # Cache data with 3 minutes expiry (optimized for WebSocket real-time updates)
        await redis_client.set_json(cache_key, consultant_dict, expiry=180)
        # Create ConsultantResponse object
        response = ConsultantResponse(**consultant_dict)
        # Add subscription information if consultant is AI-enabled
        if consultant.is_ai:
            subscription_info = ConsultantResponse.get_subscription_info(self.from_user.id, consultant.id)
            response.subscription_info = subscription_info
        # Return response
        return response.json()
    
    
    async def get_rooms(self, data):
        subq = self.db.session.query(
            Room.id, func.max(Message.id).label('max_id')
        ).join(Message).group_by(Room.id).subquery()

        unread_user_type_filter = 'Consultant' if self.from_user.user_type == 'User' else 'User'

        base_query = self.db.session.query(
            Room,
            func.count(func.distinct(Message.id)).label('messages_count'),
            func.count(func.distinct(Message.id)).filter(
                Message.has_read == False,
                Message.by_user_type == unread_user_type_filter,
            ).label('unread_count'),
        ).filter(
            Room.messages.any()
        ).join(Message).filter(
            and_(Room.id == subq.c.id, Message.id == subq.c.max_id)
        ).order_by(Message.id.desc()).group_by(Room.id, Message.id)

        def to_response(rows):
            data = []
            for row, messages_count, unread_count in rows:
                if row.room_type == 'chat' and messages_count == 0:
                    continue

                data.append(
                    RoomsResponse(
                        **row.to_dict(),
                        unread_count=unread_count,
                        messages_count=messages_count,
                    )
                )
            return data

        if type(self.from_user) is Consultant:
            objects = base_query.filter(
                Room.consultant_id == self.from_user.id,
            )
            count = objects.count()
            per_page = 10
            paginated_objects = paginate(objects, page=data.page, per_page=per_page)
            return RoomListResponse(
                current_page=data.page,
                total_pages=ceil(count / per_page),
                count=count,
                per_page=per_page,
                results=to_response(paginated_objects)
            ).json()

        elif type(self.from_user) is Admin:
            if self.from_user.username == "<EMAIL>":
                base_query = base_query.filter(Room.consultant_id.in_([9, 12, 17, 16, 15, 3]))

            objects = base_query
            count = objects.count()
            per_page = 10
            paginated_objects = paginate(objects, page=data.page, per_page=per_page)
            return RoomListResponse(
                current_page=data.page,
                total_pages=ceil(count / per_page),
                count=count,
                per_page=per_page,
                results=to_response(paginated_objects)).json()
        else:
            objects = self.db.session.query(
                Room,
                func.count(func.distinct(Message.id)).label('messages_count'),
                func.count(func.distinct(Message.id)).filter(
                    Message.has_read == False,
                    Message.by_user_type == unread_user_type_filter,
                ).label('unread_count'),
            ).outerjoin(Message).filter(
                Room.client_id == self.from_user.id, Room.consultant == data.consultant
            ).group_by(Room.id)

            return RoomListResponse(
                results=to_response(objects)
            ).json(exclude={'count', 'per_page', 'total_pages', 'current_page'})

    async def start_room(self, data):
        data = StartRoom(**self.data)
        if type(self.from_user) is Consultant:
            return {
                'act': data.type,
                'status': False,
                'error': 'only regular users can start room'
            }

        # todays_room_count = self.db.session.query(Room).filter(
        #     Room.client_id == self.from_user.id,
        #     func.date(Room.created_at) == datetime.datetime.today().date(),
        # ).count()

        # if todays_room_count > 3:
        #     return {
        #         'act': data.type,
        #         'status': False,
        #         'error': 'you can only start 3 rooms a day',
        #     }

        obj = Room(
            client=self.from_user,
            consultant=data.consultant,
            status='o',
            room_type=data.room_type,
        )

        self.db.session.add(obj)
        self.db.session.commit()
        self.db.session.refresh(obj)

        json_data = RoomsResponse(**obj.to_dict()).json()

        await self.broadcast_to_users([obj.client, obj.consultant], json_data)

        # if obj.consultant.fcm:
        #     await self.send_notif(obj.consultant, {
        #         'act': 'call_request',
        #     })

        if not self.user_connections.get(obj.consultant.username):
            await self.send_notif(obj.consultant, {
                'content': f'room:{obj.id}',
            })
            # return {
            #     'act': 'startRoom',
            #     'error': 'room created but consultant is offline'
            # }

    async def get_categories(self, data):    
        accept_language = self.connection.headers.get("accept-language", "en").split(",")[0][:2]
        topics = self.db.session.query(Topic).all()
        topics_list = []
        for topic in topics:
            translated_title = topic.get_title_for_language(accept_language)
            topics_list.append(translated_title)

        return {
            'act': 'getCategories',
            'results': topics_list,
        }

    async def update_message(self, data):
        # Check if user is admin - admins can update any message
        if self.from_user.user_type == 'Admin':
            # Admin can update any message by message_id only
            query = self.db.session.query(Message).filter(Message.id == data['message_id'])
        else:
            # Regular users can only update their own messages
            query = self.db.session.query(Message).filter(
                Message.by_user_type == self.from_user.user_type,
                Message.by_user_id == self.from_user.id,
                Message.id == data['message_id']
            )

        # Perform the update
        updated_rows = query.update({
            Message.content: data['text'],
            Message.edited_at: datetime.datetime.now(),
        })

        # Check if any rows were updated
        if updated_rows == 0:
            self.db.session.rollback()
            return {
                'act': 'updateMessage',
                'error': f'Message not found or permission denied: {data["message_id"]}',
                'message_id': data['message_id']
            }

        self.db.session.commit()

        # Fetch the updated message
        updated_message = self.db.session.query(Message).filter(Message.id == data['message_id']).first()

        if not updated_message:
            return {
                'act': 'updateMessage',
                'error': f'Message not found after update: {data["message_id"]}',
                'message_id': data['message_id']
            }

        msg_dict = updated_message.to_dict()
        msg_dict['date'] = str(msg_dict['date'])
        msg_dict['room_id'] = str(msg_dict['room_id'])  # Ensure room_id is string

        resp = MessageModel(
            **msg_dict
        )
        return resp.json()

    async def delete_message(self, data):
        # Check if user is admin - admins can delete any message
        if self.from_user.user_type == 'Admin':
            # Admin can delete any message by message_id only
            obj = self.db.session.query(Message).filter(Message.id == data['message_id']).first()
        else:
            # Regular users can only delete their own messages
            obj = self.db.session.query(Message).filter(
                Message.by_user_type == self.from_user.user_type,
                Message.by_user_id == self.from_user.id,
                Message.id == data['message_id'],
            ).first()

        if not obj:
            return {
                'act': 'deleteMessage',
                'error': f'Message not found or permission denied: {data["message_id"]}',
                'message_id': data['message_id']
            }

        await self.broadcast_to_users([obj.room.client, obj.room.consultant], {
            'act': 'deleteMessage',
            'message_id': data['message_id'],
        })

        self.db.session.delete(obj)
        self.db.session.commit()

        return {
            'act': 'deleteMessage',
            'message_id': data['message_id'],
            'success': True
        }


        
    async def purchase_subscription(self, data):
        """
        Purchase a subscription for the current user
        
        Args:
            data (dict): Contains subscription_id
            
        Returns:
            dict: Result of the purchase operation
        """
        from services.subscription_service import SubscriptionService
        
        subscription_id = data.get('subscription_id')
        if not subscription_id:
            return {
                'action': 'purchaseSubscription',
                'status': 'error',
                'code': 'missing_subscription_id',
                'message': 'Subscription ID is required'
            }
        
        # Use the subscription service to process the purchase
        result = SubscriptionService.process_subscription_purchase(self.from_user.id, subscription_id)
        
        # Add the action name to the result
        result['action'] = 'purchaseSubscription'
        
        return result

    async def subscription_info(self, data):
        """
        Returns subscription information including:
        1. The first active subscription plan from the Subscription model
        2. Whether the current user has an active subscription
        3. How many days the subscription was purchased for
        4. How many days remain in the subscription
        
        Returns:
            dict: The subscription information or an error message if none found
        """
        from services.subscription_service import SubscriptionService
        
        try:
            # Add the action name to the result
            # Get subscription information from the service
            result = SubscriptionService.get_subscription_info(self.from_user.id)
            result['act'] = 'subscriptionInfo'
            
            
            # Handle error case
            if result.get('status') == 'error':
                return {
                    'act': 'subscriptionInfo',
                    'error': result.get('message', 'Error retrieving subscription information'),
                }
                
            return result
            
        except Exception as e:
            logger.error(f"Error in subscription_info: {str(e)}")
            return {
                'act': 'subscriptionInfo',
                'error': f'Error retrieving subscription information: {str(e)}',
            }

    async def get_reservation_slots(self, data):
        """
        دریافت بازه‌های زمانی قابل رزرو برای یک مشاور
        
        این متد بازه‌های زمانی قابل رزرو برای دو روز آینده را بر اساس برنامه زمانی مشاور محاسبه می‌کند.
        هر بازه زمانی بر اساس مدت زمان جلسه (session_duration) مشاور تقسیم‌بندی می‌شود.
        
        Args:
            data: GetReservationSlots object containing consultant_id and timezone parameters
            
        Returns:
            JSON response with available reservation slots for the next two days
        """
        try:
            # پارس کردن داده‌های ورودی
            data = GetReservationSlots(**self.data)
            
            # دریافت اطلاعات مشاور
            consultant = self.db.session.query(Consultant).filter(
                Consultant.username == data.consultant,
                Consultant.is_banned == False
            ).first()
            
            if not consultant:
                return {
                    'act': 'getReservationSlots',
                    'error': f'Consultant with username {data.consultant} not found or is not available'
                }
            
            # استفاده از سرویس رزرواسیون برای محاسبه بازه‌های زمانی قابل رزرو
            from services.reservation_service import ReservationService
            reservation_slots = ReservationService.get_available_slots(consultant.id, data.timezone)
            
            if "error" in reservation_slots:
                return {
                    'act': 'getReservationSlots',
                    'error': reservation_slots["error"]
                }
            
            # تبدیل داده‌ها به فرمت مورد نظر
            formatted_slots = []
            
            for date_str, slots in reservation_slots.items():
                if not slots:  # اگر اسلاتی برای این روز وجود نداشت، آن را نادیده بگیر
                    continue
                    
                # تبدیل تاریخ به فرمت مورد نظر
                date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d")
                month_name = calendar.month_name[date_obj.month]
                day_name = calendar.day_name[date_obj.weekday()]
                
                formatted_date = {
                    "date": date_str,
                    "day": date_obj.day,
                    "month": month_name,
                    "weekday": day_name,
                    "slots": slots
                }
                
                formatted_slots.append(formatted_date)
            
            # برگرداندن نتیجه
            return {
                'act': 'getReservationSlots',
                'username': consultant.username,
                'fullname': consultant.fullname,
                'session_duration': consultant.session_duration,
                'dates': formatted_slots
            }
            
        except Exception as e:
            logger.error(f"Error in get_reservation_slots: {str(e)}")
            return {
                'act': 'getReservationSlots',
                'error': f'Error retrieving reservation slots: {str(e)}',
            }
            
    async def reserve_call_slot(self, data):
        """
        رزرو یک بازه زمانی برای تماس با مشاور
        
        این متد یک بازه زمانی را برای تماس با مشاور رزرو می‌کند و پرداخت را انجام می‌دهد.
        
        Args:
            data: ReserveCallSlot object containing reservation details
            
        Returns:
            JSON response with reservation details or error message
        """
        try:
            # پارس کردن داده‌های ورودی
            data = ReserveCallSlot(**self.data)
            
            # دریافت اطلاعات مشاور
            consultant = self.db.session.query(Consultant).filter(
                Consultant.username == data.consultant,
                Consultant.is_banned == False
            ).first()
            
            if not consultant:
                return {
                    'act': 'reserveCallSlot',
                    'status': 'error',
                    'code': 'consultant_not_found',
                    'message': f'Consultant with username {data.consultant} not found or is not available'
                }
            
            # ساخت datetime برای زمان شروع و پایان
            start_datetime_str = f"{data.date} {data.start_time}"
            end_datetime_str = f"{data.date} {data.end_time}"
            
            start_datetime = datetime.datetime.strptime(start_datetime_str, "%Y-%m-%d %H:%M")
            end_datetime = datetime.datetime.strptime(end_datetime_str, "%Y-%m-%d %H:%M")
            
            # تعیین هزینه تماس بر اساس نوع تماس
            cost = 0
            if data.call_type == 'voice':
                cost = consultant.voice_call_cost or 0
            elif data.call_type == 'video':
                cost = consultant.video_call_cost or 0
                
            if cost <= 0:
                return {
                    'act': 'reserveCallSlot',
                    'status': 'error',
                    'code': 'invalid_cost',
                    'message': 'Call cost is not defined for this consultant'
                }
            
            # استفاده از سرویس رزرواسیون برای پردازش پرداخت و ایجاد رزرو
            from services.reservation_service import ReservationService
            
            # تنظیم منطقه زمانی کاربر (اگر ارسال شده باشد)
            client_timezone = data.timezone if hasattr(data, 'timezone') else 'UTC'
            
            # پردازش کامل رزرو تماس
            result = ReservationService.process_call_reservation(
                client_id=self.from_user.id,
                consultant_id=consultant.id,
                start_time=start_datetime,
                end_time=end_datetime,
                call_type=data.call_type,
                cost=cost,
                client_timezone=client_timezone
            )
            
            # اضافه کردن نام عملیات به نتیجه
            result['act'] = 'reserveCallSlot'
            
            # اضافه کردن اطلاعات تاریخ و زمان به فرمت خوانا
            if result['status'] == 'success':
                result['date'] = data.date
                result['start_time'] = data.start_time
                result['end_time'] = data.end_time
            
            return result
            
        except Exception as e:
            logger.error(f"Error in reserve_call_slot: {str(e)}")
            return {
                'act': 'reserveCallSlot',
                'status': 'error',
                'code': 'unexpected_error',
                'message': f'Error reserving call slot: {str(e)}',
            }
            
    async def get_user_reservations(self, data):
        """
        دریافت لیست رزروهای کاربر
        
        این متد لیست تمام رزروهای کاربر را برمی‌گرداند.
        
        Args:
            data: GetUserReservations object
            
        Returns:
            JSON response with user's reservations
        """
        try:
            # پارس کردن داده‌های ورودی
            data = GetUserReservations(**self.data)
            
            # استفاده از سرویس رزرواسیون برای دریافت رزروهای کاربر
            from services.reservation_service import ReservationService
            reservations_data = ReservationService.get_user_reservations(self.from_user.id)
            
            # تبدیل به فرمت مورد نظر
            formatted_reservations = []
            for reservation in reservations_data:
                # تبدیل تاریخ و زمان به فرمت مورد نظر
                date_parts = reservation['start_time'].split(' ')[0]
                start_time_parts = reservation['start_time'].split(' ')[1]
                end_time_parts = reservation['end_time'].split(' ')[1] if reservation['end_time'] else None
                
                formatted_reservations.append({
                    'reservation_id': reservation['id'],
                    'consultant_id': reservation['consultant_id'],
                    'consultant_name': reservation['consultant_name'],
                    'consultant_username': reservation['consultant_username'],
                    'date': date_parts,
                    'start_time': start_time_parts,
                    'end_time': end_time_parts,
                    'call_type': reservation['call_type'],
                    'cost': reservation['cost'],
                    'status': reservation['status'],
                    'duration': reservation.get('duration')
                })
            
            # برگرداندن نتیجه
            return {
                'act': 'getUserReservations',
                'reservations': formatted_reservations
            }
            
        except Exception as e:
            logger.error(f"Error in get_user_reservations: {str(e)}")
            return {
                'act': 'getUserReservations',
                'status': 'error',
                'code': 'unexpected_error',
                'message': f'Error retrieving user reservations: {str(e)}',
            }
            
    async def get_consultant_reservations(self, data):
        """
        دریافت لیست رزروهای مشاور
        
        این متد لیست تمام رزروهای مشاور را برمی‌گرداند.
        فیلتر تاریخ اختیاری است و می‌تواند یکی از مقادیر زیر باشد:
        - week: هفته اخیر
        - month: ماه اخیر
        - three_months: سه ماه اخیر
        - six_months: شش ماه اخیر
        - year: یک سال اخیر
        
        Args:
            data: GetConsultantReservations object
            
        Returns:
            JSON response with consultant's reservations
        """
        try:
            # پارس کردن داده‌های ورودی
            data = GetConsultantReservations(**self.data)
            
            # بررسی اینکه آیا کاربر مشاور است
            consultant = self.db.session.query(Consultant).filter(
                Consultant.username == self.from_user.username
            ).first()
            
            if not consultant:
                return {
                    'act': 'getConsultantReservations',
                    'status': 'error',
                    'code': 'not_consultant',
                    'message': 'You are not a consultant'
                }
            
            # استفاده از سرویس رزرواسیون برای دریافت رزروهای مشاور
            from services.reservation_service import ReservationService
            reservations_data = ReservationService.get_consultant_reservations(consultant.id, data.date_filter)
            
            # تبدیل به فرمت مورد نظر
            formatted_reservations = []
            for reservation in reservations_data:
                # تبدیل تاریخ و زمان به فرمت مورد نظر
                date_parts = reservation['start_time'].split(' ')[0]
                start_time_parts = reservation['start_time'].split(' ')[1]
                end_time_parts = reservation['end_time'].split(' ')[1] if reservation['end_time'] else None
                
                formatted_reservations.append({
                    'reservation_id': reservation['id'],
                    'client_id': reservation['client_id'],
                    'client_name': reservation['client_name'],
                    'client_avatar': reservation['client_avatar'],
                    'date': date_parts,
                    'start_time': start_time_parts,
                    'end_time': end_time_parts,
                    'call_type': reservation['call_type'],
                    'cost': reservation['cost'],
                    'status': reservation['status'],
                    'duration': reservation.get('duration')
                })
            
            # برگرداندن نتیجه
            return {
                'act': 'getConsultantReservations',
                'date_filter': data.date_filter,
                'reservations': formatted_reservations
            }
            
        except Exception as e:
            logger.error(f"Error in get_consultant_reservations: {str(e)}")
            return {
                'act': 'getConsultantReservations',
                'status': 'error',
                'code': 'unexpected_error',
                'message': f'Error retrieving consultant reservations: {str(e)}',
            }
            
    async def add_preferred_language(self, data):
        """
        Set a user's preferred language.
        
        Args:
            data: AddPreferredLanguage object containing the language code
            
        Returns:
            JSON response with status "ok"
        """
        try:
            data = AddPreferredLanguage(**self.data)
            
            # Use the existing method to add preferred language
            print(f'Adding preferred language: {data.language_code}')
            self.from_user.add_preferred_language(self.db.session, data.language_code)
            
            return {
                "act": "addPreferredLanguage",
                "status": "ok"
            }
            
        except Exception as e:
            return {
                "act": "addPreferredLanguage",
                "status": "error",
                "message": str(e)
            }
