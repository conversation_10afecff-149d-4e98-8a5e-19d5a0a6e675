#!/usr/bin/env python
# chmod +x test_bot.py
import os
import sys
import logging
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("telegram_bot_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import the functions from the bot module
try:
    from bot import (
        TELEGRAM_BOT_TOKEN, 
        TELEGRAM_CHANNEL_CONFIG, 
        test_bot_connection, 
        check_webhook_status, 
        remove_webhook,
        send_message_with_button
    )
    
    # Test bot connection
    logger.info("=== Testing Bot Connection ===")
    if test_bot_connection():
        logger.info("✅ Bot connection test passed!")
    else:
        logger.error("❌ Bot connection test failed!")
        sys.exit(1)
    
    # Check webhook status
    logger.info("\n=== Checking Webhook Status ===")
    webhook_info = check_webhook_status()
    if webhook_info and webhook_info.get('url'):
        logger.warning(f"⚠️ A webhook is currently set to: {webhook_info.get('url')}")
        logger.warning("This will prevent polling from working correctly.")
        
        # Ask if user wants to remove the webhook
        response = input("Do you want to remove the webhook? (y/n): ")
        if response.lower() == 'y':
            if remove_webhook():
                logger.info("✅ Webhook successfully removed!")
            else:
                logger.error("❌ Failed to remove webhook!")
    else:
        logger.info("✅ No webhook is set. Good for polling.")
    
    # Test sending a message with button
    logger.info("\n=== Testing Send Message with Button ===")
    for time_filter, channel_id in TELEGRAM_CHANNEL_CONFIG.items():
        if channel_id:
            logger.info(f"Sending test message with button to channel for {time_filter}...")
            result = send_message_with_button(time_filter)
            if result and result.get('ok'):
                logger.info(f"✅ Message with button sent successfully to channel for {time_filter}!")
            else:
                logger.error(f"❌ Failed to send message with button to channel for {time_filter}!")
    
    logger.info("\n=== All Tests Completed ===")
    logger.info("If all tests passed, try restarting the bot with 'python run_bot.py'")
    logger.info("Check the logs at 'telegram_bot.log' for more details.")
    
except Exception as e:
    logger.error(f"Error running tests: {e}")
    import traceback
    logger.error(traceback.format_exc())
    sys.exit(1)