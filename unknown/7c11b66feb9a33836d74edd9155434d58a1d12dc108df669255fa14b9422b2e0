"""add timer_active to calls

Revision ID: c200581eb961
Revises: 1d2402b1fe0a
Create Date: 2024-06-08 19:56:28.413147

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c200581eb961'
down_revision: Union[str, None] = '1d2402b1fe0a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('calls', sa.Column('timer_active', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('calls', 'timer_active')
    # ### end Alembic commands ###
