"""update status field

Revision ID: be99d52e7660
Revises: 3b361775e3f9
Create Date: 2024-06-24 13:50:50.944094

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'be99d52e7660'
down_revision: Union[str, None] = '3b361775e3f9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('consultants', sa.Column('status', sa.String(), nullable=True))
    op.execute('UPDATE consultants SET status = \'offline\' WHERE status IS NULL')
    op.alter_column('consultants', 'status', nullable=False)
    # ### end Alembic commands ###

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('consultants', 'status', nullable=True)
    op.drop_column('consultants', 'status')
    # ### end Alembic commands ###