import os
import requests
import logging

# تنظیمات پایه‌ای برای logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ApiConsumer:
    session = None
    producer_url = "https://habibapp.com/api/chat-producer/"
    timeout = 3  # Timeout in seconds

    def __init__(self):
        admin_token = os.environ.get('admin_token') or 'e9a236c586d4fb90f7f7ce2c70392d80069022d2'
        session = requests.Session()
        session.headers = {
            'Content-Type': 'application/json',
            'AUTHORIZATION': f'Token {admin_token}',
        }
        self.session = session

    def request(self, data):
        try:
            resp = self.session.post(self.producer_url, json=data, headers={
                'user-agent': 'dart:io'
            }, timeout=self.timeout)
            if resp.status_code == 200:
                return resp.json()
            else:
                logging.error(f'auth failed: {resp.status_code} - {resp.text}')
                return {
                    'result': False,
                    'error': 'Invalid token'
                }
        except requests.Timeout:
            logging.warning('Request timed out after 3 seconds')
            return None  

    def get_user(self, token, email=None):
        logging.debug(f"Making API request to get user data. Token: {token[:8]}..., Email: {email}")
        user = self.request({
            'action': 'get_user',
            'token': token,
            'email': email,
        })

        if user is None or user.get('result') is False:
            logging.warning(f'Failed to get user data for token: {token[:8]}...')
            return None

        logging.info('User data retrieved successfully')
        return user


if __name__ == '__main__':
    r = ApiConsumer().get_user(
        token="e9a236c586d4fb90f7f7ce2c70392d80069022d2"
    )
    logging.info(f'User data: {r}')