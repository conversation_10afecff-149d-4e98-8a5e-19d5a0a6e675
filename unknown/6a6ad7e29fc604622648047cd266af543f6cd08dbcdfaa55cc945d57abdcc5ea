import logging
from colorlog import ColoredFormatter
import functools

# Define custom log level 'TRACK'
TRACK_LEVEL_NUM = 9  # Must be less than logging.DEBUG (10)
logging.addLevelName(TRACK_LEVEL_NUM, "TRACK")

def track(self, message, *args, **kwargs):
    if self.isEnabledFor(TRACK_LEVEL_NUM):
        self._log(TRACK_LEVEL_NUM, message, args, **kwargs)

logging.Logger.track = track
logging.track = track

# Configure colorlog
color_formatter = ColoredFormatter(
    # "%(log_color)s%(levelname)-8s%(reset)s %(message)s",
    "%(log_color)s%(asctime)s - %(levelname)s-%(message)s",
    datefmt='%H:%M:%S',
    reset=True,
    log_colors={
        'TRACK': 'green',
        'DEBUG': 'cyan',
        'INFO': 'white',
        'WARNING': 'yellow',
        'ERROR': 'red',
        'CRITICAL': 'bold_red',
    },
)

handler = logging.StreamHandler()
handler.setFormatter(color_formatter)
handler.setLevel(TRACK_LEVEL_NUM)

# Set up the root logger
logger = logging.getLogger()
logger.handlers = []  # Remove existing handlers
logger.addHandler(handler)
logger.setLevel(TRACK_LEVEL_NUM)




def log_message(event, message, data = '', level:int = 9):
    if level == 9:
        event = f"\033[34;1m{event}\033[0m"  
        message = f"\033[32m{message}\033[0m"
        data = f"\033[35m{data}\033[0m" if data != '' else None
        data = f"data: {data}" if data else ''
        e = "\033[39;1m'-------------------------------------'\033[0m"
        
    logger.log(level, f"((event-->{event}//{data} ===  {message}  )))")
    logger.log(23, e)
    

import json
from datetime import datetime, timedelta, timezone


# تنظیمات
TELEGRAM_BOT_TOKEN = '**********************************************'
CHANNEL_USERNAME = -1002497532429
HISTORY_CHANNEL_ID = -1002614564198  # New channel ID for chat history
import re
import pytz  # برای مدیریت مناطق زمانی

def escape_markdown_v2(text):
    """Escape کاراکترهای خاص فقط در صورت نیاز"""
    escape_chars = r'_*[]()~`>#+-=|{}.!'
    return re.sub(
        r'(?<![\\])[' + re.escape(escape_chars) + r']', 
        r'\\\g<0>', 
        str(text)
    )
    
def normalize_hashtag(text):
    """تبدیل متن به فرمت مناسب برای هشتگ"""
    # حذف کاراکترهای غیرمجاز
    cleaned = re.sub(r'[^\wآ-یa-zA-Z0-9_]', '_', str(text))
    # حذف آندراسکورهای تکراری
    cleaned = re.sub(r'_+', '_', cleaned)
    # حذف آندراسکور از ابتدا و انتها
    return cleaned.strip('_')


def format_telegram_message(
    user_fullname: str,
    language: str,
    consultant_fullname: str,
    message_text: str,
    created_at: str,
    room_id: int,
):
    dt = datetime.fromisoformat(created_at)
    # اگر تاریخ بدون منطقه زمانی باشد، به UTC تبدیل کن
    if dt.tzinfo is None:
        dt = pytz.utc.localize(dt)  # اضافه کردن UTC
    
    # تبدیل به منطقه زمانی موردنظر (مثال: تهران)
    tehran_tz = pytz.timezone('Asia/Tehran')
    dt = dt.astimezone(tehran_tz)
    
    formatted_time = dt.strftime("%m/%d/%Y ∙ %H:%M")
        
    # Escape all text fields
    user_fullname = escape_markdown_v2(user_fullname)
    message_text = escape_markdown_v2(message_text.strip())  # حذف فضای خالی
    consultant_escaped = normalize_hashtag(consultant_fullname)
    language_escaped = normalize_hashtag(language)
    
    # Create hashtags
    consultant_hashtag = (
        consultant_escaped
        .replace(" ", "_")
        .replace(".", "_")
        .replace("(", "")   # حذف پرانتز باز
        .replace(")", "")   # حذف پرانتز بسته
        .replace("?", "")   # حذف علامت سوال
        .replace("-", "_")  # جایگزینی خط تیره با آندرلاین
    )
    
    language_hashtag = (
        language_escaped
        .replace(" ", "_")
        .replace(".", "_")
        .replace("-", "_")
    )
    language_tag = f"\\#{language_hashtag}"
        
    consultant_fullname_tag = f"\\#{consultant_hashtag}"
    
    consultant_clean = re.sub(r'[^\wآ-یa-zA-Z0-9]', '_', consultant_fullname)
    consultant_clean = re.sub(r'_+', '_', consultant_clean).strip('_')
    language_clean = re.sub(r'[^\wآ-یa-zA-Z0-9]', '_', language)
    language_clean = re.sub(r'_+', '_', language_clean).strip('_')
    language_tag = f"\\#{language_hashtag}"
        
    consultant_fullname_tag = f"\\#{consultant_hashtag}"
    
    link_url = f"https://talk\\.habibapp\\.com/dashboard/chat/{room_id}/"
    # escaped_link_url = escape_markdown_v2(link_url)
    
    formatted_msg = (
        f"{consultant_fullname_tag}\n"
        f"{language_tag}\n"
        f"📅 *Date:* {formatted_time}\n"
        f"📝 *Message:*\n"
        f"*{message_text.strip()}*\n\n" 
        f"🔗 [View Conversation in Talk Panel]({link_url})"
    )
    if formatted_msg.count('_') % 2 != 0:
        formatted_msg = formatted_msg.replace('_', r'\_')
        
    print("Formatted Message:", formatted_msg)
    return formatted_msg


def format_telegram_chat_history(
    messages: list,
    room_id: int,
    client_fullname: str,
    consultant_fullname: str,
    language: str
):
    """
    Format the entire chat history for Telegram with clear distinction between questions (user) and answers (consultant)
    
    Args:
        messages: List of message objects
        room_id: Room ID
        client_fullname: Client's full name
        consultant_fullname: Consultant's full name
        language: Language code
    
    Returns:
        Formatted message string for Telegram
    """
    # Create hashtags
    consultant_escaped = normalize_hashtag(consultant_fullname)
    language_escaped = normalize_hashtag(language)
    
    consultant_hashtag = (
        consultant_escaped
        .replace(" ", "_")
        .replace(".", "_")
        .replace("(", "")
        .replace(")", "")
        .replace("?", "")
        .replace("-", "_")
    )
    
    language_hashtag = (
        language_escaped
        .replace(" ", "_")
        .replace(".", "_")
        .replace("-", "_")
    )
    
    # Create tags
    language_tag = f"\\#{language_hashtag}"
    consultant_tag = f"\\#{consultant_hashtag}"
    
    # Create link to conversation
    link_url = f"https://talk\\.habibapp\\.com/dashboard/chat/{room_id}/"
    
    # Start building the message
    formatted_msg = (
        f"{consultant_tag}\n"
        f"{language_tag}\n"
        f"💬 *Conversation History*\n\n"
    )
    
    # Add each message to the history
    for i, msg in enumerate(messages):
        # Convert timestamp to Tehran timezone
        dt = datetime.fromisoformat(str(msg.get('date')))
        if dt.tzinfo is None:
            dt = pytz.utc.localize(dt)
        tehran_tz = pytz.timezone('Asia/Tehran')
        dt = dt.astimezone(tehran_tz)
        formatted_time = dt.strftime("%H:%M")
        
        # Get message content and escape markdown characters
        content = escape_markdown_v2(msg.get('content', '').strip())
        mime_type = msg.get('mime_type', 'txt')
        
        # Determine if message is from user (question) or consultant (answer)
        is_user_message = not msg.get('by_user', {}).get('is_consultant', False)
        
        if is_user_message:
            # Format user message (question)
            if mime_type == 'audio':
                formatted_msg += (
                    f"❓ *Question*:\n"
                    f"🎤 *Voice Message*\n\n"
                )
            else:
                formatted_msg += (
                    f"❓ *Question*:\n"
                    f"{content}\n\n"
                )
        else:
            # Format consultant message (answer)
            if mime_type == 'audio':
                formatted_msg += (
                    f"✅ *Answer* :\n"
                    f"🎤 *Voice Message*\n\n"
                )
            else:
                formatted_msg += (
                    f"✅ *Answer* :\n"
                    f"{content}\n\n"
                )
    
    # Add link to conversation at the end
    formatted_msg += f"🔗 [View Full Conversation in Talk Panel]({link_url})"
    
    # Fix uneven underscores if needed
    if formatted_msg.count('_') % 2 != 0:
        formatted_msg = formatted_msg.replace('_', r'\_')
    
    return formatted_msg

import aiohttp
import requests

async def send_telegram_async(text_format, history=False):
    """
    Send a message to a Telegram channel
    
    Args:
        text_format: The formatted message text
        channel_id: Optional channel ID to send to. If None, uses the default channel.
    """
    try:
        # Use the provided channel_id or fall back to the default
        target_channel = HISTORY_CHANNEL_ID if history else CHANNEL_USERNAME
        print(f"Sending message to channel: {target_channel}")
        response = requests.get(
            f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage',
            params={
                'chat_id': target_channel,
                'text': text_format,
                'parse_mode': 'MarkdownV2',
                'disable_web_page_preview': True
            }
        )
        if response.status_code != 200:
            print(f"Error sending tel message: {response.json()['description']}")
    
    except Exception as e:
        print(f"Error sending tel message: {str(e)}")
        pass

    # params = {
    #     'chat_id': str(CHANNEL_USERNAME),
    #     'text': str(text_format),
    #     'parse_mode': 'MarkdownV2',
    #     'disable_web_page_preview': True
    # }
    
    # async with aiohttp.ClientSession() as session:
    #     try:
    #         async with session.get(url, params=params) as response:
    #             if response.status != 200:
    #                 error_text = await response.text()
    #                 print(f"Telegram API Error: {error_text}")
    #     except Exception as e:
    #         print(f"Telegram Connection Error: {str(e)}")