#! python

import click
from passlib.context import CryptContext

from models import Consultant, SessionLocal

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


@click.option('--username', help='username')
@click.option('--password', help='password')
@click.option('--topics', help='topics with Comma Separate eg. ')
@click.command()
def create_consultant(username, password, topics: str):
    with SessionLocal() as session:
        if session.query(Consultant).filter(Consultant.username == username).first():
            print(f"Consultant with username {username} already exists")
            return

    cons = Consultant(
        username=username,
        password=pwd_context.hash(password),
        topics=topics,
    )
    session.add(cons)
    session.commit()
    session.refresh(cons)

    print('Token: ', cons.token)


if __name__ == '__main__':
    create_consultant()
