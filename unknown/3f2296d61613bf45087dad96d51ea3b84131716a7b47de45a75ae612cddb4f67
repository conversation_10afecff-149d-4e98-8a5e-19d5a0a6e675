import datetime

from sqlalchemy import Column, String, DateTime, Integer, Foreign<PERSON>ey, <PERSON>olean, Float
from sqlalchemy.orm import relationship

from models import Base
from fastapi_sqlalchemy import db


class Subscription(Base):
    """
    Model for defining subscription plans
    """
    __tablename__ = "subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    
    # Subscription details
    coins_required = Column(Integer, nullable=False)  # Number of coins needed to purchase (handled by external wallet service)
    duration_days = Column(Integer, nullable=False)   # Duration in days
    
    # Optional limits
    max_messages = Column(Integer, nullable=True)     # Max messages allowed (null means unlimited)
    
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    user_subscriptions = relationship("UserSubscription", back_populates="subscription")
    
    def to_dict(self):
        # Convert coins to dollars (50 coins = 5 dollars, so rate is 0.1)
        dollars_value = self.coins_required * 0.1
        
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'coins_required': self.coins_required,
            'dollars_value': dollars_value,  # New field with dollar conversion
            'duration_days': self.duration_days,
            'max_messages': self.max_messages,
            'is_active': self.is_active,
            'created_at': str(self.created_at),
            'updated_at': str(self.updated_at)
        }


class UserSubscription(Base):
    """
    Model for tracking user's active subscriptions
    """
    __tablename__ = "user_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    subscription_id = Column(Integer, ForeignKey("subscriptions.id"), nullable=False)
    
    # Subscription status
    start_date = Column(DateTime, default=datetime.datetime.now)
    end_date = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)
    
    # Message tracking if subscription has message limits
    messages_used = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    # Relationships
    user = relationship("User", backref="subscriptions")
    subscription = relationship("Subscription", back_populates="user_subscriptions")
    
    def is_expired(self):
        """Check if subscription has expired"""
        return datetime.datetime.now() > self.end_date
    
    def has_reached_message_limit(self):
        """Check if user has reached message limit"""
        if not self.subscription.max_messages:
            return False  # No limit
        return self.messages_used >= self.subscription.max_messages
    
    def is_valid(self):
        """Check if subscription is valid (not expired and within message limits)"""
        return self.is_active and not self.is_expired() and not self.has_reached_message_limit()
    
    def increment_message_count(self):
        """Increment the message count for this subscription"""
        self.messages_used += 1
        self.updated_at = datetime.datetime.now()
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'subscription': self.subscription.to_dict(),
            'start_date': str(self.start_date),
            'end_date': str(self.end_date),
            'is_active': self.is_active,
            'messages_used': self.messages_used,
            'is_expired': self.is_expired(),
            'has_reached_limit': self.has_reached_message_limit(),
            'is_valid': self.is_valid()
        }



class UserConsultantInteraction(Base):
    """
    Model for tracking interactions between users and consultants
    """
    __tablename__ = "user_consultant_interactions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    consultant_id = Column(Integer, ForeignKey("consultants.id"), nullable=False)
    
    # Interaction tracking
    first_interaction_date = Column(DateTime, default=datetime.datetime.now)
    last_interaction_date = Column(DateTime, default=datetime.datetime.now)
    message_count = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    # Relationships
    user = relationship("User", backref="consultant_interactions")
    consultant = relationship("Consultant", backref="user_interactions")
    
    def increment_message_count(self):
        """Increment the message count for this interaction"""
        self.message_count += 1
        self.last_interaction_date = datetime.datetime.now()
        self.updated_at = datetime.datetime.now()
    
    # This method is kept for backward compatibility but always returns True
    def is_within_free_days(self):
        """Legacy method that always returns True as we no longer use free_days"""
        return True
    
    def get_messages_in_period(self):
        """
        Get number of messages sent in the current period (daily, weekly, monthly)
        based on consultant's free_limit_period setting
        """
        now = datetime.datetime.now()
        period = self.consultant.free_limit_period
        
        if period == 'daily':
            # Check messages sent today
            today = now.date()
            if self.last_interaction_date.date() == today:
                return self.message_count
            return 0
            
        elif period == 'weekly':
            # Check messages sent this week
            # Get the start of the week (Monday)
            start_of_week = now - datetime.timedelta(days=now.weekday())
            start_of_week = datetime.datetime.combine(start_of_week.date(), datetime.time.min)
            
            if self.last_interaction_date >= start_of_week:
                return self.message_count
            return 0
            
        elif period == 'monthly':
            # Check messages sent this month
            start_of_month = datetime.datetime(now.year, now.month, 1)
            
            if self.last_interaction_date >= start_of_month:
                return self.message_count
            return 0
            
        # Default fallback to daily if period is invalid
        today = now.date()
        if self.last_interaction_date.date() == today:
            return self.message_count
        return 0
    
    def is_within_free_messages_limit(self):
        """Check if interaction is within free messages limit for the current period"""
        if not self.consultant.is_ai or not self.consultant.free_messages_per_period:
            return False
            
        # Check if within free messages for the period
        messages_in_period = self.get_messages_in_period()
        return messages_in_period < self.consultant.free_messages_per_period
        
    # Keep this method for backward compatibility
    def get_messages_today(self):
        """Get number of messages sent today (legacy method)"""
        today = datetime.datetime.now().date()
        if self.last_interaction_date.date() == today:
            return self.message_count
        return 0
    
    # Keep this method for backward compatibility
    def is_within_free_messages_per_day(self):
        """Check if interaction is within free messages per day limit (legacy method)"""
        return self.is_within_free_messages_limit()
    
    def can_send_message(self, user_subscription=None):
        """
        Check if user can send message to consultant
        
        Args:
            user_subscription: Optional active subscription to check
            
        Returns:
            tuple: (bool, str) - (can_send, reason)
        """
        # If consultant is not AI, always allow messages
        if not self.consultant.is_ai:
            return True, "Not an AI consultant"
            
        # Check if within free messages limit for the period
        if self.is_within_free_messages_limit():
            period_text = {
                'daily': 'day',
                'weekly': 'week',
                'monthly': 'month'
            }.get(self.consultant.free_limit_period, 'period')
            
            return True, f"Within free message limit ({self.consultant.free_messages_per_period} messages per {period_text})"
        else:
            period_text = {
                'daily': 'daily',
                'weekly': 'weekly',
                'monthly': 'monthly'
            }.get(self.consultant.free_limit_period, 'period')
            
            return False, f"{period_text.capitalize()} message limit reached ({self.consultant.free_messages_per_period} messages per {period_text})"
        
        # Check if user has valid subscription
        if user_subscription and user_subscription.is_valid():
            return True, "Active subscription"
            
        return False, "Free message limit reached and no active subscription"
    
    def to_dict(self):
        can_send, reason = self.can_send_message()
        return {
            'id': self.id,
            'user_id': self.user_id,
            'consultant_id': self.consultant_id,
            'first_interaction_date': str(self.first_interaction_date),
            'last_interaction_date': str(self.last_interaction_date),
            'message_count': self.message_count,
            'messages_in_period': self.get_messages_in_period(),
            'free_limit_period': self.consultant.free_limit_period,
            'free_messages_per_period': self.consultant.free_messages_per_period,
            'is_within_free_messages_limit': self.is_within_free_messages_limit(),
            'can_send_message': can_send,
            'message_status_reason': reason,
            'created_at': str(self.created_at),
            'updated_at': str(self.updated_at)
        }