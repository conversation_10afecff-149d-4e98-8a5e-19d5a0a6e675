import datetime
from fastapi import Request
from starlette.responses import JSONResponse

from apps.admin import router
from models import Consultant, Room, Message, db


@router.get("/consultants/{username}/info/", name="Consultant Detailed Stats")
async def consultant_detailed_info(username: str):
    # Get consultant
    consultant = db.session.query(Consultant).filter(Consultant.username == username).first()
    if not consultant:
        return JSONResponse({"error": "Consultant not found"}, status_code=404)
    
    # Get current date for calculations
    current_date = datetime.datetime.now()
    
    # Function to calculate stats for a given time period
    def calculate_period_stats(start_date, end_date, month_labels):
        # Initialize data structures for monthly stats
        monthly_questions = {month: 0 for month in month_labels}
        monthly_answers = {month: 0 for month in month_labels}
        monthly_reply_times = {month: [] for month in month_labels}
        
        # Get rooms for this consultant in the time period
        rooms = db.session.query(Room).filter(
            Room.consultant_id == consultant.id,
            Room.created_at >= start_date,
            Room.created_at <= end_date
        ).all()
        
        if not rooms:
            # Return empty data if no rooms found
            return {
                "labels": month_labels,
                "Questions": [0] * len(month_labels),
                "Answers": [0] * len(month_labels),
                "AvgReplies": [0] * len(month_labels)
            }
        
        room_ids = [room.id for room in rooms]
        
        # Process each room separately to calculate response times accurately
        for room_id in room_ids:
            # Get all messages for this room ordered by time
            messages = db.session.query(Message).filter(
                Message.room_id == room_id
            ).order_by(Message.at_time).all()
            
            # Skip if no messages
            if not messages:
                continue
                
            # Track the last user message to calculate response time
            last_user_message = None
            
            for message in messages:
                # Get the month for this message
                message_date = message.at_time
                # Find the corresponding month in our labels
                month_name = message_date.strftime("%B")
                if month_name in month_labels:
                    # Count messages by type
                    if message.by_user_type == 'User':
                        monthly_questions[month_name] += 1
                        last_user_message = message
                    elif message.by_user_type == 'Consultant' or message.by_user_type == Consultant.__name__:
                        monthly_answers[month_name] += 1
                        
                        # Calculate response time if this is a reply to a user message
                        if last_user_message:
                            response_time = (message.at_time - last_user_message.at_time).total_seconds() / 60
                            monthly_reply_times[month_name].append(response_time)
                            last_user_message = None  # Reset to avoid counting multiple consultant replies to same message
        
        # Calculate average response times
        avg_replies = []
        for month in month_labels:
            if monthly_reply_times[month]:
                avg = sum(monthly_reply_times[month]) / len(monthly_reply_times[month])
                avg_replies.append(round(avg))
            else:
                avg_replies.append(0)
        
        return {
            "labels": month_labels,
            "Questions": [monthly_questions[month] for month in month_labels],
            "Answers": [monthly_answers[month] for month in month_labels],
            "AvgReplies": avg_replies
        }
    
    # Get the last 12 months in correct order
    def get_last_n_months(n):
        months = []
        for i in range(n):
            # Calculate month that is i months ago
            month_date = current_date - datetime.timedelta(days=30*i)
            months.append(month_date.strftime("%B"))
        months.reverse()  # Reverse to get chronological order
        return months
    
    # Calculate stats for different time periods
    
    # Year stats (last 12 months)
    year_start = (current_date - datetime.timedelta(days=365)).replace(day=1, hour=0, minute=0, second=0)
    year_labels = get_last_n_months(12)
    
    # Six months stats
    six_months_start = (current_date - datetime.timedelta(days=180)).replace(day=1, hour=0, minute=0, second=0)
    six_month_labels = get_last_n_months(6)
    
    # Three months stats
    three_months_start = (current_date - datetime.timedelta(days=90)).replace(day=1, hour=0, minute=0, second=0)
    three_month_labels = get_last_n_months(3)
    
    # Current month stats
    current_month_start = current_date.replace(day=1, hour=0, minute=0, second=0)
    current_month_label = [current_date.strftime("%B")]
    
    # Get consultant name, handling potential None values
    consultant_name = consultant.fullname
    if not consultant_name or consultant_name.strip() == "":
        consultant_name = consultant.username
    
    return {
        "expert": consultant_name,
        "year": calculate_period_stats(year_start, current_date, year_labels),
        "six_months": calculate_period_stats(six_months_start, current_date, six_month_labels),
        "three_months": calculate_period_stats(three_months_start, current_date, three_month_labels),
        "month": calculate_period_stats(current_month_start, current_date, current_month_label)
    }