#!/usr/bin/env python3
"""
Test WebSocket timezone conversion functionality
"""

import asyncio
import json
import sys
import os
from datetime import datetime
import pytz

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.websocket_client import WebSocketHealthChecker

async def test_websocket_timezone_conversion():
    """Test WebSocket getConsultants with different timezones"""
    
    print("=== تست WebSocket با timezone های مختلف ===")
    
    # Test cases with different timezones
    test_cases = [
        {
            'name': 'Tehran timezone',
            'timezone': 'Asia/Tehran',
            'language_code': 'ar,ur,az'
        },
        {
            'name': 'Dubai timezone', 
            'timezone': 'Asia/Dubai',
            'language_code': 'ar,ur,az'
        },
        {
            'name': 'New York timezone',
            'timezone': 'America/New_York',
            'language_code': 'ar,ur,az'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        
        try:
            # Create WebSocket health checker
            health_checker = WebSocketHealthChecker()
            
            # Perform health check with specific timezone
            result = await health_checker.perform_websocket_health_check(
                language_code=test_case['language_code'],
                timezone=test_case['timezone']
            )
            
            if result['status'] == 'success':
                print(f"✅ Connection successful")
                print(f"Response time: {result['response_time_ms']}ms")
                print(f"Consultant count: {result['consultant_count']}")
                
                # Parse the response to check scheduling data
                if 'response_data' in result:
                    response_data = result['response_data']
                    if 'results' in response_data:
                        consultants = response_data['results']
                        
                        # Find consultants with scheduling data
                        consultants_with_scheduling = [
                            c for c in consultants 
                            if c.get('scheduling') and any(
                                c['scheduling'].get(day, []) 
                                for day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                            )
                        ]
                        
                        print(f"Consultants with scheduling: {len(consultants_with_scheduling)}")
                        
                        # Show first consultant's scheduling as example
                        if consultants_with_scheduling:
                            example = consultants_with_scheduling[0]
                            print(f"Example consultant: {example.get('username', 'N/A')}")
                            print(f"Scheduling (converted to {test_case['timezone']}):")
                            
                            for day, times in example.get('scheduling', {}).items():
                                if times:
                                    print(f"  {day}: {times}")
                        
                        print(f"Total consultants returned: {len(consultants)}")
                    else:
                        print("❌ No 'results' field in response")
                else:
                    print("❌ No response data available")
            else:
                print(f"❌ Connection failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

async def compare_timezone_responses():
    """Compare responses from different timezones to verify conversion"""
    
    print("\n=== مقایسه response های timezone های مختلف ===")
    
    timezones = ['Asia/Tehran', 'Asia/Dubai']
    responses = {}
    
    # Get responses for different timezones
    for tz in timezones:
        print(f"\nFetching data for {tz}...")
        
        try:
            health_checker = WebSocketHealthChecker()
            result = await health_checker.perform_websocket_health_check(
                language_code='ar,ur,az',
                timezone=tz
            )
            
            if result['status'] == 'success' and 'response_data' in result:
                responses[tz] = result['response_data']
                print(f"✅ Got response for {tz}")
            else:
                print(f"❌ Failed to get response for {tz}")
                
        except Exception as e:
            print(f"❌ Error for {tz}: {e}")
    
    # Compare scheduling data between timezones
    if len(responses) >= 2:
        print(f"\n--- مقایسه scheduling بین timezone ها ---")
        
        tz1, tz2 = list(responses.keys())[:2]
        consultants1 = {c['username']: c for c in responses[tz1].get('results', [])}
        consultants2 = {c['username']: c for c in responses[tz2].get('results', [])}
        
        # Find common consultants with scheduling
        common_usernames = set(consultants1.keys()) & set(consultants2.keys())
        
        for username in list(common_usernames)[:3]:  # Check first 3 common consultants
            c1 = consultants1[username]
            c2 = consultants2[username]
            
            scheduling1 = c1.get('scheduling', {})
            scheduling2 = c2.get('scheduling', {})
            
            # Check if scheduling data is different (indicating conversion worked)
            has_scheduling = any(scheduling1.get(day, []) for day in scheduling1.keys())
            
            if has_scheduling:
                print(f"\nConsultant: {username}")
                print(f"{tz1} scheduling:")
                for day, times in scheduling1.items():
                    if times:
                        print(f"  {day}: {times}")
                
                print(f"{tz2} scheduling:")
                for day, times in scheduling2.items():
                    if times:
                        print(f"  {day}: {times}")
                
                # Check if times are different (indicating conversion)
                different_times = False
                for day in scheduling1.keys():
                    if scheduling1.get(day) != scheduling2.get(day):
                        different_times = True
                        break
                
                if different_times:
                    print("✅ Scheduling times are different - timezone conversion working!")
                else:
                    print("⚠️  Scheduling times are the same - check if conversion is working")
                
                break  # Just show one example
    else:
        print("❌ Not enough responses to compare")

async def main():
    """Main test function"""
    
    print("شروع تست WebSocket timezone conversion")
    
    try:
        # Test WebSocket with different timezones
        await test_websocket_timezone_conversion()
        
        # Compare responses between timezones
        await compare_timezone_responses()
        
        print("\n=== تست WebSocket کامل شد ===")
        
    except Exception as e:
        print(f"خطا در تست: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
