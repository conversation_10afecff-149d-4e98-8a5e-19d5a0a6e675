#!/usr/bin/env python
import os
import sys
import logging
import time
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("telegram_bot_cron.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import the function from the bot module
try:
    from bot import fetch_and_post_messages_for_channel
    
    fetch_and_post_messages_for_channel('24_48')
    time.sleep(50)
    fetch_and_post_messages_for_channel('48_72')
    time.sleep(50)
    fetch_and_post_messages_for_channel('72_216')

except Exception as e:
    logger.error(f"Error running fetch_24_48.py: {e}")
    sys.exit(1)