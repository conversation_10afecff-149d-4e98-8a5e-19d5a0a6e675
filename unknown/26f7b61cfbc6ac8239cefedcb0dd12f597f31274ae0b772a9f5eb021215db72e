from datetime import datetime
from typing import *

from pydantic import BaseModel, validator

from schemas.models import *

__all__ = [
    'HistoryResponse', 'FileUploadResponse', 'TextMessageResponse',
    'ByUser', 'ConsultantsResponse', 'RoomsResponse', 'ConsultantsListResponse',
    'RoomListResponse', 'CallResponse', 'CallListResponse', 
]


class ByUser(BaseModel):
    act: str = "user"
    username: str = ...
    fullname: Optional[str] = None
    avatar_url: Optional[str] = None
    language_code: Optional[str] = None
    is_active: bool = True
    is_admin: bool = False
    is_consultant: bool = False
    is_supporter: bool = False
    is_banned: bool = False
    is_ai: bool = False
    ai_project: Optional[str] = None
    has_preferences: bool = False
    gender: Optional[str] = None
    preferred_languages: Optional[List[str]] = []  # لیست زبان‌ها
    age: Optional[int] = None
    country: Optional[str] = None
    city: Optional[str] = None
    
    @validator("preferred_languages", pre=True)
    def user_preferred_languages(cls, v):
        if isinstance(v, str):
            return v.split(", ") if v else []
        return v or []


    @validator('username', pre=True, always=True)
    def process_username(cls, v):
        if isinstance(v, str) and ':' in v:
            return v.split(':')[0]
        return v

class TextMessageResponse(BaseModel):
    act: str = "message"
    content: str = ...
    at_time: datetime = ...

    @validator('at_time', always=True)
    def validate_at_time(cls, val):
        return str(val)


class HistoryResponse(BaseModel):
    act: str = "history"
    count: int = ...
    room_id: str = ...
    status: str = 'open'
    reason: Optional[str] = None
    room: dict = {}
    results: List[MessageModel] = []


class FileUploadResponse(BaseModel):
    name: str = ...
    id: str = ...
    mime_act: str = ...


class ConsultantsResponse(BaseModel):
    act: str = "consultant"
    unread_count: int = 0
    is_supporter: bool = False
    username: str = ...
    fullname: Optional[str] = None
    slogan: Optional[str] = None
    topics: Any = ...
    languages: Any = ...
    avatar_url: Optional[str] = None
    is_active: bool = True
    is_ai: bool = False
    ai_project: Optional[str] = None

    @validator('username', pre=True, always=True)
    def process_username(cls, v):
        if isinstance(v, str) and ':' in v:
            return v.split(':')[0]
        return v


class ConsultantsListResponse(BaseModel):
    act: str = "consultantList"
    results: List[ConsultantsResponse] = ...


class RoomsResponse(BaseModel):
    act: str = "room"
    id: Any = None
    unread_count: int = 0

    subject: str = None
    client: ByUser = ...
    consultant: ConsultantsResponse = ...
    created_at: datetime = ...
    closed_at: datetime = None
    status: str = ...
    reason: Optional[str] = None
    is_public: bool = ...
    last_message: dict = ...
    recent_call: List[dict] = []  
    session_duration: str = ''
    messages_count: int = 0
    room_type: str = 'chat'
    voice_call_count: int = 0
    video_call_count: int = 0

    @validator('created_at', always=True)
    def validate_created_at(cls, val):
        return str(val)


class RoomListResponse(BaseModel):
    act: str = "roomList"
    count: int = 0
    current_page: int = 1
    total_pages: int = 1
    per_page: int = 50
    results: List[RoomsResponse] = ...


class CallResponse(BaseModel):
    id: int
    consultant_id: int
    client_id: int
    start_time: datetime
    end_time: Optional[datetime]
    call_type: str
    cost: int
    timer_active: Optional[bool] = False    
    status: str
    recording_audio_link: str = None
    

class CallListResponse(BaseModel):
    act: str = "recentCalls"
    count: int = 0
    current_page: int = 1
    total_pages: int = 1
    per_page: int = 50
    results: List[CallResponse] = ...
    
    