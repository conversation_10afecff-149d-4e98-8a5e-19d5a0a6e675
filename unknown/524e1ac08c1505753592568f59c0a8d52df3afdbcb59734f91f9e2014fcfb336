from enum import Enum
from typing import Optional, Any

from fastapi_sqlalchemy import db
from pydantic import BaseModel, root_validator, validator

__all__ = [
    'SendMessage', 'GetHistory', 'SupportRequestData',
    'MessageBaseModel', 'GetConsultants', 'StartRoom',
    'ReportUser', 'UpdateRoom', 'Login', 'GoogleLogin', 'RecentCalls',
]

from sqlalchemy.orm import joinedload

from models import Room, Consultant, Topic


class _ChatType(str, Enum):
    team = 'team'
    private = 'private'


class MessageBaseModel(BaseModel):
    pass
    # def _iter(
    #         self,
    #         to_dict: bool = False,
    #         by_alias: bool = False,
    #         include: Union['AbstractSetIntStr', 'MappingIntStrAny'] = None,
    #         exclude: Union['AbstractSetIntStr', 'MappingIntStrAny'] = {'chat_type', 'chat_id', 'type'},
    #         exclude_unset: bool = False,
    #         exclude_defaults: bool = False,
    #         exclude_none: bool = True,
    # ) -> 'TupleGenerator':
    #     return super(MessageBaseModel, self)._iter(
    #         to_dict, by_alias, include, exclude, exclude_unset, exclude_defaults, exclude_none
    #     )


# ------------------ Send Message Models----------------------------

class SendMessage(BaseModel):
    type: str = 'text'
    text: str = ...
    room: Any = ...
    tmp_id: str = ''
    payload: str = ''
    
    @validator('room')
    def validate_room(cls, value):
        if not value:
            raise ValueError('room id is required')

        if type(value) is Room:
            return value

        with db():
            if room := db.session.query(Room).filter(Room.id == value).first():
                return room

        return ValueError(f"Room {value} does not exist")


# --------------- Get History Model---------------


class GetHistory(BaseModel):
    type: Optional[str] = 'getHistory'
    room: str

    @validator('room')
    def validate_room(cls, value):
        if not value:
            raise ValueError('room id is required')

        with db():
            if room := db.session.query(Room).filter(Room.id == value).options(
                    joinedload(Room.client), joinedload(Room.consultant)
            ).first():
                return room

            raise ValueError(f"Room {value} does not exist")

        return value


class GetConsultants(BaseModel):
    type: str = 'getConsultants'
    language: str = ''


class RecentCalls(BaseModel):
    type: str = 'recentCalls'
    consultant: str = ''
    page: int = 1
    per_page: int = 10

    @validator('consultant')
    def validate_consultant(cls, val):
        if val:
            with db():
                if consultant := db.session.query(Consultant).filter(Consultant.username == val).first():
                    return consultant

            return val

class GetRooms(BaseModel):
    type: str = 'getRooms'
    consultant: str = ''
    page: int = 1
    per_page: int = 10
    date: str = None
    unread_only: Optional[bool] = False
    not_replied: Optional[bool] = False
    search: Optional[str] = None
    
    @validator('unread_only', 'not_replied', pre=True, always=True)
    def ensure_boolean(cls, value):
        if value in [True, 'true', '1', 1]:
            return True
        return False
        
    @validator('consultant')
    def validate_consultant(cls, val):
        if val:
            with db():
                if consultant := db.session.query(Consultant).filter(Consultant.username == val).first():
                    return consultant

            return val


class StartRoom(BaseModel):
    type: str = 'startRoom'
    message: str = ''
    consultant: str = ...

    @root_validator()
    def validate_topic_and_consultant(cls, values):
        with db():
            _consultant = values.get('consultant')

            consultant = db.session.query(Consultant).filter(Consultant.username == _consultant).first()
            values['consultant'] = consultant

            if not consultant:
                raise ValueError(f"Consultant {_consultant} is not valid")

            return values


class UpdateRoom(BaseModel):
    subject: str = None
    topic: str = None
    consultant: str = None

    @root_validator()
    def validate_topic_and_consultant(cls, values):
        with db():
            _topic, _consultant = values.get('topic'), values.get('consultant')

            if _consultant:
                consultant = db.session.query(Consultant).filter(Consultant.username == _consultant).first()
                if not consultant:
                    raise ValueError(f"Consultant {_consultant} is not valid")
                values['consultant'] = consultant

            if _topic:
                topic = db.session.query(Topic).filter(Topic.title == _topic).first()
                values['topic'] = topic

            return values


class Login(BaseModel):
    username: str = ...
    password: str = ...


class ReportUser(BaseModel):
    subject: str = ''
    username: str = ...
    reason: str = ...


class SupportRequestData(BaseModel):
    subject: str = ...
    wa_number: str = ...
    description: str = ...


class GoogleLogin(BaseModel):
    email: str = ...
    family_name: Optional[str] = None
    given_name: Optional[str] = None
    id: str
    locale: Optional[str] = None
    name: Optional[str] = None
    picture: Optional[str] = None
    verified_email: bool

class GetToken(BaseModel):
    event: str = 'Talk'
    user_token: str
    call_id: int
    can_subscribe: bool = True
    can_publish: bool = True
    
    
    
class RateConsultantRequest(BaseModel):
    consultant: str 
    rate: int = 5
