def convert_hour_to_float(hour_str):
    # Convert hour string (e.g., "12:01") to a float representation
    hours, minutes = map(int, hour_str.split(':'))
    return hours + minutes / 60.0


def is_in_scheduling(consultant, scheduling_plan):
    today, hour = '', ''
    today = ''
    # Convert input hour to float
    input_hour = convert_hour_to_float(hour)

    # Check each day in the scheduling plan
    for day, time_ranges in scheduling_plan.items():
        if day == today:
            for time_range in time_ranges:
                start_str, end_str = time_range.split(',')
                start_hour = convert_hour_to_float(start_str)
                end_hour = convert_hour_to_float(end_str)

                # Check if the input hour is within the current time range
                if start_hour <= input_hour <= end_hour:
                    return True

    return False

def get_languages():
    return [
        {"name": "English", "code": "en"},
        {"name": "Spanish", "code": "es"},
        {"name": "German", "code": "de"},
        {"name": "Uzbek", "code": "uz"},
        {"name": "Portuguese", "code": "pt"},
        {"name": "Bengali", "code": "bn"},
        {"name": "Chinese", "code": "zh"},
        {"name": "Azerbaijani", "code": "az"},
        {"name": "Urdu", "code": "ur"},
        {"name": "French", "code": "fr"},
        {"name": "Turkish", "code": "tr"},
        {"name": "Indonesian", "code": "id"},
        {"name": "Swahili", "code": "sw"},
        {"name": "Russian", "code": "ru"},
        {"name": "Arabic", "code": "ar"},
        {"name": "Tajik", "code": "tg"},
        {"name": "Persian", "code": "fa"},
        {"name": "Gujarati", "code": "gu"},
        {"name": "Kashmiri", "code": "ks"},
        {"name": "Hausa", "code": "ha"}
    ]