import datetime

from sqlalchemy import Column, String, DateTime, Integer
from sqlalchemy_utils import generic_relationship

__all__ = [
    'Report',
]

from models import Base


class Report(Base):
    __tablename__ = "reports"

    id = Column(Integer, primary_key=True, index=True)
    reporter_id = Column(Integer, )
    reporter_type = Column(String)
    reporter = generic_relationship(reporter_type, reporter_id,)

    reporter_username = Column(String, )
    reporter_fullname = Column(String, )

    username = Column(String,)
    fullname = Column(String, nullable=True)

    message = Column(String)
    reported_at = Column(DateTime, default=datetime.datetime.now)

    user_id = Column(Integer)
    user_type = Column(String)
    user = generic_relationship(user_type, user_id)

    def __str__(self):
        return f'Report({self.pk})'

    def __repr__(self):
        return f'Report({self.pk})'
