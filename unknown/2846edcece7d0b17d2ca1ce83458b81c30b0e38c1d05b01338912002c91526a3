import datetime

from sqlalchemy import Column, String, DateTime, Integer, ForeignKey, Text, JSON, Boolean
from sqlalchemy.orm import relationship

from sqlalchemy_utils import generic_relationship

from models import Base


class Notification(Base):
    __tablename__ = "notifications"


    id = Column(Integer, primary_key=True, index=True)
    room = Column(Integer, ForeignKey("rooms.id"))
    message = Column(Integer, ForeignKey("messages.id"))

    user_id = Column(Integer)
    user_type = Column(String)
    user = generic_relationship(user_type, user_id)

    date = Column(DateTime, default=datetime.datetime.now)

class PushNotification(Base):
    __tablename__ = 'push_notifications'

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=True)
    message = Column(Text, nullable=True)
    data = Column(JSON, default=dict, nullable=True)
    username = Column(String, nullable=True)  
    result = Column(JSON, default=dict, nullable=True)
    created_at = Column(DateTime, nullable=True, default=None)
    updated_at = Column(DateTime, nullable=True, default=None)
    is_read = Column(Boolean, default=False)

    def __repr__(self):
        return f"<PushNotification(id={self.id}, title={self.title})>"
