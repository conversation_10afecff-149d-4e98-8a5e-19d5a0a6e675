#!/usr/bin/env python3
"""
Check consultant ID 7 details
"""
import asyncio
import websockets
import json

async def check_all_consultants():
    """Get all consultants and look for ID 7"""
    
    token = "516e059d2a6b31d74b6a9e4c8f98fe4e8413efbc"
    url = f"wss://qa.habibapp.com/ws/?version=3&t={token}&language=az"
    
    print("=== Checking all consultants for ID 7 ===")
    
    try:
        async with websockets.connect(url) as websocket:
            print("✓ Connected to WebSocket")
            
            # Wait for initial response
            initial_response = await websocket.recv()
            
            # Send getConsultants request with broader parameters
            request_data = {
                "action": "getConsultants",
                "language_code": "en,ar,ur,az,fa",  # More languages
                "timezone": "Asia/Tehran",
                "only_interacted": False,
                "search": None
            }
            
            print(f"Sending getConsultants with broader language filter...")
            await websocket.send(json.dumps(request_data))
            
            # Wait for response
            response = await websocket.recv()
            
            try:
                response_data = json.loads(response)
                
                if 'results' in response_data:
                    results = response_data['results']
                    print(f"Total consultants found: {len(results)}")
                    
                    # Look for consultant with ID 7 or username containing 7
                    found_consultants = []
                    
                    for consultant in results:
                        username = consultant.get('username', '')
                        consultant_id = consultant.get('id', 'N/A')
                        
                        # Check if this could be consultant 7
                        if ('7' in str(username) or 
                            str(consultant_id) == '7' or 
                            username.endswith('7') or
                            '7' in str(consultant_id)):
                            found_consultants.append(consultant)
                    
                    if found_consultants:
                        print(f"\n=== Found potential consultant(s) 7 ===")
                        for i, consultant in enumerate(found_consultants):
                            print(f"\nCandidate {i+1}:")
                            print(f"  ID: {consultant.get('id', 'N/A')}")
                            print(f"  Username: {consultant.get('username')}")
                            print(f"  Status: {consultant.get('status')}")
                            print(f"  Availability status: {consultant.get('availability_status')}")
                            print(f"  Visible: {consultant.get('visible', 'N/A')}")
                            print(f"  Is banned: {consultant.get('is_banned', 'N/A')}")
                            print(f"  Is tester: {consultant.get('is_tester', 'N/A')}")
                            print(f"  Languages: {consultant.get('languages', 'N/A')}")
                            print(f"  Status from schedule: {consultant.get('status_from_schedule', 'N/A')}")
                            print(f"  Scheduling: {consultant.get('scheduling', 'N/A')}")
                    else:
                        print(f"\n✗ No consultant with ID 7 found")
                        print(f"\nAll consultants:")
                        for i, consultant in enumerate(results):
                            print(f"  {i+1}. ID: {consultant.get('id', 'N/A')}, Username: {consultant.get('username')}, Status: {consultant.get('status')}")
                
            except json.JSONDecodeError as e:
                print(f"✗ JSON parse error: {e}")
                
    except Exception as e:
        print(f"✗ Connection error: {e}")

async def test_with_admin_user():
    """Test if consultant 7 appears for admin users"""
    
    # This would require admin token - just showing the concept
    print("\n=== Note ===")
    print("If consultant 7 is not visible, it might be:")
    print("1. is_banned = True")
    print("2. visible = False") 
    print("3. is_tester = True (and current user is not tester)")
    print("4. Languages don't match the filter")
    print("5. Actually doesn't exist or has different ID")

async def main():
    await check_all_consultants()
    await test_with_admin_user()

if __name__ == "__main__":
    asyncio.run(main())
