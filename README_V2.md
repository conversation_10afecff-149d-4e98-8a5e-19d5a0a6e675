<div style="text-align: center;">**بسم الله الرحمن الرحیم**</div>

<p align="center">
  <a href="https://fastapi.tiangolo.com"><img src="https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png" alt="FastAPI"></a>
</p>
<p align="center">
    <em>FastAPI framework, high performance, easy to learn, fast to code, ready for production</em>
</p>
<p align="center">
<a href="https://github.com/tiangolo/fastapi/actions?query=workflow%3ATest+event%3Apush+branch%3Amaster" target="_blank">
    <img src="https://github.com/tiangolo/fastapi/workflows/Test/badge.svg?event=push&branch=master" alt="Test">
</a>
<a href="https://codecov.io/gh/tiangolo/fastapi" target="_blank">
    <img src="https://img.shields.io/codecov/c/github/tiangolo/fastapi?color=%2334D058" alt="Coverage">
</a>
<a href="https://pypi.org/project/fastapi" target="_blank">
    <img src="https://img.shields.io/pypi/v/fastapi?color=%2334D058&label=pypi%20package" alt="Package version">
</a>
<a href="https://pypi.org/project/fastapi" target="_blank">
    <img src="https://img.shields.io/pypi/pyversions/fastapi.svg?color=%2334D058" alt="Supported Python versions">
</a>
</p>

# HabibApp Chat Application (Websocket)

## Actions

getHistory getConsultants getRooms startRoom sendMessage

### connect to socket

```shell
BASE_URL = "qa.habibapp.com"

for client:
ws://$BASE_URL/ws/?version=2&t=${TOKEN}

for consultant:
ws://$BASE_URL/ws/?version=2&t=consultant:${TOKEN}

```

### get consultants

```yaml
{
  "action": "getConsultants",
  "language_code": "en", #client language
  "timezone": "Asia/Tehran" # client timezone
}
```

### Start Room

```yaml
{
  "action": "startRoom",
  "consultant": "<EMAIL>", # consultant username
  "room_type": "chat" # room type One of [chat, video, voice]
}
```

### Categories List

```yaml
{
  "action": "getCategories"
}

```