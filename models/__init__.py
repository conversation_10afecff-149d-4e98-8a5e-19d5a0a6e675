from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel, Session
import os

# database_url = os.environ.get('DB_URL', 'postgresql://postgres:root@127.0.0.1/najm-chat')
database_url = '***********************************************************************/chat-db'

SQLALCHEMY_DATABASE_URL = database_url

engine = create_engine(
    SQLALCHEMY_DATABASE_URL
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def get_session():
    with Session(engine) as session:
        yield session


from models.admin import *
from models.user import *
from models.notifications import *
from models.consultant import *
from models.message import *
from models.room import *
from models.file import *
from models.report import *
from models.topic import *
from models.category import *
from models.rate import *
from models.activity import *
from models.support import *
from models.calls import *
from models.subscription import *

# db_session = SessionLocal()
