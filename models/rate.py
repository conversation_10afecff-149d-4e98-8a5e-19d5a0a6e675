import datetime

from sqlalchemy import Column, String, DateTime, Integer, ForeignKey

from models import Base


class Rate(Base):
    __tablename__ = "rates"

    id = Column(Integer, primary_key=True, index=True)
    user = Column(Integer, ForeignKey('users.id'))
    consultant = Column(Integer, ForeignKey('consultants.id'))

    rate = Column(Integer, default=1)
    
    created_at = Column(DateTime, default=datetime.datetime.now)
