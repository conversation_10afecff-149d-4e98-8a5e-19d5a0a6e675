from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON><PERSON>, Boolean
from sqlalchemy.orm import relationship
from models import Base
import datetime
from sqlalchemy.sql import func

from typing import List, Dict, Optional, Tuple

class Call(Base):
    __tablename__ = "calls"
    id = Column(Integer, primary_key=True, index=True)
    consultant_id = Column(Integer, ForeignKey('consultants.id'))
    client_id = Column(Integer, ForeignKey('users.id'))
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=True)
    call_type = Column(String, nullable=False)  # 'voice' or 'video'
    cost = Column(Integer, nullable=False)
    timer_active = Column(Boolean, default=False)  # تغییر به False

    #  وضعیت تایید تماس
    status = Column(String, default='unconfirmed')  # وضعیت: 'unconfirmed', 'confirmed', 'completed'
    
    is_reservation = Column(Boolean, default=False)  # آیا این تماس از نوع رزرو است؟
    reservation_date = Column(DateTime(timezone=True), nullable=True)  # تاریخ رزرو شده برای تماس

    consultant = relationship("Consultant", back_populates="calls")
    client = relationship("User", back_populates="calls")
    
    
    @classmethod
    def find_call_id_by_user(cls, db, user_id):
        try:
            with db():
                # last_call = db.session.query(cls).filter_by(client_id=user_id).order_by(cls.start_time.desc()).first()
                last_call = db.session.query(Call.id).order_by(Call.id.desc()).first()

                if last_call:
                    return last_call.id  # برگرداندن call_id
            return None
        except Exception as e:
            print(f"Error finding call by user >>> {e}")
            return None