import datetime

from sqlalchemy import Column, String, DateTime, Inte<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>olean, Index
from sqlalchemy.orm import relationship
from sqlalchemy_utils import generic_relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import TIMESTAMP
import datetime

from models import Base

class Message(Base):
    __tablename__ = 'messages'
    __table_args__ = (
        # ایندکس ترکیبی برای فیلدهای by_user_id و by_user_type
        Index('ix_messages_by_user', 'by_user_id', 'by_user_type'),
        # ایندکس ترکیبی برای بهینه‌سازی get_history query
        Index('ix_messages_room_id_id', 'room_id', 'id'),
        # ایندکس ترکیبی برای فیلتر پیام‌های خوانده نشده
        Index('ix_messages_room_has_read_by_user_type', 'room_id', 'has_read', 'by_user_type'),
    )

    id = Column(Integer, primary_key=True, index=True)
    mime_type = Column(String, default='text')
    content = Column(String)
    audio_url = Column(String, nullable=True)  # افزودن فیلد برای URL فایل صوتی
    at_time = Column(DateTime(timezone=True), default=func.now())  

    reply_to = Column(Integer, ForeignKey("messages.id"), nullable=True)
    edited_at = Column(DateTime, nullable=True)
    has_read = Column(Boolean, default=False)
    by_user_id = Column(Integer, index=True)
    by_user_type = Column(String, index=True)
    by_user = generic_relationship(by_user_type, by_user_id)
    room_id = Column(Integer, ForeignKey("rooms.id"), index=True)
    room = relationship("Room", back_populates="messages")
    chat_type = Column(String, default='TextChat')  # VoiceChat, VideoChat, TextChat

    def to_dict(self):
        """
        current_user_type should be consultant or user
        """
        return {
            'id': str(self.id),
            'mime_type': self.mime_type,
            'content': self.content,
            'audio_url': self.audio_url,  # افزودن به دیکشنری خروجی
            'date': str(self.at_time),
            'reply_to': self.reply_to,
            'room_id': self.room_id,
            'chat_type': self.chat_type,
            'by_user': {
                'id': str(self.by_user_id),
                'username': self.by_user.username.split(':')[0] if ':' in str(self.by_user.username) else self.by_user.username,
                'fullname': self.by_user.fullname,
                'avatar_url': self.by_user.avatar_url,
                'is_consultant': False if self.by_user_type == 'User' else True,
                'by_user_type': self.by_user_type,
            },
            'edited_at': str(self.edited_at) if self.edited_at else None,
            'has_read': self.has_read,
        }
