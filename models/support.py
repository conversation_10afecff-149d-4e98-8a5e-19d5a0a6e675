import datetime

from sqlalchemy import Column, String, DateTime, Integer, Foreign<PERSON>ey
from sqlalchemy.orm import relationship

__all__ = [
    'SupportRequest',
]

from models import Base


class SupportRequest(Base):
    __tablename__ = "support_request"

    id = Column(Integer, primary_key=True, index=True)

    subject = Column(String)
    wa_number = Column(String)
    description = Column(String)
    at_time = Column(DateTime, default=datetime.datetime.now)

    user_id = Column(Integer, ForeignKey("users.id"))
    user = relationship('User', back_populates='support_requests')

    def __str__(self):
        return f'Report({self.pk})'

    def __repr__(self):
        return f'Report({self.pk})'
