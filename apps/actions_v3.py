import logging
import os
import threading
import asyncio
from datetime import datetime, timed<PERSON>ta
from math import ceil
from fastapi import BackgroundTasks, WebSocket
import json
from collections import defaultdict

import aiohttp
import requests
from sqlalchemy import func, and_, or_
from sqlalchemy import case
from sqlalchemy.orm import aliased

from sqlalchemy import desc
from apps.actions_v2 import ActionHandlerV2
from models import Message, Room, Consultant, Admin, User
from models.calls import Call
from schemas.models import MessageModel
from schemas.request_types_v2 import StartRoom
from schemas.response_types import RoomsResponse, HistoryResponse, RoomListResponse, CallListResponse, CallResponse
from utils.fcm_notification import send_notification, send_silent_notification
from utils.paginate import paginate
from .signature import get_signature_data
from urllib.parse import urlencode
from datetime import datetime, timezone
from utils.hlog import log_message

def get_language_name(code):
    langs = {
        'en': 'English',
        'fa': 'Persian',
        'ar': 'Arabic',
        'fr': 'French',
        'in': 'Indonesian',
        'tr': 'Turkish',
        'az': 'Azerbaijani',
        'ru': 'Russian',
        'sw': 'Swahili',
        'es': 'Spanish',
        'ur': 'Urdu',
        'de': 'German',
    }
    return langs.get(code, 'English')

class ActionHandlerV3(ActionHandlerV2):
    def __init__(self):
        super().__init__()
        self.actions['getHistory'] = self.get_history
        self.actions['vmRequest'] = self.vm_request
        self.actions['setFCM'] = self.set_fcm
        self.actions['streamStarted'] = self.stream_started_handler
        self.actions['streamStopped'] = self.stream_stopped_handler
        self.actions['payForNextPeriod'] = self.pay_for_next_period_handler
        self.actions['consultantResponseCall'] = self.handle_consultant_response_event
        self.actions['cancelCall'] = self.cancel_call
        self.actions['recentCalls'] = self.get_recent_calls
        self.consultant_responses = defaultdict(asyncio.Queue)
        
    def is_on_another_call(self, consultant):
        """
        Check if a consultant is currently on another call.
        
        Args:
            consultant: Consultant object to check
            
        Returns:
            bool: True if consultant is on another active call, False otherwise
        """
        try:
            # Check for active calls where this consultant is participating
            active_call = self.db.session.query(Call).filter(
                Call.consultant_id == consultant.id,
                Call.status == "confirmed",
                Call.end_time.is_(None)
            ).first()
            
            return active_call is not None
        except Exception as e:
            logging.error(f"Error checking if consultant is on another call: {e}")
            return False
        

    async def set_fcm(self, data):
        # Extract platform from WebSocket connection
        platform = getattr(self.connection, 'platform', 'android')

        if type(self.from_user) is Consultant:
            self.db.session.query(Consultant).filter(
                Consultant.username == self.from_user.username,
                ).update({
                Consultant.fcm: data['token'],
                Consultant.device_os: platform,
            })


        elif type(self.from_user) is User:
            self.db.session.query(User).filter(
                User.username == self.from_user.username,
                ).update({
                User.fcm: data['token'],
                User.device_os: platform,
            })

        self.db.session.commit()

        return {
            'action': data['action'],
            'status': True,
        }


    
    async def vm_request(self, data):
        """
            Voice / Video message Request 
        """

        consultant = self.db.session.query(Consultant).filter(Consultant.username == data['consultant']).first()
        if not consultant:
                return {'action': data['action'], 'error': 'not consultant', 'status': False}
            
        # if data['chat_type'] == 'video':
        #     if consultant.video_call_cost is None:
        #         return {'action': data['action'], 'error': 'consultant not video call ', 'status': False}
        #     cost = consultant.video_call_cost

        # elif data['chat_type'] == 'voice':
        #     if consultant.voice_call_cost is None:
        #         return {'act': data['action'], 'error': 'consultant not voice call ', 'status': False}
        #     cost = consultant.voice_call_cost

        if consultant.status == "busy" or consultant.status == "offline":
            return {'act': data['action'], 'error': 'consultant offline or busy', 'status': False}
            
            
        # has_balance = await credit_balance(self.from_user, cost)
        # if not has_balance:
            # return {'act': data['action'], 'error': 'Insufficient balance', 'status': False}

        
        call = Call(
            consultant_id=consultant.id,
            client_id=self.from_user.id,
            start_time=datetime.now(),
            call_type=data['chat_type'],
            cost=consultant.video_call_cost if data['chat_type'] == 'video' else consultant.voice_call_cost,
            status='unconfirmed'
        )
        self.db.session.add(call)
        self.db.session.commit()
        self.db.session.refresh(call)             
        
        if not call.consultant and not call.consultant.fcm:            
            await self.connection.send_text(json.dumps({'act': 'callFailed', 'status': False, 'error': 'Failed to send notification to consultant.'}))
            
        await self.connection.send_text(json.dumps({'act': data['action'], 'call_id': call.id, 'status': True, 'message': 'call prossing'}))


    async def handle_consultant_response(self, call_id):
        call = self.db.session.query(Call).filter(Call.id == int(call_id)).first()
        try:
            # confirmation = await asyncio.wait_for(self.wait_for_consultant_response(int(call_id)), timeout=500)
            confirmation = await asyncio.wait_for(self.waiting_tasks[call_id], timeout=30)  # محدودیت زمان انتظار به ۵۰۰ ثانیه

            if confirmation == 'confirmed':
                # logging.info(f"The connection was established successfully/ => {call.id}")
                log_message('handle_consultant_response', 'Confirmed', {'call_id': {call.id}})
                call.status = 'confirmed'
                if int(call_id) in self.waiting_tasks:
                    task = self.waiting_tasks[int(call_id)]
                    del self.waiting_tasks[call_id]
                self.consultant_responses.pop(call.id, None)

                self.db.session.commit()
                await self.broadcast_to_users(
                    [call.client, call.consultant],
                    data=json.dumps({
                        'act': 'callConfirmed',
                        'status': True,
                        "call_id": call.id,
                        'call_type': call.call_type,
                        'call_status': call.status,
                        'message': f"The connection was established successfully"
                    })
                )
                await self.change_status(data={"status": "busy"})
                
            elif confirmation == 'rejected':
                # logging.info(f"The connection Rejected/ => {call.id}")
                log_message('handle_consultant_response', 'Rejected', {'call_id': {call.id}})
                call.status = 'rejected'
                self.db.session.commit()
                if int(call_id) in self.waiting_tasks:
                    task = self.waiting_tasks[int(call_id)]
                    del self.waiting_tasks[call_id]
                self.consultant_responses.pop(call.id, None)

                await self.broadcast_to_users(
                    [call.client],
                    data=json.dumps({
                        'act': 'callRejected',
                        'status': True,
                        "call_id": call.id,
                        'call_type': call.call_type,
                        'call_id': call.id,
                        'call_status': call.status,
                        'call_type': call.call_type,
                        'call_id': call.id,
                        'call_status': call.status,
                        'message_id': call.id,
                        'from_user_fullname': call.client.fullname,
                        'from_user_avatar': call.client.avatar_url,
                        'from_user_username': call.client.username,
                        'message': f"The call was rejected"
                    })
                )

        except asyncio.TimeoutError:
            log_message('handle_consultant_response', 'TimeoutError', {'call_id': {call_id}})
            call.status = 'unconfirmed'
            self.db.session.commit()
            await self.broadcast_to_users(
                [call.client],
                data=json.dumps({
                    'act': 'callTimeout',
                    'status': False,
                    'call_type': call.call_type,
                    'call_id': call.id,
                    'call_status': call.status,
                    'message_id': call.id,
                    'from_user_fullname': call.client.fullname,
                    'from_user_avatar': call.client.avatar_url,
                    'from_user_username': call.client.username,
                    'message': f"Call {call_id} timed out waiting for consultant's response."
                })
            )
            
        except asyncio.CancelledError:
            log_message('Handle_Response_Task', 'Cancelled user disconnected', {'call_id': {call_id}})
            request_data = {
                'act': 'ExternalCallCanceledUser',
                'notif_type': 'callCanceled',
                'call_type': call.call_type,
                'call_id': call.id,
                'call_status': call.status,
                'message_id': call.id,
                'from_user_fullname': call.client.fullname,
                'from_user_avatar': call.client.avatar_url,
                'from_user_username': call.client.username,
                'session_duration': call.consultant.session_duration,
                'message': 'call cancel user'
            }
            # Use consultant's device_os for platform-specific notifications
            consultant_platform = getattr(call.consultant, 'device_os', 'android')
            await send_notification(
                ids=[call.consultant.fcm],
                data=request_data,
                extra_notification_kwargs={'android_channel_id': 'incoming_call_channel'},
                platform=consultant_platform
            )
            



    async def wait_for_consultant_response(self, call_id):
        call = self.db.session.query(Call).filter(Call.id == call_id).first()

        # print(f'>> >>>{self.consultant_responses}/call.id:{type(call.id)}/ call_id: {type(call_id)}')
        log_message('Wait_Task', 'wait_for_consultant_response', {'responses': f"{self.consultant_responses}", 'call.id': f"{type(call.id)} / {call.id}"})
        
        for second in range(30):
            try:
                if not self.consultant_responses[call.id].empty():
                
                    response_data = await self.consultant_responses[call.id].get()
                    if int(response_data['call_id']) == call.id:
                        # print(f">>>>>>>>>>>>>>>{response_data['response']}")          
                        return response_data['response']

                # print(f"Checking... {second + 1} seconds elapsed.")
                await asyncio.sleep(1)  
            except asyncio.CancelledError:
                logging.info(f"Task for call {call.id} was cancelled.")
                self.waiting_tasks[int(call.id)].cancel() and self.waiting_tasks.pop(int(call.id), None) if int(call.id) in self.waiting_tasks else None
                self.consultant_responses.pop(call.id, None)
                self.waiting_tasks.pop(call.id, None)
                raise
            except asyncio.TimeoutError:
                logging.info(f"Call {call_id} timed out waiting for consultant's response.")
                self.waiting_tasks.pop(call.id, None)
                self.consultant_responses.pop(call.id, None)
                raise
            except Exception as e:
                self.waiting_tasks[int(call.id)].cancel() and self.waiting_tasks.pop(int(call.id), None) if int(call.id) in self.waiting_tasks else None
                self.consultant_responses.pop(call.id, None)
                logging.error(f"Error while checking consultant response: {e}")
                await asyncio.sleep(1)  

    


    async def handle_consultant_response_event(self, data):
        call_id = data.get('call_id')
        response = data.get('response')
        action = data.get('action')

        if not call_id or response not in ['confirmed', 'rejected']:
            logging.error(f"Invalid response data received from consultant: {data}")
            return {'action': data['action'], 'status': False, 'error': 'not call_id or status response'}

        # بررسی وجود تماس در سیستم
        call = self.db.session.query(Call).filter(Call.id == call_id).first()
        if not call:
            logging.error(f"Call with id {call_id} does not exist.")
            return {'action': data['action'], 'status': False, 'error': 'Call does not exist'}

        # if client_username := self.db.session.query(Call).filter(Call.id == call_id).first().client.username:             
            # await self.broadcast_to_users([client_username], data={'action': 'call '})
            

        if call.id not in self.consultant_responses:
            logging.info(f"Creating new queue for call_id: {call_id}")
            self.consultant_responses[call.id] = asyncio.Queue()

        await self.consultant_responses[call.id].put(data)

        

        logging.info(f"Received {response} response for call {call_id} from consultant.")
    
        return {'act': data['action'], 'status': True, 'call_id': call.id ,'message': f'Response {response} received for call {call_id}.'}


    async def cancel_call(self, data):
        call_id = int(data['call_id'])
        canceled = data['canceled']
        action = data['action']
        utc_now = datetime.now(timezone.utc)

        call = self.db.session.query(Call).filter(Call.id == int(call_id)).first()
        if not call or call.status == 'cancelled':
            logging.error(f"Call with id {call_id} does not exist.")
            return {'act': action,'status': False, 'error': 'Call does not exist or canceled or confirmed'}
                
        if int(call_id) in self.waiting_tasks:
            task = self.waiting_tasks[int(call_id)]
            call.status = 'cancelled'
            call.end_time = utc_now
            self.db.session.commit()
            task.cancel()
            data = {'act': 'cancelled', 'status': True, 'call_id': call.id, 'message': 'Call canceled successfully'}
            await self.broadcast_to_users(
                [call.client], data=json.dumps(data), send_notification_if_offline=False
            )        
        
        data = {
            "act": "callEnded",
            'call_type': call.call_type,
            'call_id': call.id,
            'call_status': call.status,
            'message_id': call.id,
            'from_user_fullname': call.client.fullname,
            'from_user_avatar': call.client.avatar_url,
            'from_user_username': call.client.username,
            'session_duration': call.consultant.session_duration,
            'status': True,
            "message": "The call ended"                
        }

        if canceled and canceled == "client":
            data['message'] = "The call that was in progress was ended by the user"
        elif canceled and canceled == "consultant":
            data['message'] = "The call that was in progress was ended by the consultant"

        call.status = 'confirmed'
        call.end_time = utc_now
        self.db.session.commit()

        logging.info(f'event acancel_call, call_id: {call.id}, canceled: {canceled}')
        await self.broadcast_to_users(
            [call.consultant, call.client], data=json.dumps(data), send_notification_if_offline=False
        )        


    async def start_room(self, data):
        data = StartRoom(**self.data)
        if type(self.from_user) is Consultant:
            return {
                'act': data.type,
                'status': False,
                'error': 'only regular users can start room'
            }

        obj = self.db.session.query(Room).filter(
            Room.client_id == self.from_user.id,
            Room.consultant_id == data.consultant.id,
        ).order_by(Room.id.desc()).first()
        if not obj:
            print(f'===room=started====')
            obj = Room(
                client=self.from_user,
                consultant=data.consultant,
                status='o',
                room_type=data.room_type,
            )

            self.db.session.add(obj)
            self.db.session.commit()
            self.db.session.refresh(obj)

        # return RoomsResponse(**room.to_dict()).json()


        json_data = RoomsResponse(**obj.to_dict()).json()
        print(f'===room====')
        await self.broadcast_to_users([obj.client], data=json_data, send_notification_if_offline=False)

        # if obj.consultant.fcm:
        #     await self.send_notif(obj.consultant, {
        #         'act': 'call_request',
        #     })

        # if not self.user_connections.get(obj.consultant.username):
            # await self.send_notif(obj.consultant, {
                # 'content': f'room:{obj.id}',
            # })
            # return {
            #     'act': 'startRoom',
            #     'error': 'room created but consultant is offline'
            # }

    async def get_rooms(self, data):
        per_page = data.per_page  # اگر per_page در داده‌ها وجود نداشت، مقدار پیش‌فرض 10 باشد.
        unread_user_type_filter = 'Consultant' if self.from_user.user_type == 'User' else 'User'

        # 1. زیرکوئری برای دریافت آخرین پیام هر اتاق
        subq_latest = self.db.session.query(
            Room.id.label('room_id'),
            func.max(Message.id).label('max_id')
        ).join(Message).group_by(Room.id).subquery()

        # 2. زیرکوئری برای شمارش کل پیام‌ها و پیام‌های خوانده نشده هر اتاق
        subq_counts = self.db.session.query(
            Message.room_id.label('room_id'),
            func.count(Message.id).label('messages_count'),
            func.count(
                case(
                    [
                        (
                            (Message.by_user_type == unread_user_type_filter) & 
                            (Message.has_read == False),
                            1
                        )
                    ],
                    else_=None
                )
            ).label('unread_count')
        ).group_by(Message.room_id).subquery()

        # 3. تعریف alias برای پیام آخر
        latest_message_alias = aliased(Message)


        base_query = self.db.session.query(
            Room,
            subq_counts.c.messages_count,
            subq_counts.c.unread_count,
            latest_message_alias
        ).join(
            subq_latest, Room.id == subq_latest.c.room_id
        ).join(
            latest_message_alias, latest_message_alias.id == subq_latest.c.max_id
        ).outerjoin(
            subq_counts, Room.id == subq_counts.c.room_id
        ).order_by(
            desc(latest_message_alias.at_time)
        )
    
        # فیلتر تاریخ
        date_filter = None
        if data.date == "week":
            date_filter = datetime.now() - timedelta(weeks=1)
        elif data.date == "week2":
            date_filter = datetime.now() - timedelta(weeks=2)

        elif data.date == "month":
            date_filter = datetime.now() - timedelta(days=30)
        elif data.date == "three_months":
            date_filter = datetime.now() - timedelta(days=90)
        elif data.date == "six_months":
            date_filter = datetime.now() - timedelta(days=180)
        elif data.date == "year":
            date_filter = datetime.now() - timedelta(days=365)

        if date_filter:
            base_query = base_query.filter(latest_message_alias.at_time >= date_filter)
        if data.unread_only == True:
            base_query = base_query.filter(subq_counts.c.unread_count > 0)
        if data.not_replied == True:
            base_query = base_query.filter(latest_message_alias.by_user_type != 'Consultant')
        if data.search is not None:
            base_query = base_query.join(Room.client).filter(
                or_(
                    User.username.ilike(f"%{data.search}%"),
                    User.fullname.ilike(f"%{data.search}%")
                )
            )

        base_query = base_query.group_by(
            Room.id, 
            subq_counts.c.messages_count, 
            subq_counts.c.unread_count, 
            latest_message_alias.id
        ).order_by(
            case([(subq_counts.c.unread_count > 0, 0)], else_=1),
            desc(Room.created_at)
        )


        def to_response(rows):
            data = []
            for room, messages_count, unread_count, last_message in rows:
                room_data = room.to_dict()
                # room_data['messages_count'] = messages_count
                # room_data['unread_count'] = unread_count
                data.append(
                    RoomsResponse(
                        **room_data,
                        unread_count=unread_count,
                        messages_count=messages_count,
                    )
                )
            return data

        if type(self.from_user) is Consultant:  
            objects = base_query.filter(
                Room.consultant_id == self.from_user.id,
            )
            count = objects.count()
            paginated_objects = paginate(objects, page=data.page, per_page=per_page)

            return RoomListResponse(
                current_page=data.page,
                total_pages=ceil(count / per_page),
                count=count,
                per_page=per_page, 
                results=to_response(paginated_objects)
            ).json()

        elif type(self.from_user) is Admin:
            if data.consultant:
                base_query = base_query.filter(
                    Room.consultant_id == data.consultant.id,
                )
            count = base_query.count()
            paginated_objects = paginate(base_query, page=data.page, per_page=per_page)
            return RoomListResponse(
                current_page=data.page,
                total_pages=ceil(count / per_page),
                count=count,
                per_page=per_page,
                results=to_response(paginated_objects)).json()
        else:
            objects = self.db.session.query(
                Room,
                func.count(Message.id).label('messages_count'),
                func.count(Message.id).filter(
                    Message.has_read == False,
                    Message.by_user_type == unread_user_type_filter,
                ).label('unread_count'),
            ).outerjoin(Message).filter(
                Room.client_id == self.from_user.id, Room.consultant == data.consultant
            ).group_by(Room.id).order_by(Room.id.desc())
            
            calls = self.db.session.query(Call).filter(
                Call.client_id == self.from_user.id,
                Call.consultant_id == data.consultant.id,
                Call.end_time.isnot(None)  # اضافه کردن شرط برای فیلتر کردن تماس‌های بدون end_time
            ).order_by(desc(Call.start_time)).limit(10).all()
            def to_response_user(rows):
                data = []
                for row, messages_count, unread_count in rows:
                    data.append(
                        RoomsResponse(
                            **row.to_dict(),
                            unread_count=unread_count,
                            messages_count=messages_count,
                        )
                    )
                return data
            room_responses = to_response_user(objects[:1])
            
            for room_response in room_responses:
                recent_calls_dicts = [
                    {
                        "id": call.id,
                        "start_time": str(call.start_time),
                        "end_time": str(call.end_time) if call.end_time else None,
                        "call_type": call.call_type,
                        "cost": call.cost,
                        "timer_active": call.timer_active,
                        "recording_audio_link": None
                    }
                    for call in calls
                ]
                room_response.recent_call = recent_calls_dicts


            return RoomListResponse(
                results=room_responses
            ).json(exclude={'count', 'per_page', 'total_pages', 'current_page'})

    async def get_recent_calls(self, data):
        consultant = data.consultant
        page = data.page
        per_page = data.per_page
        
        offset = (page - 1) * per_page
        calls_query = self.db.session.query(Call).filter(
            Call.consultant_id == data.consultant.id,
            Call.end_time.isnot(None)
        ).order_by(desc(Call.id))
            
        total_calls = calls_query.count()
        total_pages = ceil(total_calls / per_page)
        calls = calls_query.offset(offset).limit(per_page).all()
        response = CallListResponse(
            count=total_calls,
            current_page=page,
            total_pages=total_pages,
            per_page=per_page,
            results=[CallResponse(**call.__dict__) for call in calls]
        ).json()
        
        return response


    async def get_history(self, data):        
        messages = []
        for msg in self.db.session.query(Message).filter(Message.room_id == data.room.id).order_by(
                Message.id.asc()).all():
            msg_dict = msg.to_dict()
            msg_dict[
                "type"] = msg.chat_type if msg.chat_type is not None else "text"  # Set a default value if msg.chat_type is None
            msg_model = MessageModel(
                **msg_dict,
            )
            messages.append(msg_model)

        return HistoryResponse(
            results=messages, count=len(messages), room_id=str(data.room.id),
            status='open' if data.room.status == 'o' else 'closed', reason=data.room.reason,
            room=RoomsResponse(**data.room.to_dict())
        ).json()

    async def stream_started_handler(self, data):
        call = self.db.session.query(Call).filter(Call.id == data['room_id']).first()
        if not call:
            return {'action':'stream_started_handler', 'status': 'error', 'message': 'Call not found'}

        client = self.db.session.query(User).filter(User.id == call.client_id).first()
        consultant = self.db.session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
        if call.timer_active == True:
            return None

        if self.from_user.username == client.username:
            return None
                
        call.start_time = datetime.now()

        call.timer_active = True
        call.cost = 0  # Initialize cost
        self.db.session.commit()
        if not consultant:
            return {'action':'stream_started_handler', 'status': 'error', 'message': 'Consultant not found'}

        cost = consultant.video_call_cost if call.call_type == 'video' else consultant.voice_call_cost
            
        result = await deduct_coins(client, consultant, call, cost, self.db.session, self.broadcast_to_users, self.stream_stopped_handler)
        
        if result['action'] == 'paymentError':
            log_message("----event stream_started_handler" , 'Show', result)        
            return result

        call.cost += cost
        call.status = "confirmed"
        consultant.status = 'busy'
        self.db.session.commit()

        set_timer(call, consultant.session_duration, cost, client, self.db.session, self.broadcast_to_users)
        await self.broadcast_to_users(
            [call.client, call.consultant],
            data=json.dumps({
                'action': 'stream_started_handler',
                'call_id': call.id,
                'status': 'ok',
            })
        )

        # return {'action':'stream_started_handler', 'status': 'ok'}
        

    async def stream_stopped_handler(self, data, db = None):        
        logging.error(f"----event stream_stopped_handler")
        if db:
            session = db
        else: 
            session = self.db.session
            
        call = session.query(Call).filter(Call.id == data['room_id']).first()
        if not call:
            return {'action':'stream_stopped_handler', 'status': 'error', 'message': 'Call not found'}

        call.end_time = datetime.now()
        call.timer_active = False
        session.commit()

        consultant = session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
        consultant.status = 'online'
        session.commit()    


        stop_timer(call)
        return {'action':'stream_stopped_handler', 'status': 'ok'}

    async def pay_for_next_period_handler(self, data):
        logging.error(f"----event pay_for_next_period_handler")
        
        call = self.db.session.query(Call).filter(Call.id == data['room_id']).first()
        if not call:
            return {'action':'pay_for_next_period_handler', 'status': 'error', 'message': 'Call not found'}

        client = self.db.session.query(User).filter(User.id == call.client_id).first()
        consultant = self.db.session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
        if not consultant:
            return {'action':'pay_for_next_period_handler', 'status': 'error', 'message': 'Consultant not found'}

        cost = consultant.video_call_cost if call.call_type == 'video' else consultant.voice_call_cost
        result = await deduct_coins(client, consultant, call, cost, self.db.session, self.broadcast_to_users, self.stream_stopped_handler, next_period=True)

        if result['action'] == 'paymentError':
            log_message("----event pay_for_next_period_handler" , 'Show', result)        
            result['action'] = 'notEnoughCoin'
            return result
        
        
        call.cost += cost
        self.db.session.commit()


        await self.broadcast_to_users(
            [call.client, call.consultant],
            data=json.dumps({
                'action': 'pay_for_next_period_handler',
                'call_id': call.id,
                'status': 'ok',
            })
        )

     
async def credit_balance(client, cost):
    url = "https://habibapp.com/habcoin/inventory/"
    session = requests.Session()
    session.headers = {
        'Content-Type': 'application/json',
        'AUTHORIZATION': f'Token {client.token}',
    }
    try:
        response = session.get(url=url, headers={
            'user-agent': 'dart:io'
        })
        # بررسی وضعیت کد پاسخ
        if response.status_code == 200:
            data = response.json()  # تبدیل پاسخ به JSON
            # بررسی وجود 'coin_balance' در داده‌ها
            if 'coin_balance' in data:
                coin_balance = data['coin_balance']

                # بررسی اینکه آیا موجودی کافی است یا خیر
                print(f'-credit_balance-->{coin_balance}//cost: {cost}')
                if coin_balance >= cost:
                    return True  # موجودی کافی است
                else:
                    print("Insufficient balance.")
                    return False  # موجودی کافی نیست
            else:
                print("coin_balance not found in the response.")
                return False  # موجودی یافت نشد، در نتیجه عملیات متوقف می‌شود
        else:
            print(f"Unexpected status code: {response.status_code}/ res: {response.text}/ token: {client.token}")
            return False  # در صورت پاسخ غیرمنتظره
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return False  # در صورت وقوع خطا    


async def deduct_coins(client, consultant, call, cost, db, broadcast_func, stop_handler, next_period=False):
    url = "https://habibapp.com/habcoin/pay/"
    username = client.username.split(':')[0] if ':' in client.username else client.username
    params = {"username": username,
              "amount": cost,
              "token": "t5yugymks5458fd4ghfg6h6fg",
              "service": "consultants",
              "object_id": consultant.id}

    log_message("----event deduct_coins" , 'Log', params)
    
    encoded_params = urlencode(params)
    admin_token = os.environ.get('admin_token') or 'e9a236c586d4fb90f7f7ce2c70392d80069022d2'
    session = None
    session = requests.Session()
    session.headers = {
        'Content-Type': 'application/json',
        'AUTHORIZATION': f'Token {admin_token}',
    }
    try:
        response = session.get(f"{url}?{encoded_params}", headers={
            'user-agent': 'dart:io'
        })
        response.raise_for_status()
        try:
            result = response.json()
            if result.get("status") == "Coins deducted successfully":
                logging.info("Payment successful")
                notification_data = {
                    'action': 'paymentSuccess',
                    'message': f'Payment successful for client {client.username}.',
                    'call_id': call.id,
                }
                await broadcast_func([call.client, call.consultant], notification_data)
                if next_period:
                    call.cost += cost
                    db.commit()
                return {
                    'action': 'paymentSuccess',
                    'message': f'Payment successful for client {client.username}.',
                    'call_id': call.id,
                }
            else:
                error_message = result.get("error", "Unknown error")
                logging.error(f"Error in payment: {error_message}")
                notification_data = {
                    'action': 'paymentError',
                    'message': f'Error in payment for client {client.username}: {error_message}',
                    'call_id': call.id,
                }
                await broadcast_func([call.client, call.consultant], notification_data)
                if not next_period:
                    await stop_handler({'room_id': call.id})
                return {
                    'action': 'paymentError',
                    'message': f'Error in payment for client {client.username}: {error_message}',
                    'call_id': call.id,
                }
        except requests.exceptions.JSONDecodeError:
            error_message = f"Failed to decode JSON response: {response.text}"
            logging.error(error_message)
            notification_data = {
                'action': 'paymentError',
                'message': f'Error in payment for client {client.username}: Failed to decode JSON response.',
                'call_id': call.id,
            }
            await broadcast_func([call.client, call.consultant], notification_data)
            if not next_period:
                await stop_handler({'room_id': call.id})
            return {
                'action': 'paymentError',
                'message': f'Error in payment for client {client.username}: Failed to decode JSON response.',
                'call_id': call.id,
            }
    except requests.exceptions.RequestException as e:
        error_message = f"Request failed: {e}"
        logging.error(error_message)
        notification_data = {
            'action': 'paymentError',
            'message': f'Error in payment for client {client.username}: Request failed.',
            'call_id': call.id,
        }
        await broadcast_func([call.client, call.consultant], notification_data)
        if not next_period:
            await stop_handler({'room_id': call.id})
        return {
            'action': 'paymentError',
            'message': f'Error in payment for client {client.username}: Request failed.',
            'call_id': call.id,
        }

def set_timer(call, session_duration, cost, client, db, broadcast_func):
    next_deduction_time = datetime.now() + timedelta(minutes=session_duration)

    def deduct_periodic_coins(next_deduction_time):
        current_time = datetime.now()
        if current_time >= next_deduction_time and call.end_time is None and call.timer_active:
            consultant = db.query(Consultant).filter(Consultant.id == call.consultant_id).first()
            period_cost = consultant.video_call_cost if call.call_type == 'video' else consultant.voice_call_cost
            required_cost = period_cost * ((current_time - call.start_time).total_seconds() // (session_duration * 60) + 1)

            if call.cost < required_cost:
                print(f'--->call.cost: {call.cost}/ required_cost: {required_cost}')
                # asyncio.run(ActionHandlerV3(db).stream_stopped_handler({'room_id': call.id}))
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    ActionHandlerV3().stream_stopped_handler(data={'room_id': call.id}, db=db)
                )
                loop.close()    
            else:
                next_deduction_time = current_time + timedelta(minutes=session_duration)
                set_timer(call, session_duration, cost, client, db, broadcast_func)

    threading.Timer(10, lambda: threading.Timer(
        (next_deduction_time - datetime.now()).total_seconds(),
        lambda: deduct_periodic_coins(next_deduction_time)
    ).start()).start()
    
def stop_timer(call):
    call.timer_active = False
    logging.info(f"Timer for call {call.id} stopped.")
