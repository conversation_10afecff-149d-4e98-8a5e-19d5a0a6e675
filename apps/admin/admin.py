import json
from datetime import datetime
from typing import List, Dict

from fastapi import APIRouter
from fastapi import Request
from fastapi.responses import Response
from passlib.context import CryptContext
from sqlalchemy import func, case
from starlette.responses import J<PERSON>NResponse

from apps import APIPermissionException
from apps.admin import router
from models import *
from schemas.admin import category as category_schema
from schemas.admin import consultant as consultantDataModel
from schemas.admin.admin import BanUser
from schemas.admin.report import ReportList
from schemas.request_types import UpdateRoom
from schemas.response_types import RoomsResponse
from utils.charts import consultant_activity_chart, activity_chart, consultant_activity_chart2
from utils.helpers import sqlalchemy_obj_to_dict

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def get_password_hash(password):
    return pwd_context.hash(password)


@router.get("/consultants/expertise-subjects/", )
def consultant_expertise_subjects(request: Request):
    accept_language = request.headers.get("Accept-Language", "en").split(",")[0][:2]  
    return JSONResponse({
        'result': [
            "Clinical Psychology", "Counseling Psychology",
            "Industrial-Organizational Psychology",
            "Developmental Psychology", "Social Psychology"
        ]
    })

@router.get("/consultants/topics/", )
def consultant_expertise_subjects(request: Request):
    accept_language = request.headers.get("Accept-Language", "en").split(",")[0][:2]  
    topics = db.session.query(Topic).all()
    topics_list = []
    for topic in topics:
        translated_title = topic.get_title_for_language(accept_language)
        topics_list.append(
            {
                "id": topic.id,
                "title": translated_title
            }
        )
        
    return JSONResponse({
        'result': topics_list
    })

@router.get("/languages/")
def get_languages_list(request: Request):
    return {
        'en': 'English',
        'fa': 'Persian',
        'ar': 'Arabic',
        'ur': 'Urdu',
        'fr': 'French',
        'id': 'Indonesian',
        'az': 'Azerbaijani',
        'ru': 'Russian',
        'es': 'Spanish',
        'sw': 'Swahili',
        'tr': 'Turkish',
    }


@router.get("/topics/", response_model=consultantDataModel.TopicList)
def get_topic_list(request: Request):
    qs = db.session.query(Topic).all()
    resp = json.dumps([t.title for t in qs])
    return Response(resp, media_type='application/json')


@router.get("/categories/", response_model=category_schema.CategoryList)
def get_categories(request: Request):
    qs = db.session.query(Category).all()

    return Response(
        category_schema.CategoryList(results=[category_schema.BaseCategory(
            **t.to_dict()
        ) for t in qs]).json(),
        media_type='application/json'
    )


@router.post("/categories/", response_model=category_schema.BaseCategory)
def create_category(request: Request, data: category_schema.CategoryCreate):
    qs = db.session.query(Category).all()
    cat_obj = Category(title=data.title)
    db.session.add(cat_obj)
    db.session.commit()
    db.session.refresh(cat_obj)

    resp = json.dumps(sqlalchemy_obj_to_dict(cat_obj))
    return Response(resp, media_type='application/json')


@router.get("/consultants/", response_model=consultantDataModel.ConsultantList)
def get_consultants_list(request: Request):
    qs = db.session.query(Consultant).order_by(Consultant.id.desc()).all()
    js = consultantDataModel.ConsultantList(results=[obj.to_dict() for obj in qs]).json()

    return Response(js, media_type='application/json')


@router.get("/consultants/{username}/", response_model=consultantDataModel.ConsultantInList)
def get_consultant(username: str):
    # Try to find consultant by username
    obj = db.session.query(Consultant).filter(Consultant.username == username).first()
    
    # If not found, try to find by email
    if not obj:
        obj = db.session.query(Consultant).filter(Consultant.email == username).first()
    
    if obj:
        print(f'>>>>>>>>>>>.. {obj}')
        obj_dict = sqlalchemy_obj_to_dict(obj)
        # obj_dict['languages'] = obj_dict['languages'].split(',')
        # obj_dict['topics'] = obj_dict['topics'].split(',')
        # obj_dict['contact_type'] = obj_dict['contact_type'].split(',')

        result = (
            db.session.query(
                func.count(case([(Room.room_type == 'voice', Room.id)]).label('voice_calls')),
                func.count(case([(Room.room_type == 'video', Room.id)]).label('video_calls')),
                func.count(case([(Room.room_type == 'chat', Room.id)]).label('text_chats'))
            )
            .filter(Room.consultant_id == obj.id)  # Use the actual consultant ID
            .first()
        )
        voice_calls, video_calls, text_chats = result

        js = consultantDataModel.ConsultantInList(
            **obj_dict,
            voice_call_count=voice_calls,
            video_call_count=video_calls,
            text_chat_count=text_chats,
        ).dict()
        return js
    return {}, 404


@router.put("/consultants/{username}/update/", response_model=consultantDataModel.Consultant)
async def update_consultant(
        request: Request, username,
        data: consultantDataModel.ConsultantUpdateSchema,
):
    
    accept_language = request.headers.get("Accept-Language", "en").split(",")[0][:2]  
    if request.state.consultant and request.state.consultant.username != username:
        return JSONResponse({
            'error': 'only admins can update other consultants'
        }, status_code=401)
    query_data = data.dict(exclude_none=True)

    obj = db.session.query(Consultant).filter(
        Consultant.username == username
    ).first()

    if query_data.get('password', None):
        query_data['password'] = get_password_hash(query_data['password'])

    categories = []
    for _category in query_data.pop('categories'):
        if _cat_obj := db.session.query(Category).filter(Category.title == _category).first():
            categories.append(_cat_obj)
        else:
            _cat_obj = Category(title=_category)
            db.session.add(_cat_obj)
            db.session.commit()
            db.session.refresh(_cat_obj)
            categories.append(_cat_obj)

    query_data['categories'] = categories


    if query_data.get('topics', None):
        topic_ids = query_data['topics']
        # topic_ids = [int(tid) for tid in query_data['topics'].split(',') if tid.strip().isdigit()]
        topics = db.session.query(Topic).filter(Topic.id.in_(topic_ids)).all()
        existing_topic_ids = [topic.id for topic in topics]
        query_data['topics'] = ','.join(map(str, existing_topic_ids))

    # Convert contact_type from list to comma-separated string
    if 'contact_type' in query_data:
        if isinstance(query_data['contact_type'], list):
            query_data['contact_type'] = ','.join(query_data['contact_type'])
        # Handle empty list case
        if query_data['contact_type'] == [] or query_data['contact_type'] is None:
            query_data['contact_type'] = ""
    


    for key, value in query_data.items():
        setattr(obj, key, value)

    obj.updated_at = datetime.datetime.now()

    db.session.commit()

    consultant = sqlalchemy_obj_to_dict(obj)
    consultant['languages'] = consultant['languages'].split(',') if consultant['languages'] else None 
    consultant['call_languages'] = consultant['call_languages'].split(',') if consultant['call_languages'] else None    
    topics = consultant['topics'].split(',') if consultant['topics'] else None
    consultant['topics'] = [int(topic) for topic in topics] if topics else None
    
    consultant['contact_type'] = consultant['contact_type'].split(',') if consultant['contact_type'] else None
    consultant['categories'] = [i.to_dict() for i in obj.categories]
    return consultant


@router.post("/{username}/change-password/", )
def change_password(request: Request, username, data: Dict):
    password = get_password_hash(data['password'])
    if request.state.admin and request.state.admin.username == username:
        qs = db.session.query(Admin).filter(Admin.username == username).update({'password': password})
        db.session.commit()

    elif request.state.admin or request.state.consultant and request.state.consultant.username == username:
        qs = db.session.query(Consultant).filter(Consultant.username == username).update({'password': password})
        db.session.commit()

    else:
        return JSONResponse({
            'error': f'you dont have permission to change {username} password'
        }, status_code=401)

    if not qs:
        return JSONResponse({
            'status': False,
            'result': 'user not updated'
        }, status_code=400)

    return JSONResponse({
        'status': True,
        'result': 'password updated successfully',
    })


@router.get("/me/", name="get current admin")
def get_me(request: Request):
    if request.state.admin:
        return request.state.admin.to_dict()
    else:
        data = consultantDataModel.ConsultantInList(
            **sqlalchemy_obj_to_dict(request.state.consultant),
        ).dict()
        data['is_admin'] = False
        data['role'] = 'consultant'
        return data


@router.get("/consultants/{username}/rooms/", response_model=List[RoomsResponse])
def get_consultant_rooms(username: str):
    obj = db.session.query(Consultant).filter(Consultant.username == username).first()

    return JSONResponse(
        {
            'open': [
                RoomsResponse(**sqlalchemy_obj_to_dict(o)).dict() for o in
                db.session.query(Room).filter(Room.consultant.id == obj.id, status='o').all()
            ],
            'closed': [
                RoomsResponse(**sqlalchemy_obj_to_dict(o)).dict() for o in
                db.session.query(Room).filter(Room.consultant.id == obj.id, status='c').all()
            ],
        }
    )


@router.get("/reports/")
def get_reports():
    reports = db.session.query(Report).order_by(Report.id.desc()).all()
    return {
        'total': len(reports),
        'results': [
            ReportList(**sqlalchemy_obj_to_dict(obj)).dict() for obj in reports
        ]
    }


@router.delete("/remove-room/{room_id}/")
def remove_room(room_id: str):
    if db.session.query(Room).filter(Room.id == room_id).delete():
        db.session.commit()
        return {
            "status": True,
            "result": f"room {room_id} deleted"
        }
    return {
        "status": False,
        "result": f"room {room_id} not deleted"
    }, 400


@router.put("/update-room/{room_id}/")
def update_room(room_id: str, data: UpdateRoom):
    if db.session.query(Room).filter(Room.id == room_id).update(**data.dict()):
        db.session.commit()
        return {
            "status": True,
            "result": f"room {room_id} updated"
        }

    return {
        "status": False,
        "result": f"room {room_id} not updated"
    }, 400


@router.post("/consultants/", response_model=consultantDataModel.Consultant)
def add_consultant(request: Request, data: consultantDataModel.ConsultantCreateSchema):
    if request.state.admin is None:
        raise APIPermissionException(detail='only admins can add consultants')

    query_data = data.dict(exclude_none=True)
    query_data.pop('is_banned')
    categories = query_data.pop('categories', [])

    query_data['password'] = get_password_hash(query_data['password'])

    for topic in query_data.get('topics', []):
        if not db.session.query(Topic).filter(Topic.title == topic).count():
            t = Topic(title=topic)
            db.session.add(t)
            db.session.commit()

    _categories_to_assign = []
    for _category in categories:
        if _cat_obj := db.session.query(Category).filter(Category.title == _category).first():
            _categories_to_assign.append(_cat_obj)
        else:
            _cat_obj = Category(title=_category)
            db.session.add(_cat_obj)
            db.session.commit()
            db.session.refresh(_cat_obj)
            _categories_to_assign.append(_cat_obj)

    consultant_obj = Consultant(**query_data)

    if _categories_to_assign:
        consultant_obj.categories.extend(_categories_to_assign)

    db.session.add(consultant_obj)
    db.session.commit()
    db.session.refresh(consultant_obj)

    data = sqlalchemy_obj_to_dict(consultant_obj)
    data['password'] = ''
    data['categories'] = [i.to_dict() for i in consultant_obj.categories]
    return data


@router.post("/ban-user/", )
def ban_user(request: Request, data: BanUser):
    if request.state.admin is None:
        return JSONResponse({
            'error': 'only admins can ban users'
        }, status_code=401)

    res = db.session.query(User).filter(User.username == data.username).update({
        'banned_reason': data.reason,
        'banned_at': datetime.now(),
    })
    db.session.commit()
    return {
        'success': bool(res),
    }


@router.get("/current-consultant-stats/", name="Current Consultant Activity Stats")
async def current_consultant_stats(request: Request):
    if request.state.consultant:
    
        consultant = request.state.consultant
    
        consultant_activity_data = consultant_activity_chart2(consultant)
        room_counts = (
            db.session.query(
                func.sum(case([(Room.status == 'o', 1)], else_=0)).label('open_rooms'),
                func.sum(case([(Room.status == 'c', 1)], else_=0)).label('closed_rooms'),
            ).filter(Room.consultant_id == consultant.id)
        ).one()
    
        avg_rate = db.session.query(func.avg(Rate.rate)).filter(Rate.consultant == consultant.id).scalar()
    
        return {
            "consultant_activity": consultant_activity_data,
            'unanswered_count': room_counts[0],
            'answered_count': room_counts[1],
            'your_score': avg_rate or 5.0,
        }
    else:
        {
            "message": "not consultant!"
        }
        


@router.get("/stats/", name="Consultants Activity Stats")
async def stats(request: Request):
    if request.state.admin:
            consultant_activity_data = consultant_activity_chart2(request.state.admin.id)
    else:
        consultant_activity_data = consultant_activity_chart2(request.state.consultant.id)
    
    return {    
        "consultant_activity": consultant_activity_data,
    }

@router.get("/stats/country/", name="total client per Country ")
async def admin_stats_country(request: Request):
    results = db.session.query(User.country, func.count(Room.id).label('room_count'))\
                .join(Room, Room.client_id == User.id)\
                .group_by(User.country)\
                .order_by(func.count(Room.id).desc())\
                .limit(8)\
                .all()
    
    data = {country: count for country, count in results}
    empty_value = data.pop("", 0)
    null_value = data.pop(None, 0)
    
    # تعداد کشورها را بشمارید (در حال حاضر 6 کشور باقی مانده)
    number_of_countries = len(data)
    
    # مقدار خالی و null را بین کشورها تقسیم کنید
    total_value_to_distribute = empty_value + null_value
    share = total_value_to_distribute // number_of_countries
    
    # مقدار هر کشور را به روز رسانی کنید
    for country in data:
        data[country] += share
        
    return data                    

@router.get("/stats/admin/", name="total Activity Stats")
async def admin_stats(request: Request):
    total_rooms = db.session.query(Room).count()
    answered = db.session.query(Room).join(Message).filter(Message.by_user_type == Consultant.__name__).count()
    return {
        "consultants": db.session.query(Consultant).count(),
        "total_questions": total_rooms,
        "total_answered": answered,
        "total_pending_questions": total_rooms - answered,
        "total_open_rooms": db.session.query(Room).filter(Room.status == 'o').count(),
        "total_closed_rooms": db.session.query(Room).filter(Room.status == 'c').count(),
    }

