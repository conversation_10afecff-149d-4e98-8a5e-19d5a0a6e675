import secrets

from fastapi import APIRouter
from starlette.responses import JSONResponse

from schemas.request_types import Login, GoogleLogin

router = APIRouter(
    prefix="/api/login"
)
from models import User, Consultant, db, Admin
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

from cryptography.fernet import Fernet
import base64
import os

SECRET_KEY = b'HASJIdfcWFDGLACLB22XkNZ'  

def get_fernet_key():
    key = base64.urlsafe_b64encode(SECRET_KEY.ljust(32)[:32])
    return key

def encrypt_password(password: str) -> str:
    fernet = Fernet(get_fernet_key())
    encrypted = fernet.encrypt(password.encode())
    return encrypted.decode()

def decrypt_password(encrypted_password: str) -> str:
    fernet = Fernet(get_fernet_key())
    decrypted = fernet.decrypt(encrypted_password.encode())
    return decrypted.decode()


@router.post("/google/")
def google_login(data: GoogleLogin):
    # todo: validate login credential by id

    if consultant := db.session.query(Consultant).filter(Consultant.username == data.email).first():
        user = consultant

    elif user := db.session.query(User).filter(User.username == data.email).first():
        user = user

    else:
        user = User(
            username=data.email,
            fullname=data.name,
            avatar_url=data.picture,
            token=secrets.token_urlsafe(40),
            language_code=data.locale,
        )
        db.session.add(user)
        db.session.commit()
        db.session.refresh(user)

    return JSONResponse({
        "accessToken": user.token,
        "user": {
            "id": str(user.id),
            "displayName": user.fullname,
            "username": user.username,
            "email": user.username,
            "photoURL": user.avatar_url,
            "phoneNumber": "",
            "country": user.country,
            "address": "",
            "state": "",
            "city": user.city,
            "zipCode": "",
            "about": "",
            "role": user.__class__.__name__.lower(),
            "is_admin": False,
            "isPublic": True,
        }
    })


@router.post("/")
def login_(data: Login):
    print(f'-site---login--> {data}')
    obj = db.session.query(Consultant).filter(Consultant.username == data.username).first()
    
    if obj:
        # رمزگشایی رمز ذخیره شده و مقایسه با رمز ورودی
        try:
            decrypted_password = decrypt_password(obj.password)
            if decrypted_password == data.password:
                return JSONResponse({
                    "accessToken": obj.token,
                    "user": {
                        "id": str(obj.id),
                        "displayName": obj.fullname,
                        "username": obj.username,
                        "email": "<EMAIL>",
                        "photoURL": obj.avatar_url,
                        "phoneNumber": obj.phone,
                        "country": obj.country,
                        "address": "",
                        "state": obj.state,
                        "city": obj.city,
                        "zipCode": "",
                        "about": obj.bio,
                        "role": "consultant",
                        "is_admin": False,
                        "isPublic": True,
                    }
                })
        except:
            pass

    obj = db.session.query(Admin).filter(Admin.username == data.username).first()
    if obj:
        try:
            decrypted_password = decrypt_password(obj.password)
            if decrypted_password == data.password:
                return JSONResponse({
                    "accessToken": obj.token,
                    "user": {
                        "id": str(obj.id),
                        "displayName": obj.fullname,
                        "email": obj.username,
                        "username": obj.username,
                        "photoURL": obj.avatar_url,
                        "phoneNumber": obj.phone,
                        "country": "",
                        "address": "",
                        "state": "",
                        "city": "",
                        "zipCode": "",
                        "about": "",
                        "role": "admin",
                        "is_admin": True,
                        "isPublic": True,
                    }
                })
        except:
            pass

    return JSONResponse({
        'error': 'Invalid login credential'
    }, status_code=401)