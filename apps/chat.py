import logging
from urllib.parse import parse_qs

from fastapi import <PERSON>AP<PERSON>
from fastapi_sqlalchemy import db
from starlette.endpoints import WebSocketEndpoint
from starlette.middleware.authentication import AuthenticationMiddleware

import models
from apps.actions import <PERSON>Handler
from apps.actions_v2 import ActionHandlerV2
from apps.actions_v3 import ActionHandlerV3
from middlewares.auth import BasicAuthBackend
from schemas.response_types import ByUser
from schemas.response_types_v2 import ConsultantsResponse
from utils.type_hints import WebSocket

app = FastAPI()

app.add_middleware(AuthenticationMiddleware, backend=BasicAuthBackend())

action_handler = ActionHandler()
action_handler_v2 = ActionHandlerV2()
action_handler_v3 = ActionHandlerV3()

logging.basicConfig(level=logging.INFO)


@app.websocket_route('/ws/', )
class Chat(WebSocketEndpoint):
    encoding = "json"
    room_connections = {}
    user_connections = {}
    admin_connections = {}
    waiting_tasks = {}        

    async def on_connect(self, websocket: WebSocket) -> None:
        try:
            await websocket.accept()

            if websocket.user.is_authenticated:
                username = websocket.user.username

                # Extract platform parameter from query string
                query_params = parse_qs(websocket.url.query)
                platform = query_params.get('platform', ['android'])[0]  # Default to 'android' for backward compatibility

                # Store platform information in the websocket for later use
                websocket.platform = platform

                if username not in self.user_connections:
                    self.user_connections[username] = [websocket]
                else:
                    self.user_connections[username].append(websocket)

                logging.info(f"{websocket.user.user_type}/{username} connected with platform: {platform}")

                user_dict = websocket.user.to_dict()
                user_json = ByUser(**user_dict).json()

                if "version=3" in websocket.url.query:
                    if websocket.user.user_type == "Consultant":
                        user_dict['status'] = websocket.user.status
                        user_json = ConsultantsResponse(**user_dict).json()
                    else:
                        user_json = ByUser(**user_dict).json()

                await websocket.send_text(user_json)
            else:
                await websocket.send_json({"result": False, "error": "invalid user token"})

            logging.info(f'total: {sum(len(conns) for conns in self.user_connections.values())}')
        except Exception as e:
            logging.error(f"error opening connection: {e}", exc_info=True)

    async def on_disconnect(self, websocket: WebSocket, close_code: int) -> None:
        try:
            if websocket.application_state == 2:
                return
            if websocket.user.is_authenticated and websocket.user.username in self.user_connections:
                # if hasattr(websocket.user, 'id') and (call_id := models.Call.find_call_id_by_user(db, websocket.user.id)) and (task := self.waiting_tasks.pop(call_id, None)):
                    # print(f'---->>>>>>>>>>>>>> id{ models.Call.find_call_id_by_user(db, websocket.user.id)} cancel')
                    # task.cancel()

                for i, con in enumerate(self.user_connections[websocket.user.username]):
                    if websocket is con:
                        self.user_connections[websocket.user.username].pop(i)
                        try:
                            if websocket.client_state.value == 1:
                                await websocket.close(close_code)
                            else:
                                logging.info('client already closed')
                        except Exception:
                            pass

            logging.info(f'total: {len(self.user_connections)}')

        except Exception as e:
            logging.error(f"ERROR CLOSING: {e}", exc_info=True)

    async def on_receive(self, websocket: WebSocket, data) -> None:
        if not websocket.user.is_authenticated:
            logging.info(f'user is not authenticated {data}')
            await websocket.send_text("user is not authenticated")
            return
        try:
            with db():
                if "version=3" in websocket.url.query:
                    resp = await action_handler_v3(db, data, websocket, self.room_connections, self.user_connections, self.waiting_tasks)
                elif "version=2" in websocket.url.query:
                    resp = await action_handler_v2(db, data, websocket, self.room_connections, self.user_connections, self.waiting_tasks)
                else:
                    resp = await action_handler(db, data, websocket, self.room_connections, self.user_connections, self.waiting_tasks)

            if resp:
                await websocket.send_text(resp)

        except Exception as e:
            logging.error(f'ERROR data-> {data} | error->{e}', exc_info=True)
