import datetime
import threading
import hashlib
import requests

from fastapi import APIRouter, Request, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Extra, validator
from sqlalchemy.orm import Session
from fastapi_sqlalchemy import db

from models.calls import Call
from models import User, Consultant
from utils.fcm_notification import send_notification

router = APIRouter(
    prefix="/stream/callback"
)

# یک توکن امنیتی برای بررسی
SECURITY_TOKEN = "YOUR_SECURE_TOKEN"

def verify_request(signature, timestamp, nonce):
    # Use the CallbackSecret obtained from the ZEGO Admin Console.
    secret = '4b25b50e4dc719a80b2976acf10cc936'

    temp_arr = [secret, str(timestamp), nonce]
    temp_arr.sort()

    tmp_str = ''.join(temp_arr)
    hashed_str = hashlib.sha1(tmp_str.encode()).hexdigest()

    return hashed_str == signature

class CallbackEventData(BaseModel):
    signature: str = ...
    timestamp: str = ...
    nonce: str = ...
    room_id: str = ...

    @validator('signature', pre=True, always=True)
    def validate(cls, v, values):
        is_valid = verify_request(
            signature=values.get('signature'),
            timestamp=values.get('timestamp'),
            nonce=values.get('nonce'),
        )
        if not is_valid:
            raise ValueError('invalid request')

        return v

    class Config:
        extra = Extra.allow

@router.post("/stream_started/")
def stream_started_handler(request: Request, data: CallbackEventData, db: Session = Depends(db.session)):
    print(data, ' -> stream_started_handler')

    # پیدا کردن تماس بر اساس شناسه تماس
    call = db.query(Call).filter(Call.id == data.room_id).first()

    if not call:
        return JSONResponse({
            'status': 'error',
            'message': 'Call not found'
        })

    # به‌روزرسانی زمان شروع تماس
    call.start_time = datetime.datetime.now()
    db.commit()

    # کم کردن کوین اولیه از کاربر
    client = db.query(User).filter(User.id == call.client_id).first()
    consultant = db.query(Consultant).filter(Consultant.id == call.consultant_id).first()

    if not consultant:
        return JSONResponse({
            'status': 'error',
            'message': 'Consultant not found'
        })

    # تعیین هزینه بر اساس نوع تماس
    if call.call_type == 'video':
        cost = consultant.video_call_cost
    else:
        cost = consultant.voice_call_cost

    deduct = deduct_coins(client.username, cost,consultant)
    if "Error" in deduct:
        print("Error in payment:", deduct)
    else:
        print("Payment successful")

    set_timer(call, consultant.session_duration, cost, client, db, consultant)

    return JSONResponse({
        'status': 'ok'
    })

@router.post("/stream_stopped/")
def stream_stopped_handler(request: Request, data: CallbackEventData, db: Session = Depends(db.session)):
    print(data, ' -> stream_stopped_handler')

    # پیدا کردن تماس بر اساس شناسه تماس
    call = db.query(Call).filter(Call.id == data.room_id).first()

    if not call:
        return JSONResponse({
            'status': 'error',
            'message': 'Call not found'
        })

    # به‌روزرسانی زمان پایان تماس
    call.end_time = datetime.datetime.now()
    db.commit()

    # توقف تایمر
    stop_timer(call)

    return JSONResponse({
        'status': 'ok'
    })

def deduct_coins(username, amount, consultant):
    url = "https://habibapp.com/habcoin/pay/"
    params = {
        {"user_name": username,
         "amount": amount,
         "token": "t5yugymks5458fd4ghfg6h6fg",
         "service": "talk",
         "object_id": consultant.id}
    }

    response = requests.get(url, params=params)

    if response.status_code == 200:
        return "Coins deducted successfully"
    else:
        return f"Error: {response.json()}"

# تابع تنظیم تایمر برای کم کردن کوین به صورت دوره‌ای
def set_timer(call, session_duration, cost, client, db ,consultant):
    next_deduction_time = datetime.datetime.now() + datetime.timedelta(minutes=session_duration)

    def deduct_periodic_coins(next_deduction_time):
        current_time = datetime.datetime.now()
        if current_time >= next_deduction_time and call.end_time is None:
            deduct = deduct_coins(client.username, cost, consultant)
            if "Error" in deduct:
                print("Error in payment:", deduct)
            else:
                print("Payment successful")
            next_deduction_time = current_time + datetime.timedelta(minutes=session_duration)
            set_timer(call, session_duration, cost, client, db, consultant)

    threading.Timer((next_deduction_time - datetime.datetime.now()).total_seconds(), deduct_periodic_coins, [next_deduction_time]).start()

# تابع توقف تایمر
def stop_timer(call):
    call.timer_active = False
    # منطق توقف تایمر

@router.post("/recordedfiles/")
def recordedfiles_handler(request: Request, data: CallbackEventData):
    print(data, ' -> recordedfiles_handler')

    return JSONResponse({
        'status': 'ok'
    })
