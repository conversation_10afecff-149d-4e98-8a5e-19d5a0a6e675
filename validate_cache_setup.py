#!/usr/bin/env python3
"""
Validation script for consultant cache clearing setup.

This script validates that all components are properly set up for cache clearing
without actually clearing any caches.

Usage:
    python validate_cache_setup.py
"""

import asyncio
import sys
import os
import importlib.util

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CacheSetupValidator:
    """Validator for cache clearing setup."""
    
    def __init__(self):
        self.validation_results = {}
    
    def validate_file_exists(self, filepath: str, description: str) -> bool:
        """Validate that a required file exists."""
        try:
            if os.path.exists(filepath):
                logger.info(f"✓ {description}: {filepath}")
                return True
            else:
                logger.error(f"✗ {description} not found: {filepath}")
                return False
        except Exception as e:
            logger.error(f"Error checking {description}: {e}")
            return False
    
    def validate_python_syntax(self, filepath: str, description: str) -> bool:
        """Validate Python file syntax."""
        try:
            spec = importlib.util.spec_from_file_location("module", filepath)
            if spec is None:
                logger.error(f"✗ Cannot load {description}: {filepath}")
                return False
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            logger.info(f"✓ {description} syntax is valid")
            return True
        except Exception as e:
            logger.error(f"✗ {description} syntax error: {e}")
            return False
    
    def validate_redis_client_methods(self) -> bool:
        """Validate that Redis client has required methods."""
        try:
            from utils.redis_client import redis_client
            
            required_methods = ['keys', 'delete_pattern', 'get_json', 'set_json', 'delete', 'exists']
            missing_methods = []
            
            for method in required_methods:
                if not hasattr(redis_client, method):
                    missing_methods.append(method)
            
            if missing_methods:
                logger.error(f"✗ Redis client missing methods: {missing_methods}")
                return False
            else:
                logger.info("✓ Redis client has all required methods")
                return True
                
        except ImportError as e:
            logger.error(f"✗ Cannot import Redis client: {e}")
            return False
        except Exception as e:
            logger.error(f"✗ Error validating Redis client: {e}")
            return False
    
    def validate_cache_cleaner_import(self) -> bool:
        """Validate that cache cleaner can be imported."""
        try:
            from clear_consultant_caches import ProjectCacheCleaner

            # Try to instantiate the class
            cleaner = ProjectCacheCleaner(dry_run=True)

            # Check that it has required methods
            required_methods = ['clear_all_project_caches', 'analyze_cache_usage', 'verify_cache_clearing']
            missing_methods = []

            for method in required_methods:
                if not hasattr(cleaner, method):
                    missing_methods.append(method)

            if missing_methods:
                logger.error(f"✗ ProjectCacheCleaner missing methods: {missing_methods}")
                return False
            else:
                logger.info("✓ ProjectCacheCleaner class is properly configured")
                return True

        except ImportError as e:
            logger.error(f"✗ Cannot import ProjectCacheCleaner: {e}")
            return False
        except Exception as e:
            logger.error(f"✗ Error validating ProjectCacheCleaner: {e}")
            return False
    
    async def validate_redis_connection(self) -> bool:
        """Validate Redis connection (optional - may fail in some environments)."""
        try:
            from utils.redis_client import redis_client
            
            # Try to ping Redis
            if await redis_client.ping():
                logger.info("✓ Redis connection successful")
                return True
            else:
                logger.warning("⚠ Redis connection failed (this may be expected in some environments)")
                return False
                
        except Exception as e:
            logger.warning(f"⚠ Redis connection test failed: {e} (this may be expected in some environments)")
            return False
        finally:
            try:
                from utils.redis_client import redis_client
                await redis_client.close()
            except:
                pass
    
    def validate_cache_patterns(self) -> bool:
        """Validate that cache patterns are correctly defined."""
        try:
            from clear_consultant_caches import ProjectCacheCleaner

            cleaner = ProjectCacheCleaner()
            patterns = cleaner.cache_patterns

            expected_patterns = [
                "consultants:*",
                "consultant:*",
                "stats_info_consultant_*",
                "stats_info_all_*",
                "period_stats_*",
                "*"
            ]

            missing_patterns = []
            for pattern in expected_patterns:
                if pattern not in patterns:
                    missing_patterns.append(pattern)

            if missing_patterns:
                logger.error(f"✗ Missing cache patterns: {missing_patterns}")
                return False
            else:
                logger.info(f"✓ All required cache patterns are defined: {patterns}")
                return True

        except Exception as e:
            logger.error(f"✗ Error validating cache patterns: {e}")
            return False
    
    async def run_validation(self) -> bool:
        """Run complete validation suite."""
        logger.info("Starting cache clearing setup validation...")
        
        validations = [
            ("File Existence - Main Script", lambda: self.validate_file_exists("clear_consultant_caches.py", "Cache clearing script")),
            ("File Existence - Test Script", lambda: self.validate_file_exists("test_cache_clearing.py", "Test script")),
            ("File Existence - Redis Client", lambda: self.validate_file_exists("utils/redis_client.py", "Redis client")),
            ("File Existence - Documentation", lambda: self.validate_file_exists("CONSULTANT_CACHE_CLEARING_README.md", "Documentation")),
            ("Python Syntax - Main Script", lambda: self.validate_python_syntax("clear_consultant_caches.py", "Cache clearing script")),
            ("Python Syntax - Test Script", lambda: self.validate_python_syntax("test_cache_clearing.py", "Test script")),
            ("Redis Client Methods", self.validate_redis_client_methods),
            ("Cache Cleaner Import", self.validate_cache_cleaner_import),
            ("Cache Patterns", self.validate_cache_patterns),
            ("Redis Connection", self.validate_redis_connection),
        ]
        
        passed = 0
        total = len(validations)
        
        for name, validation_func in validations:
            logger.info(f"\n--- Validating: {name} ---")
            try:
                if asyncio.iscoroutinefunction(validation_func):
                    result = await validation_func()
                else:
                    result = validation_func()
                
                self.validation_results[name] = result
                if result:
                    passed += 1
            except Exception as e:
                logger.error(f"Validation '{name}' failed with exception: {e}")
                self.validation_results[name] = False
        
        # Summary
        logger.info(f"\n{'='*50}")
        logger.info("VALIDATION SUMMARY")
        logger.info(f"{'='*50}")
        
        for name, result in self.validation_results.items():
            status = "✓ PASS" if result else "✗ FAIL"
            logger.info(f"{status}: {name}")
        
        logger.info(f"\nOverall: {passed}/{total} validations passed")
        
        if passed == total:
            logger.info("🎉 All validations passed! The cache clearing setup is ready.")
            return True
        elif passed >= total - 1:  # Allow Redis connection to fail
            logger.info("✅ Setup is ready (Redis connection test may fail in some environments)")
            return True
        else:
            logger.error("❌ Some critical validations failed. Please review the issues above.")
            return False


async def main():
    """Main function."""
    validator = CacheSetupValidator()
    
    try:
        success = await validator.run_validation()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"Validation failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
