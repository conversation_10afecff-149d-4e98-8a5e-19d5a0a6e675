import os, requests, json


async def gpt_ask(question, project):
    print("asking ", question)
    response = requests.post(
        'http://188.40.92.124:5007/ask',
        json={
            'question': question,
            'token': 'wedklnwxmwkeldnwekldnwed',
            'project': project,
        },
    )
    if response.status_code == 200:
        return response.json()['response']

    return None


if __name__ == '__main__':
    gpt_ask('Hello', '')
