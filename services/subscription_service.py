import datetime
import os
import logging
import requests
from urllib.parse import urlenco<PERSON>
from fastapi_sqlalchemy import db
from sqlalchemy import and_, or_

from models.subscription import Subscription, UserSubscription, UserConsultantInteraction
from models.user import User
from models.consultant import Consultant
from models.room import Room

# Set up logger
logger = logging.getLogger(__name__)


class SubscriptionService:
    """
    Service class for managing subscriptions and free limits
    """
    
    @staticmethod
    def get_active_subscription(user_id):
        """
        Get user's active subscription
        
        Args:
            user_id: ID of the user
            
        Returns:
            UserSubscription or None: The active subscription if exists, None otherwise
        """
        from sqlalchemy.orm import joinedload

        with db():
            subscription = db.session.query(UserSubscription).options(
                joinedload(UserSubscription.subscription)
            ).filter(
                and_(
                    UserSubscription.user_id == user_id,
                    UserSubscription.is_active == True,
                    UserSubscription.end_date > datetime.datetime.now()
                )
            ).order_by(UserSubscription.end_date.desc()).first()
            
            return subscription
    
    @staticmethod
    def purchase_subscription(user_id, subscription_id):
        """
        Legacy method for purchasing a subscription for the user
        Use process_subscription_purchase instead for new code
        
        Args:
            user_id: ID of the user
            subscription_id: ID of the subscription to purchase
            
        Returns:
            tuple: (bool, str, UserSubscription) - (success, message, subscription)
        """
        # Check if user already has an active subscription
        existing_subscription = SubscriptionService.get_active_subscription(user_id)
        if existing_subscription:
            return False, "User already has an active subscription", None
            
        with db():
            # Get user
            user = db.session.query(User).filter(User.id == user_id).first()
            if not user:
                return False, "User not found", None
                
            # Get subscription
            subscription = db.session.query(Subscription).filter(Subscription.id == subscription_id).first()
            if not subscription:
                return False, "Subscription not found", None
                
            if not subscription.is_active:
                return False, "Subscription is not active", None
            
            # Process payment
            payment_success, payment_message = SubscriptionService.process_subscription_payment(user, subscription)
            if not payment_success:
                return False, payment_message, None
            
            # Create subscription
            now = datetime.datetime.now()
            end_date = now + datetime.timedelta(days=subscription.duration_days)
            user_subscription = UserSubscription(
                user_id=user_id,
                subscription_id=subscription_id,
                start_date=now,
                end_date=end_date,
                is_active=True
            )
            db.session.add(user_subscription)
            db.session.commit()
            
            return True, "Subscription purchased successfully", user_subscription
    
    @staticmethod
    def can_message_consultant(user_id, consultant_id):
        """
        Check if user can send message to consultant
        
        Args:
            user_id: ID of the user
            consultant_id: ID of the consultant
            
        Returns:
            tuple: (bool, str) - (can_message, reason)
        """
        # Check if consultant exists
        with db():
            consultant = db.session.query(Consultant).filter(Consultant.id == consultant_id).first()
            if not consultant:
                return False, "Consultant not found"
                
            # Only apply subscription rules for AI consultants
            if not consultant.is_ai:
                return True, "Not an AI consultant"
        
        # Check if user has active subscription
        active_subscription = SubscriptionService.get_active_subscription(user_id)
        
        with db():
            # Get or create interaction
            interaction = db.session.query(UserConsultantInteraction).filter(
                and_(
                    UserConsultantInteraction.user_id == user_id,
                    UserConsultantInteraction.consultant_id == consultant_id
                )
            ).first()
            
            if not interaction:
                # Create new interaction
                interaction = UserConsultantInteraction(
                    user_id=user_id,
                    consultant_id=consultant_id,
                    consultant=consultant
                )
                db.session.add(interaction)
                db.session.commit()
                
                # Check if within free trial or has subscription
                can_send, reason = interaction.can_send_message(active_subscription)
                return can_send, reason
            
            # Check if interaction allows sending messages
            can_send, reason = interaction.can_send_message(active_subscription)
            return can_send, reason
    
    @staticmethod
    def record_message_sent(user_id, consultant_id):
        """
        Record a message sent from user to consultant
        
        Args:
            user_id: ID of the user
            consultant_id: ID of the consultant
            
        Returns:
            bool: True if message was recorded, False otherwise
        """
        # Check if consultant is AI-enabled
        with db():
            consultant = db.session.query(Consultant).filter(Consultant.id == consultant_id).first()
            if not consultant or not consultant.is_ai:
                return True  # No need to record for non-AI consultants
        
        with db():
            # Get interaction
            interaction = db.session.query(UserConsultantInteraction).filter(
                and_(
                    UserConsultantInteraction.user_id == user_id,
                    UserConsultantInteraction.consultant_id == consultant_id
                )
            ).first()
            
            if not interaction:
                return False
            
            # Increment message count
            interaction.increment_message_count()
            db.session.commit()
            
            # If user has active subscription, increment message count
            active_subscription = SubscriptionService.get_active_subscription(user_id)
            if active_subscription and active_subscription.is_valid():
                active_subscription.increment_message_count()
                db.session.commit()
            
            return True
    
    @staticmethod
    def can_send_message_in_room(room_id):
        """
        Check if client can send message in a room
        
        Args:
            room_id: ID of the room
            
        Returns:
            tuple: (bool, str) - (can_send, reason)
        """
        with db():
            room = db.session.query(Room).filter(Room.id == room_id).first()
            if not room:
                return False, "Room not found"
                
            # If room is closed, user cannot send message
            if room.status != 'o':
                return False, "Room is closed"
                
            # Check if consultant is AI-enabled
            consultant = db.session.query(Consultant).filter(Consultant.id == room.consultant_id).first()
            if not consultant or not consultant.is_ai:
                return True, "Not an AI consultant"  # No restrictions for non-AI consultants
            
            # Check if user can message consultant
            can_message, reason = SubscriptionService.can_message_consultant(room.client_id, room.consultant_id)
            return can_message, reason
    
    @staticmethod
    def set_consultant_free_limits(consultant_id, free_messages_per_period=3, free_limit_period='daily'):
        """
        Set free limits for a consultant
        
        Args:
            consultant_id: ID of the consultant
            free_messages_per_period: Number of messages users can send for free per period (default: 3)
            free_limit_period: Period for free messages: 'daily', 'weekly', 'monthly' (default: 'daily')
            
        Returns:
            tuple: (bool, str, dict) - (success, message, free_limits)
        """
        # Validate period
        if free_limit_period not in ['daily', 'weekly', 'monthly']:
            free_limit_period = 'daily'  # Default to daily if invalid
            
        with db():
            # Check if consultant exists and is AI-enabled
            consultant = db.session.query(Consultant).filter(Consultant.id == consultant_id).first()
            if not consultant:
                return False, "Consultant not found", None
                
            if not consultant.is_ai:
                return False, "Consultant is not AI-enabled", None
            
            # Set free limits
            consultant.free_messages_per_period = free_messages_per_period
            consultant.free_limit_period = free_limit_period
            db.session.commit()
            
            free_limits = {
                'free_messages_per_period': consultant.free_messages_per_period,
                'free_limit_period': consultant.free_limit_period
            }
            
            return True, "Free limits set successfully", free_limits
    
    @staticmethod
    def get_consultant_free_limits(consultant_id):
        """
        Get free limits for a consultant
        
        Args:
            consultant_id: ID of the consultant
            
        Returns:
            dict or None: Dictionary with free limits if consultant exists, None otherwise
        """
        with db():
            consultant = db.session.query(Consultant).filter(Consultant.id == consultant_id).first()
            if not consultant:
                return None
                
            return {
                'free_messages_per_period': consultant.free_messages_per_period,
                'free_limit_period': consultant.free_limit_period
            }
    
    @staticmethod
    def get_user_consultant_interaction(user_id, consultant_id):
        """
        Get interaction between user and consultant
        
        Args:
            user_id: ID of the user
            consultant_id: ID of the consultant
            
        Returns:
            UserConsultantInteraction or None: The interaction if exists, None otherwise
        """
        with db():
            interaction = db.session.query(UserConsultantInteraction).filter(
                and_(
                    UserConsultantInteraction.user_id == user_id,
                    UserConsultantInteraction.consultant_id == consultant_id
                )
            ).first()
            
            return interaction
    
    @staticmethod
    def get_all_subscriptions(active_only=True):
        """
        Get all subscriptions
        
        Args:
            active_only: Only return active subscriptions
            
        Returns:
            list: List of subscriptions
        """
        with db():
            query = db.session.query(Subscription)
            if active_only:
                query = query.filter(Subscription.is_active == True)
                
            subscriptions = query.all()
            return subscriptions
            
    @staticmethod
    def get_subscription_info(user_id):
        """
        Get subscription information for a user
        
        Args:
            user_id: ID of the user
            
        Returns:
            dict: Subscription information including active plan and user subscription details
        """
        # Get all active subscription plans
        subscription_plans = SubscriptionService.get_all_subscriptions(active_only=True)
        
        if not subscription_plans:
            return {
                'status': 'error',
                'code': 'no_subscription_plans',
                'message': 'No active subscription plans found'
            }
        
        # Use the first subscription plan
        subscription_plan = subscription_plans[0]
        
        # Get current user's active subscription if any
        user_subscription = SubscriptionService.get_active_subscription(user_id)
        
        # Calculate days remaining if user has an active subscription
        has_active_subscription = False
        total_days = 0
        days_remaining = 0
        
        if user_subscription:
            has_active_subscription = True
            total_days = user_subscription.subscription.duration_days
            
            # Calculate days remaining
            now = datetime.datetime.now()
            if user_subscription.end_date > now:
                days_remaining = (user_subscription.end_date - now).days
        
        # Return the subscription data
        return {
            'status': 'success',
            'subscription_plan': subscription_plan.to_dict(),
            'user_subscription': {
                'has_active_subscription': has_active_subscription,
                'subscription_duration_days': total_days,
                'subscription_days_left': days_remaining,
            }
        }
            
    @staticmethod
    def process_subscription_payment(user, subscription_plan):
        """
        Process payment for a subscription
        
        Args:
            user: User object
            subscription_plan: Subscription object
            
        Returns:
            tuple: (bool, str) - (success, message)
        """
        # Process payment
        url = "https://habibapp.com/habcoin/pay/"
        username = user.username.split(':')[0] if ':' in user.username else user.username
        params = {
            "username": username,
            "amount": subscription_plan.coins_required,
            "token": "t5yugymks5458fd4ghfg6h6fg",
            "service": "subscription",
            "app_name": "q_and_a",
            "object_id": subscription_plan.id
        }
        
        logger.info(f"Purchase subscription request: {params}")
        
        encoded_params = urlencode(params)
        admin_token = os.environ.get('admin_token') or 'e9a236c586d4fb90f7f7ce2c70392d80069022d2'
        session = requests.Session()
        session.headers = {
            'Content-Type': 'application/json',
            'AUTHORIZATION': f'Token {admin_token}',
        }
        
        try:
            response = session.get(f"{url}?{encoded_params}", headers={
                'user-agent': 'dart:io'
            })
            response.raise_for_status()
            
            try:
                result = response.json()
                if result.get("status") == "Coins deducted successfully":
                    return True, "Payment successful"
                else:
                    error_message = result.get("error", "Unknown error")
                    logger.error(f"Error in subscription payment: {error_message}")
                    return False, f"Payment failed: {error_message}"
            except requests.exceptions.JSONDecodeError:
                error_message = f"Failed to decode JSON response: {response.text}"
                logger.error(error_message)
                return False, "Failed to process payment response"
        except requests.exceptions.RequestException as e:
            error_message = f"Request failed: {e}"
            logger.error(error_message)
            return False, "Payment service unavailable"
            
    @staticmethod
    def process_subscription_purchase(user_id, subscription_id):
        """
        Process a subscription purchase including payment and subscription creation
        
        Args:
            user_id: ID of the user
            subscription_id: ID of the subscription to purchase
            
        Returns:
            dict: Result of the purchase operation with status and message
        """
        with db():
            # Check if user already has an active subscription
            existing_subscription = db.session.query(UserSubscription).filter(
                UserSubscription.user_id == user_id,
                UserSubscription.is_active == True,
                UserSubscription.end_date > datetime.datetime.now()
            ).first()
            
            if existing_subscription:
                return {
                    'status': 'error',
                    'code': 'already_subscribed',
                    'message': 'You already have an active subscription'
                }
                
            # Get the user
            user = db.session.query(User).filter(User.id == user_id).first()
            if not user:
                return {
                    'status': 'error',
                    'code': 'user_not_found',
                    'message': 'User not found'
                }
                
            # Get the subscription plan
            subscription_plan = db.session.query(Subscription).filter(
                Subscription.id == subscription_id,
                Subscription.is_active == True
            ).first()
            
            if not subscription_plan:
                return {
                    'status': 'error',
                    'code': 'invalid_subscription',
                    'message': 'Invalid or inactive subscription plan'
                }
                
            # Process payment
            payment_success, payment_message = SubscriptionService.process_subscription_payment(user, subscription_plan)
            
            if not payment_success:
                return {
                    'status': 'error',
                    'code': 'payment_failed',
                    'message': payment_message
                }
                
            # Create new user subscription
            now = datetime.datetime.now()
            end_date = now + datetime.timedelta(days=subscription_plan.duration_days)
            
            new_subscription = UserSubscription(
                user_id=user_id,
                subscription_id=subscription_plan.id,
                start_date=now,
                end_date=end_date,
                is_active=True
            )
            
            db.session.add(new_subscription)
            db.session.commit()
            
            return {
                'status': 'success',
                'code': 'subscription_activated',
                # 'subscription': new_subscription.to_dict()
            }