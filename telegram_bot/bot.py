#!/usr/bin/env python
import os
import json
import requests
import logging
from datetime import datetime, timedelta, timezone
import time
import sys
import traceback
import re
import pytz
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("telegram_bot.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN = "**********************************************"

# Channel Configuration - Each channel is dedicated to a specific time filter
TELEGRAM_CHANNEL_CONFIG = {
    '24_48': "-1002690568818",
    '48_72': "-1002287284364",
    '72_216': "-1002613695489",
}

# API Configuration
API_BASE_URL = 'http://habibapp.com'
API_TOKEN = "516e059d2a6b31d74b6a9e4c8f98fe4e8413efbc"

# Message Configuration
BUTTON_MESSAGE = "📬 Click to fetch messages"
BUTTON_TEXT = "🔄 Fetch Messages"
MAX_RETRIES = 3
RETRY_BACKOFF = 2

# Message history file to track sent messages for each channel
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
MESSAGE_HISTORY_FILE = os.path.join(SCRIPT_DIR, 'telegram_message_history.json')

# Create a session with retry capability
def create_session():
    session = requests.Session()
    # Handle both old and new versions of requests library
    try:
        # For newer versions of requests
        retry_strategy = Retry(
            total=MAX_RETRIES,
            backoff_factor=RETRY_BACKOFF,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST"]
        )
    except TypeError:
        # For older versions of requests
        retry_strategy = Retry(
            total=MAX_RETRIES,
            backoff_factor=RETRY_BACKOFF,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["GET", "POST"]
        )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

session = create_session()

def load_message_history():
    """
    Load message history from JSON file.
    
    Returns:
        dict: Message history for each channel
    """
    if os.path.exists(MESSAGE_HISTORY_FILE):
        try:
            with open(MESSAGE_HISTORY_FILE, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading message history: {e}")
    
    # Return empty history if file doesn't exist or there's an error
    return {
        '24_48': [],
        '48_72': [],
        '72_216': []
    }

def save_message_history(history):
    """
    Save message history to JSON file.
    
    Args:
        history (dict): Message history for each channel
    """
    try:
        with open(MESSAGE_HISTORY_FILE, 'w') as f:
            json.dump(history, f, indent=2)
        logger.info(f"Message history saved to {MESSAGE_HISTORY_FILE}")
    except Exception as e:
        logger.error(f"Error saving message history: {e}")

def delete_previous_messages(time_filter):
    """
    Delete previous messages sent to the channel.
    
    Args:
        time_filter (str): Time filter to determine which channel to use
    
    Returns:
        bool: True if successful, False otherwise
    """
    # Load message history
    history = load_message_history()
    
    # Get message IDs for this channel
    message_ids = history.get(time_filter, [])
    
    if not message_ids:
        logger.info(f"No previous messages to delete for channel {time_filter}")
        return True
    
    # Get the appropriate channel ID for this time filter
    channel_id = TELEGRAM_CHANNEL_CONFIG.get(time_filter, '')
    if not channel_id:
        logger.error(f"No channel ID configured for time filter: {time_filter}")
        return False
    
    # Delete each message
    success = True
    deleted_count = 0
    
    for message_id in message_ids:
        try:
            url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/deleteMessage'
            data = {
                'chat_id': channel_id,
                'message_id': message_id
            }
            
            response = session.post(url, data=data)
            response.raise_for_status()
            deleted_count += 1
            
            # Small delay to avoid hitting rate limits
            time.sleep(0.1)
        except Exception as e:
            # Don't fail if a message can't be deleted (it might have been deleted already)
            logger.warning(f"Error deleting message {message_id}: {e}")
            success = False
    
    logger.info(f"Deleted {deleted_count}/{len(message_ids)} previous messages for channel {time_filter}")
    
    # Clear the history for this channel
    history[time_filter] = []
    save_message_history(history)
    
    return success

def send_message_with_button(time_filter='24_48'):
    """
    Send a message with an inline button to the appropriate Telegram channel.
    
    Args:
        time_filter (str): Time filter to determine which channel to use
    """
    url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage'
    
    # Get the appropriate channel ID for this time filter
    channel_id = TELEGRAM_CHANNEL_CONFIG.get(time_filter, '')
    if not channel_id:
        logger.error(f"No channel ID configured for time filter: {time_filter}")
        return None
    
    # Create inline keyboard with one button
    keyboard = {
        'inline_keyboard': [
            [
                {
                    'text': BUTTON_TEXT,
                    'callback_data': f'fetch_messages_{time_filter}'
                }
            ]
        ]
    }
    
    # Customize message based on time filter
    time_range_text = {
        '24_48': '24-48 hours',
        '48_72': '48-72 hours',
        '72_216': '72-216 hours'
    }.get(time_filter, '')
    
    message = f"📬 Click to fetch messages from {time_range_text} ago"
    
    data = {
        'chat_id': channel_id,
        'text': message,
        'reply_markup': json.dumps(keyboard),
        'parse_mode': 'HTML'
    }
    
    try:
        response = session.post(url, data=data)
        response.raise_for_status()
        logger.info(f"Message with button sent successfully to channel for {time_filter}")
        
        # We don't track button messages in history because we don't want to delete them
        # when fetching new messages
        
        return response.json()
    except Exception as e:
        logger.error(f"Error sending message with button to channel for {time_filter}: {e}")
        return None

def fetch_messages(time_filter='24_48'):
    """
    Fetch messages from the API.

    Args:
        time_filter (str): Time filter for messages ('24_48', '48_72', or '72_216')

    Returns:
        list: List of messages
    """
    url = f'{API_BASE_URL}/api/talk/user-messages/?time_filter={time_filter}'

    headers = {
        'Authorization': f'Token {API_TOKEN}',
        'Content-Type': 'application/json',
        'user-agent': 'dart:io'
    }

    try:
        response = session.get(url, headers=headers)
        response.raise_for_status()
        response_data = response.json()

        # Extract messages from the 'results' field
        if isinstance(response_data, dict) and 'results' in response_data:
            messages = response_data['results']
            logger.info(f"Fetched {len(messages)} messages with filter {time_filter}")
            return messages
        else:
            logger.warning(f"Unexpected API response format: {response_data}")
            return []
    except Exception as e:
        logger.error(f"Error fetching messages: {e}")
        logger.error(traceback.format_exc())
        return []
    
def escape_markdown_v2(text):
    """Escape special characters for MarkdownV2 format"""
    escape_chars = r'_*[]()~`>#+-=|{}.!'
    return re.sub(
        r'(?<![\\])[' + re.escape(escape_chars) + r']',
        r'\\\g<0>',
        str(text)
    )

def normalize_hashtag(text):
    """Convert text to a format suitable for hashtags"""
    # Remove invalid characters
    cleaned = re.sub(r'[^\wآ-یa-zA-Z0-9_]', '_', str(text))
    # Remove repeated underscores
    cleaned = re.sub(r'_+', '_', cleaned)
    # Remove underscores from beginning and end
    return cleaned.strip('_')

def format_message_for_telegram(message):
    """
    Format a message for Telegram with better formatting using MarkdownV2.

    Args:
        message (dict): Message data

    Returns:
        str: Formatted message text
    """
    # Get message data
    user_fullname = message.get('user_fullname', 'Unknown User')
    language_code = message.get('language_code', 'unknown')
    consultant_fullname = message.get('consultant_fullname', 'Unknown Consultant')
    content = message.get('content', '')
    created_at = message.get('at_time', '')
    room_id = message.get('room_id', 0)
    is_answered = message.get('is_answered', False)

    # Truncate content if it's too long
    if len(content) > 3000:
        content = content[:3000] + "... (message truncated)"

    # Format the date
    try:
        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        # If date has no timezone, convert to UTC
        if dt.tzinfo is None:
            dt = pytz.utc.localize(dt)

        # Convert to desired timezone (e.g., Tehran)
        tehran_tz = pytz.timezone('Asia/Tehran')
        dt = dt.astimezone(tehran_tz)

        formatted_time = dt.strftime("%m/%d/%Y ∙ %H:%M")
    except Exception as e:
        logger.error(f"Error formatting date: {e}")
        formatted_time = created_at

    # Escape all text fields
    user_fullname = escape_markdown_v2(user_fullname) if user_fullname else "Unknown User"
    message_text = escape_markdown_v2(content.strip()) if content else "Empty message"
    consultant_escaped = normalize_hashtag(consultant_fullname) if consultant_fullname else "Unknown"
    language_escaped = normalize_hashtag(language_code) if language_code else "unknown"

    # Create hashtags
    consultant_hashtag = (
        consultant_escaped
        .replace(" ", "_")
        .replace(".", "_")
        .replace("(", "")
        .replace(")", "")
        .replace("?", "")
        .replace("-", "_")
    )

    language_hashtag = (
        language_escaped
        .replace(" ", "_")
        .replace(".", "_")
        .replace("-", "_")
    )

    language_tag = f"\\#{language_hashtag}"
    consultant_fullname_tag = f"\\#{consultant_hashtag}"

    # Create link to conversation
    link_url = f"https://talk\\.habibapp\\.com/dashboard/chat/{room_id}/"

    # Format the message
    formatted_msg = (
        f"{consultant_fullname_tag}\n"
        f"{language_tag}\n"
        f"� *Date:* {formatted_time}\n"
        f"� *Answered:* {'🟢' if is_answered else '🔴'}\n\n"
        f"� *Message:*\n"
        f"*{message_text.strip()}*\n"
        f"🔗 [View Conversation in Talk Panel]({link_url})" 
    )

    # Fix uneven underscores that might break markdown
    if formatted_msg.count('_') % 2 != 0:
        formatted_msg = formatted_msg.replace('_', r'\_')

    logger.debug(f"Formatted Message: {formatted_msg}")
    return formatted_msg


def send_message_to_telegram(text, time_filter='24_48', track=True, use_markdown=False):
    """
    Send a message to the appropriate Telegram channel.

    Args:
        text (str): Message text
        time_filter (str): Time filter to determine which channel to use
        track (bool): Whether to track this message in history
        use_markdown (bool): Whether to use MarkdownV2 formatting instead of HTML

    Returns:
        dict: Response from Telegram API
    """
    url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage'

    # Get the appropriate channel ID for this time filter
    channel_id = TELEGRAM_CHANNEL_CONFIG.get(time_filter, '')
    if not channel_id:
        logger.error(f"No channel ID configured for time filter: {time_filter}")
        return None

    # Determine parse mode based on the use_markdown flag
    parse_mode = 'MarkdownV2' if use_markdown else 'HTML'

    data = {
        'chat_id': channel_id,
        'text': text,
        'parse_mode': parse_mode,
        'disable_web_page_preview': True
    }

    try:
        response = session.post(url, data=data)
        response.raise_for_status()
        result = response.json()
        logger.info(f"Message sent successfully to channel for {time_filter}")

        # Track this message in history if requested
        if track and result.get('ok') and 'result' in result:
            message_id = result['result']['message_id']
            history = load_message_history()
            if time_filter not in history:
                history[time_filter] = []
            history[time_filter].append(message_id)
            save_message_history(history)
            logger.debug(f"Message {message_id} added to history for channel {time_filter}")

        return result
    except Exception as e:
        logger.error(f"Error sending message to channel for {time_filter}: {e}")
        logger.error(traceback.format_exc())
        logger.error(f"Failed message content: {text}")
        return None

def process_callback_query(callback_query):
    """
    Process a callback query from Telegram.

    Args:
        callback_query (dict): Callback query data
    """
    logger.info(f"Processing callback query: {json.dumps(callback_query)}")

    callback_data = callback_query.get('data', '')
    logger.info(f"Callback data: {callback_data}")

    # Check if this is a fetch messages request
    if callback_data.startswith('fetch_messages_'):
        logger.info(f"This is a fetch messages request with data: {callback_data}")

        # Extract the time filter from the callback data
        time_filter = callback_data.replace('fetch_messages_', '')
        logger.info(f"Extracted time filter: {time_filter}")

        if time_filter not in ['24_48', '48_72', '72_216']:
            logger.error(f"Invalid time filter in callback data: {callback_data}")
            return

        logger.info(f"Valid time filter: {time_filter}")

        # Answer the callback query
        logger.info(f"Answering callback query with ID: {callback_query['id']}")
        answer_callback_query(callback_query['id'])
        
        # Send a status message to the appropriate channel (don't track this in history)
        status_message = send_message_to_telegram(
            "🔍 <b>Fetching messages...</b>", 
            time_filter,
            track=False
        )
        
        try:
            # Delete previous messages
            logger.info(f"Deleting previous messages for channel {time_filter}")
            delete_previous_messages(time_filter)
            
            # Update status message
            if status_message:
                edit_message(
                    status_message['result']['message_id'],
                    "🗑️ <b>Previous messages deleted.</b>\n\nFetching new messages...",
                    time_filter
                )
            
            # Fetch messages for this specific time filter
            messages = fetch_messages(time_filter)
            
            print(f'---messages--------------> {type(messages)}/{len(messages)}')
            # Update status message
            if status_message:
                edit_message(
                    status_message['result']['message_id'],
                    f"📊 <b>Found {len(messages)} messages</b>\n\nStarting to post them...",
                    time_filter
                )
            
            # Send each message to the appropriate Telegram channel
            for i, message in enumerate(messages):
                formatted_message = format_message_for_telegram(message)
                send_message_to_telegram(formatted_message, time_filter, track=True, use_markdown=True)
                
                # Update progress every 5 messages
                if (i + 1) % 5 == 0 and status_message:
                    progress = f"📊 <b>Progress:</b> {i + 1}/{len(messages)} messages posted"
                    edit_message(status_message['result']['message_id'], progress, time_filter)
                
                time.sleep(1)  # Avoid hitting rate limits
            
            # Final status update
            if status_message:
                edit_message(
                    status_message['result']['message_id'],
                    f"✅ <b>Completed!</b>\n\nPosted {len(messages)} messages.",
                    time_filter
                )
            
            # Send the button message again to the appropriate channel (don't track this in history)
            send_message_with_button(time_filter)
            
        except Exception as e:
            logger.error(f"Error processing messages for {time_filter}: {e}")
            logger.error(traceback.format_exc())
            
            # Send error message to the appropriate channel
            if status_message:
                edit_message(
                    status_message['result']['message_id'],
                    f"❌ <b>Error:</b> Failed to process messages.\n\n<code>{str(e)}</code>",
                    time_filter
                )
            
            # Send the button message again to the appropriate channel
            send_message_with_button(time_filter)

def edit_message(message_id, text, time_filter='24_48'):
    """
    Edit a message in the appropriate Telegram channel.
    
    Args:
        message_id (int): Message ID
        text (str): New message text
        time_filter (str): Time filter to determine which channel to use
    """
    url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/editMessageText'
    
    # Get the appropriate channel ID for this time filter
    channel_id = TELEGRAM_CHANNEL_CONFIG.get(time_filter, '')
    if not channel_id:
        logger.error(f"No channel ID configured for time filter: {time_filter}")
        return None
    
    data = {
        'chat_id': channel_id,
        'message_id': message_id,
        'text': text,
        'parse_mode': 'HTML'
    }
    
    try:
        response = session.post(url, data=data)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.error(f"Error editing message in channel for {time_filter}: {e}")
        return None

def answer_callback_query(callback_query_id):
    """
    Answer a callback query.

    Args:
        callback_query_id (str): Callback query ID
    """
    url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/answerCallbackQuery'

    data = {
        'callback_query_id': callback_query_id,
        'text': 'Fetching messages...'
    }

    logger.info(f"Sending answerCallbackQuery request to URL: {url}")
    logger.info(f"With data: {data}")

    try:
        response = session.post(url, data=data)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response content: {response.text}")

        response.raise_for_status()
        result = response.json()
        logger.info(f"Callback query answered successfully: {result}")
        return result
    except Exception as e:
        logger.error(f"Error answering callback query: {e}")
        logger.error(traceback.format_exc())
        return None

def fetch_and_post_messages_for_channel(time_filter):
    """
    Fetch and post messages for a specific channel.
    
    Args:
        time_filter (str): Time filter to determine which channel to use
    """
    logger.info(f"Automatically fetching messages for time filter: {time_filter}")
    
    # Send a status message to the appropriate channel (don't track in history)
    status_message = send_message_to_telegram(
        "🔄 <b>Scheduled message fetch started...</b>", 
        time_filter,
        track=False
    )
    
    try:
        # Delete previous messages
        logger.info(f"Deleting previous messages for channel {time_filter}")
        delete_previous_messages(time_filter)
        
        # Update status message
        if status_message:
            edit_message(
                status_message['result']['message_id'],
                "🗑️ <b>Previous messages deleted.</b>\n\nFetching new messages...",
                time_filter
            )
        
        # Fetch messages for this specific time filter
        messages = fetch_messages(time_filter)
        
        
        # Update status message
        if status_message:
            edit_message(
                status_message['result']['message_id'],
                f"📊 <b>Found {len(messages)}  messages</b>\n\nStarting to post them...",
                time_filter
            )
        
        # Send each message to the appropriate Telegram channel
        for i, message in enumerate(messages):
            formatted_message = format_message_for_telegram(message)
            send_message_to_telegram(formatted_message, time_filter, track=True, use_markdown=True)
            
            # Update progress every 5 messages
            if (i + 1) % 5 == 0 and status_message:
                progress = f"📊 <b>Progress:</b> {i + 1}/{len(messages)} messages posted"
                edit_message(status_message['result']['message_id'], progress, time_filter)
            
            time.sleep(1)  # Avoid hitting rate limits
        
        # Final status update
        if status_message:
            edit_message(
                status_message['result']['message_id'],
                f"✅ <b>Scheduled task completed!</b>\n\nPosted {len(messages)} messages.",
                time_filter
            )
        
        # Send the button message again to the appropriate channel
        send_message_with_button(time_filter)
        
    except Exception as e:
        logger.error(f"Error in scheduled task for {time_filter}: {e}")
        logger.error(traceback.format_exc())
        
        # Send error message to the appropriate channel
        if status_message:
            edit_message(
                status_message['result']['message_id'],
                f"❌ <b>Error in scheduled task:</b> Failed to process messages.\n\n<code>{str(e)}</code>",
                time_filter
            )
        
        # Send the button message again to the appropriate channel
        send_message_with_button(time_filter)

def start_polling():
    """
    Start polling for updates from Telegram.
    """
    url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getUpdates'
    offset = None
    
    logger.info("Starting to poll for updates")
    
    # Send initial message with button to all channels
    for time_filter in TELEGRAM_CHANNEL_CONFIG.keys():
        if TELEGRAM_CHANNEL_CONFIG[time_filter]:
            send_message_with_button(time_filter)
    
    while True:
        try:
            # Poll for updates - explicitly request callback_query updates
            params = {
                'timeout': 30,
                'allowed_updates': json.dumps(['callback_query'])
            }
            if offset:
                params['offset'] = offset

            # logger.info(f"Polling for updates with params: {params}")
            response = session.get(url, params=params)
            response.raise_for_status()
            result = response.json()
            # logger.info(f"Full response: {json.dumps(result)}")

            if not result.get('ok', False):
                logger.error(f"Error in Telegram API response: {result}")
                time.sleep(5)
                continue

            updates = result.get('result', [])
            logger.info(f"Received {len(updates)} updates from Telegram")

            for update in updates:
                # Log the update for debugging
                logger.info(f"Processing update: {json.dumps(update)}")

                # Update offset to acknowledge the update
                offset = update['update_id'] + 1

                # Process callback queries
                if 'callback_query' in update:
                    logger.info(f"Found callback_query in update: {update['callback_query']}")
                    try:
                        process_callback_query(update['callback_query'])
                    except Exception as e:
                        logger.error(f"Error processing callback_query: {e}")
                        logger.error(traceback.format_exc())
                else:
                    logger.info(f"No callback_query in this update. Update keys: {update.keys()}")

            time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
            break
        except Exception as e:
            logger.error(f"Error polling for updates: {e}")
            logger.error(traceback.format_exc())
            time.sleep(5)  # Wait before retrying

def test_bot_connection():
    """
    Test the connection to the Telegram Bot API.
    """
    url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getMe'

    logger.info(f"Testing bot connection to Telegram API...")

    try:
        response = session.get(url)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response content: {response.text}")

        response.raise_for_status()
        result = response.json()

        if result.get('ok'):
            bot_info = result.get('result', {})
            logger.info(f"Bot connection successful! Bot info: {bot_info}")
            logger.info(f"Bot username: @{bot_info.get('username')}")
            return True
        else:
            logger.error(f"Bot connection failed: {result}")
            return False
    except Exception as e:
        logger.error(f"Error testing bot connection: {e}")
        logger.error(traceback.format_exc())
        return False

def check_webhook_status():
    """
    Check if a webhook is currently set for the bot.
    """
    url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getWebhookInfo'

    logger.info(f"Checking webhook status...")

    try:
        response = session.get(url)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response content: {response.text}")

        response.raise_for_status()
        result = response.json()

        if result.get('ok'):
            webhook_info = result.get('result', {})
            if webhook_info.get('url'):
                logger.warning(f"Webhook is currently set to: {webhook_info.get('url')}")
                logger.warning("This may interfere with polling. Consider removing the webhook if using polling.")
                return webhook_info
            else:
                logger.info("No webhook is currently set. Good for polling.")
                return None
        else:
            logger.error(f"Failed to check webhook status: {result}")
            return None
    except Exception as e:
        logger.error(f"Error checking webhook status: {e}")
        logger.error(traceback.format_exc())
        return None

def remove_webhook():
    """
    Remove any webhook currently set for the bot.
    """
    url = f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/deleteWebhook'

    logger.info(f"Removing webhook...")

    try:
        response = session.get(url)
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response content: {response.text}")

        response.raise_for_status()
        result = response.json()

        if result.get('ok'):
            logger.info("Webhook successfully removed.")
            return True
        else:
            logger.error(f"Failed to remove webhook: {result}")
            return False
    except Exception as e:
        logger.error(f"Error removing webhook: {e}")
        logger.error(traceback.format_exc())
        return False

if __name__ == '__main__':
    # Check if token is set
    if not TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN environment variable is not set")
        sys.exit(1)

    # Check if at least one channel ID is set
    if not any(TELEGRAM_CHANNEL_CONFIG.values()):
        logger.error("No channel IDs are set. At least one of TELEGRAM_CHANNEL_ID_24_48, TELEGRAM_CHANNEL_ID_48_72, or TELEGRAM_CHANNEL_ID_72_216 must be set")
        sys.exit(1)

    # Check which channels are configured
    for time_filter, channel_id in TELEGRAM_CHANNEL_CONFIG.items():
        if channel_id:
            logger.info(f"Channel for {time_filter} is configured with ID: {channel_id}")
        else:
            logger.warning(f"No channel configured for {time_filter}")

    if not API_TOKEN:
        logger.error("API_TOKEN environment variable is not set")
        sys.exit(1)

    try:
        # Test bot connection
        if not test_bot_connection():
            logger.error("Bot connection test failed. Please check your bot token.")
            sys.exit(1)

        # Check webhook status
        webhook_info = check_webhook_status()
        if webhook_info and webhook_info.get('url'):
            logger.warning("A webhook is currently set. This will prevent polling from working.")
            logger.info("Removing webhook to enable polling...")
            if not remove_webhook():
                logger.error("Failed to remove webhook. Polling may not work correctly.")

        # Start polling
        logger.info("Starting polling for updates...")
        start_polling()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)